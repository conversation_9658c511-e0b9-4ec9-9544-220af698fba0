<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtGui">
  <load-typesystem name="typesystem_core.xml" generate="no"/>
  <load-typesystem name="common.xml" generate="no"/>
  <load-typesystem name="core_common.xml" generate="no"/>
  <load-typesystem name="gui_common.xml" generate="no"/>

  <rejection class="^Q.*$" argument-type="^QPlatform.*$"/>
  <function signature="qAlpha(uint)"/>
  <function signature="qBlue(uint)"/>
  <function signature="qGray(int,int,int)"/>
  <function signature="qGray(uint)"/>
  <function signature="qGreen(uint)"/>
  <function signature="qIsGray(uint)"/>
  <function signature="qRed(uint)"/>
  <function signature="qRgb(int,int,int)"/>
  <function signature="qRgba(int,int,int,int)"/>
  <!-- qFuzzyCompare() are declared as friend functions of the respective classes only. -->
  <declare-function signature="qFuzzyCompare(QTransform,QTransform)" return-type="bool"/>
  <function signature="qFuzzyCompare(QTransform,QTransform)"/>
  <declare-function signature="qFuzzyCompare(QQuaternion,QQuaternion)" return-type="bool"/>
  <function signature="qFuzzyCompare(QQuaternion,QQuaternion)"/>
  <declare-function signature="qFuzzyCompare(QMatrix4x4,QMatrix4x4)" return-type="bool"/>
  <function signature="qFuzzyCompare(QMatrix4x4,QMatrix4x4)"/>
  <declare-function signature="qFuzzyCompare(QVector2D,QVector2D)" return-type="bool"/>
  <function signature="qFuzzyCompare(QVector2D,QVector2D)"/>
  <declare-function signature="qFuzzyCompare(QVector3D,QVector3D)" return-type="bool"/>
  <function signature="qFuzzyCompare(QVector3D,QVector3D)"/>
  <declare-function signature="qFuzzyCompare(QVector4D,QVector4D)" return-type="bool"/>
  <function signature="qFuzzyCompare(QVector4D,QVector4D)"/>
  <function signature="qPixelFormatRgba(uchar,uchar,uchar,uchar,QPixelFormat::AlphaUsage,QPixelFormat::AlphaPosition,QPixelFormat::AlphaPremultiplied,QPixelFormat::TypeInterpretation)"/>
  <function signature="qPixelFormatGrayscale(uchar,QPixelFormat::TypeInterpretation)"/>
  <function signature="qPixelFormatAlpha(uchar,QPixelFormat::TypeInterpretation)"/>
  <function signature="qPixelFormatCmyk(uchar,uchar,QPixelFormat::AlphaUsage,QPixelFormat::AlphaPosition,QPixelFormat::TypeInterpretation)"/>
  <function signature="qPixelFormatHsl(uchar,uchar,QPixelFormat::AlphaUsage,QPixelFormat::AlphaPosition,QPixelFormat::TypeInterpretation)"/>
  <function signature="qPixelFormatHsv(uchar,uchar,QPixelFormat::AlphaUsage,QPixelFormat::AlphaPosition,QPixelFormat::TypeInterpretation)"/>
  <function signature="qPixelFormatYuv(QPixelFormat::YUVLayout,uchar,QPixelFormat::AlphaUsage,QPixelFormat::AlphaPosition,QPixelFormat::AlphaPremultiplied,QPixelFormat::TypeInterpretation,QPixelFormat::ByteOrder)"/>
  <function signature="qt_set_sequence_auto_mnemonic(bool)"/>

  <rejection class="^QOpenGL.*$" argument-type="^const GLboolean ?\*$"/>
  <rejection class="^QOpenGL.*$" argument-type="^GLchar\*$"/>
  <rejection class="^QOpenGL.*$" argument-type="^(const )?GLchar ?\*(const)?\*$"/>
  <rejection class="^QOpenGL.*$" argument-type="^char\*$"/>
  <rejection class="^QOpenGL.*$" argument-type="^(const )?char ?\*\*$"/>
  <rejection class="^QOpenGL.*$" argument-type="GLintptr"/>
  <rejection class="^QOpenGL.*$" argument-type="GLsizeiptr"/>
  <rejection class="^QOpenGL.*$" argument-type="^GLu?int64 ?\*$"/>
  <rejection class="^QOpenGL.*$" argument-type="^GLenum ?\*$"/> <!-- glGetProgramBinary -->
  <rejection class="^QOpenGL.*$" argument-type="GLsync"/>
  <rejection class="^QOpenGL.*$" argument-type="^GLubyte( const)?\*$"/>
  <rejection class="^QOpenGL.*$" argument-type="^(const )?QMatrix.x. ?\*$"/>
  <rejection class="^QOpenGL.*$" argument-type="qopengl_GLintptr"/>
  <rejection class="^QOpenGL.*$" argument-type="qopengl_GLsizeiptr"/>
  <rejection class="^QOpenGL.*$" argument-type="QOpenGLTextureHelper*"/>
  <rejection class="^QOpenGL.*$" argument-type="^(const )?QVector.D ?\*$"/>
  <rejection class="^QOpenGL.*$" argument-type="^(const )?void ?\*\*$"/>

  <!--
    Event classes have a lot of non-documented protected fields, those fields
    are removed from PySide because they are Qt implementation details,
    besides the fact they are accessible by ordinary event methods.
    -->

  <rejection class="^Q.*Event$" field-name="^m_.*$"/>

  <rejection class="QFont" enum-name="ResolveProperties"/>
  <rejection class="QPolygon" function-name="putPoints"/>
  <rejection class="QPolygon" function-name="setPoints"/>
  <rejection class="QPolygon" function-name="setPoint"/>
  <rejection class="QPolygon" function-name="point"/>
  <rejection class="QPaintEngine" function-name="fix_neg_rect"/>

  <inject-code class="native" position="beginning" file="../glue/qtgui.cpp"
               snippet="gui-declarations"/>

  <!-- For Qt::mightBeRichText(QString), Qt::convertFromPlainText(QString,Qt::WhiteSpaceMode)
       Match on files from '/QtGui/' (shadow build) or '/gui/' (developer build)
       or '/QtGui.framework' (macOS) -->
  <namespace-type name="Qt" files="^.*/(gui|QtGui)[/.].*\.h$" extends="PySide6.QtCore"
                  doc-file="qt-sub-qtgui"/>

  <primitive-type name="WId" target-lang-api-name="PyLong">
    <conversion-rule>
      <native-to-target file="../glue/qtgui.cpp" snippet="return-pylong-voidptr"/>
      <target-to-native>
        <add-conversion type="PyLong" file="../glue/qtgui.cpp" snippet="conversion-pylong"/>
      </target-to-native>
    </conversion-rule>
  </primitive-type>

  <object-type name="QAbstractFileIconProvider">
      <enum-type name="IconType"/>
      <enum-type name="Option" flags="Options"/>
  </object-type>

  <value-type name="QAccessible">
      <extra-includes>
        <include file-name="QtGui/QAccessibleInterface" location="global"/>
      </extra-includes>
      <inject-code class="native" position="beginning"
                   file="../glue/qtgui.cpp"
                   snippet="qaccessible-pysidefactory"/>
      <value-type name="State"/>
      <enum-type name="AnnouncementPoliteness" since="6.8"/>
      <enum-type name="Event"/>
      <enum-type name="Role"/>
      <enum-type name="Text"/>
      <enum-type name="RelationFlag" flags="Relation"/>
      <enum-type name="InterfaceType"/>
      <enum-type name="TextBoundaryType"/>
      <enum-type name="Attribute" since="6.8"/>
      <add-function signature="installFactory(PyCallable)" static="yes">
          <inject-code class="target" position="beginning"
                       file="../glue/qtgui.cpp"
                       snippet="qaccessible-installfactory"/>
      </add-function>
  </value-type>

  <object-type name="QAccessibleActionInterface"/>
  <object-type name="QAccessibleAttributesInterface" since="6.8"/>
  <object-type name="QAccessibleEditableTextInterface"/>
  <object-type name="QAccessibleInterface"/>
  <object-type name="QAccessibleObject" qt-register-metatype="base"/>
  <object-type name="QAccessibleSelectionInterface" since="6.7"/>
  <object-type name="QAccessibleTableCellInterface"/>
  <object-type name="QAccessibleTextInterface"/>
  <object-type name="QAccessibleValueInterface"/>

  <object-type name="QAccessibleEvent"
               polymorphic-id-expression="%B-&gt;type() == QAccessible::InvalidEvent"/>
  <object-type name="QAccessibleAnnouncementEvent"
               polymorphic-id-expression="%B-&gt;type() == QAccessible::Announcement"/>
  <object-type name="QAccessibleStateChangeEvent"
               polymorphic-id-expression="%B-&gt;type() == QAccessible::StateChanged"/>
  <object-type name="QAccessibleTableModelChangeEvent"
                    polymorphic-id-expression="%B-&gt;type() == QAccessible::TableModelChanged">
      <enum-type name="ModelChangeType"/>
  </object-type>
  <object-type name="QAccessibleTextCursorEvent"
               polymorphic-id-expression="%B-&gt;type() == QAccessible::TextCaretMoved"/>
  <object-type name="QAccessibleTextInsertEvent"
               polymorphic-id-expression="%B-&gt;type() == QAccessible::TextInserted"/>
  <object-type name="QAccessibleTextRemoveEvent"
               polymorphic-id-expression="%B-&gt;type() == QAccessible::TextRemoved"/>
  <object-type name="QAccessibleTextSelectionEvent"
               polymorphic-id-expression="%B-&gt;type() == QAccessible::TextSelectionChanged"/>
  <object-type name="QAccessibleTextUpdateEvent"
               polymorphic-id-expression="%B-&gt;type() == QAccessible::TextUpdated"/>
  <object-type name="QAccessibleValueChangeEvent"
               polymorphic-id-expression="%B-&gt;type() == QAccessible::ValueChanged"/>

  <object-type name="QAction">
    <enum-type name="ActionEvent"/>
    <enum-type name="MenuRole"/>
    <enum-type name="Priority"/>
    <!-- PYSIDE-1627 QAction::menu()/setMenu() are templates -->
    <add-function signature="menu()const" return-type="QObject*">
        <inject-code file="../glue/qtgui.cpp" snippet="qaction-menu"/>
    </add-function>
    <declare-function signature="setMenu(QObject*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </declare-function>
    <modify-function signature="triggered(bool)" allow-thread="yes"/>
    <add-function signature="setShortcut(Qt::Key)">
        <inject-code file="../glue/qtgui.cpp" snippet="set-qtkey-shortcut"/>
    </add-function>
  </object-type>
  <object-type name="QActionGroup">
    <enum-type name="ExclusionPolicy"/>
    <modify-function signature="addAction(QAction*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="removeAction(QAction*)">
      <modify-argument index="1">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QShortcut">
    <add-function signature="QShortcut(QKeySequence&amp;@key@,QObject*@parent@,PyCallable*@callable@,Qt::ShortcutContext@context@=Qt::WindowShortcut)">
      <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qshortcut-1"/>
      <inject-code class="target" position="end" file="../glue/qtgui.cpp" snippet="qshortcut-2"/>
    </add-function>
    <add-function signature="QShortcut(QKeySequence::StandardKey@standard_key@,QObject*@parent@,PyCallable*@callable@,Qt::ShortcutContext@context@=Qt::WindowShortcut)">
      <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qshortcut-1"/>
      <inject-code class="target" position="end" file="../glue/qtgui.cpp" snippet="qshortcut-2"/>
    </add-function>
  </object-type>

  <value-type name="QTransform">
    <enum-type name="TransformationType"/>
    <add-function signature="__repr__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="repr_code">
               <replace from="%REPR_FORMAT" to="%f, %f, %f, %f, %f, %f, %f, %f, %f"/>
               <replace from="%REPR_ARGS"
                        to="%CPPSELF.m11(), %CPPSELF.m12(), %CPPSELF.m13(), %CPPSELF.m21(), %CPPSELF.m22(), %CPPSELF.m23(), %CPPSELF.m31(), %CPPSELF.m32(), %CPPSELF.m33()"/>
             </insert-template>
        </inject-code>
    </add-function>

    <add-function signature="__reduce__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="reduce_code">
              <replace from="%REDUCE_FORMAT" to="ddddddddd"/>
              <replace from="%REDUCE_ARGS"
                       to="%CPPSELF.m11(), %CPPSELF.m12(), %CPPSELF.m13(), %CPPSELF.m21(), %CPPSELF.m22(), %CPPSELF.m23(), %CPPSELF.m31(), %CPPSELF.m32(), %CPPSELF.m33()"/>
            </insert-template>
        </inject-code>
    </add-function>

    <modify-function signature="map(qreal,qreal,qreal*,qreal*)const">
        <modify-argument index="0">
            <replace-type modified-type="PyObject"/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="4">
            <remove-argument/>
        </modify-argument>
        <inject-code class="target" position="beginning">
            <insert-template name="fix_args,number*,number*">
                <replace from="$TYPE" to="qreal"/>
            </insert-template>
        </inject-code>
    </modify-function>
    <!-- ### This is just an overload to "map(qreal,qreal,qreal*,qreal*)const" and can be discarded in Python -->
    <modify-function signature="map(int,int,int*,int*)const" remove="all"/>
    <!-- ### -->

    <modify-function signature="inverted(bool*)const">
      <modify-argument index="1">
        <remove-argument/>
      </modify-argument>
      <modify-argument index="return" pyi-type="Tuple[PySide6.QtGui.QTransform, bool]">
        <replace-type modified-type="PyTuple"/>
      </modify-argument>
      <inject-code class="target" position="beginning">
        <insert-template name="fix_bool*"/>
      </inject-code>
    </modify-function>
    <add-function signature="quadToQuad(QPolygonF&amp;@one@,QPolygonF&amp;@two@)" return-type="PyObject*" static="true">
      <inject-code file="../glue/qtgui.cpp" snippet="qtransform-quadtoquad"/>
    </add-function>
    <add-function signature="quadToSquare(QPolygonF &amp;)" return-type="PyObject*" static="true">
      <inject-code file="../glue/qtgui.cpp" snippet="qtransform-quadtosquare"/>
    </add-function>

    <add-function signature="squareToQuad(QPolygonF &amp;)" return-type="PyObject*" static="true">
      <inject-code file="../glue/qtgui.cpp" snippet="qtransform-squaretoquad"/>
    </add-function>
    <!-- Disambiguate from Qt3DCore/qtransform.h -->
    <include file-name="QtGui/qtransform.h" location="global"/>
  </value-type>

  <value-type name="QGlyphRun">
    <enum-type name="GlyphRunFlag" flags="GlyphRunFlags"/>
  </value-type>

  <value-type name="QStaticText">
      <enum-type name="PerformanceHint"/>
  </value-type>
  <value-type name="QTextFragment"/>
  <value-type name="QBitmap" >
    <modify-function signature="fromData(QSize,const uchar*,QImage::Format)">
      <modify-argument index="2">
        <replace-type modified-type="PyBuffer"/>
      </modify-argument>
      <inject-code file="../glue/qtgui.cpp" snippet="qbitmap-fromdata"/>
    </modify-function>
  </value-type>
  <value-type name="QTextInlineObject"/>
  <value-type name="QTextDocumentFragment"/>
  <value-type name="QTextOption">
      <!-- PYSIDE-2088, Avoid MSVC signedness issues -->
      <enum-type name="Flag" flags="Flags" cpp-type="unsigned"/>
      <enum-type name="TabType"/>
      <enum-type name="WrapMode"/>
      <value-type name="Tab"/>
  </value-type>
  <value-type name="QTextLine" >
    <enum-type name="CursorPosition"/>
    <enum-type name="Edge"/>
    <modify-function signature="cursorToX(int*,QTextLine::Edge)const" remove="all"/>
    <modify-function signature="cursorToX(int,QTextLine::Edge)const">
        <modify-argument index="0">
            <replace-type modified-type="PyObject"/>
        </modify-argument>
        <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qtextline-cursortox"/>
    </modify-function>
    <modify-function signature="xToCursor(qreal,QTextLine::CursorPosition)const">
      <modify-argument index="2">
        <rename to="edge"/>
      </modify-argument>
    </modify-function>
  </value-type>
  <value-type name="QTextTableFormat"/>
  <value-type name="QTextImageFormat"/>
  <value-type name="QTextFrameFormat" >
    <enum-type name="BorderStyle"/>
    <enum-type name="Position"/>
  </value-type>
  <value-type name="QTextLength">
      <enum-type name="Type"/>
  </value-type>
  <value-type name="QPainterPath">
    <enum-type name="ElementType"/>
    <value-type name="Element">
      <include file-name="QPainterPath" location="global"/>
    </value-type>
  </value-type>
  <value-type name="QPalette">
    <enum-type name="ColorGroup"/>
    <enum-type name="ColorRole"/>
  </value-type>
  <object-type name="QInputMethod">
      <enum-type name="Action"/>
  </object-type>
  <value-type name="QKeySequence">
    <enum-type name="SequenceFormat"/>
    <enum-type name="SequenceMatch"/>
    <enum-type name="StandardKey"/>

    <add-function signature="__repr__" return-type="str">
        <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qkeysequence-repr"/>
    </add-function>

    <add-function signature="__reduce__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="reduce_code">
              <replace from="%REDUCE_FORMAT" to="iiii"/>
              <replace from="%REDUCE_ARGS" to="(*%CPPSELF)[0], (*%CPPSELF)[1], (*%CPPSELF)[2], (*%CPPSELF)[3]"/>
            </insert-template>
        </inject-code>
    </add-function>

    <modify-function signature="operator[](uint)const" remove="all"/>
    <add-function signature="__getitem__">
        <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qkeysequence-getitem"/>
    </add-function>

    <!-- ### Not necessary due the PySide QVariant conversion rules -->
    <modify-function signature="operator QVariant()const" remove="all"/>
  </value-type>
  <value-type name="QPicture" >
    <modify-function signature="load(QIODevice*)" allow-thread="yes"/>
    <modify-function signature="load(const QString&amp;)" allow-thread="yes"/>
    <modify-function signature="save(QIODevice*)" allow-thread="yes"/>
    <modify-function signature="save(const QString&amp;)" allow-thread="yes"/>
    <!-- See bug 759 -->
    <modify-function signature="data()const">
        <modify-argument index="return">
            <replace-type modified-type="PyObject"/>
        </modify-argument>
        <inject-code file="../glue/qtgui.cpp" snippet="qpicture-data"/>
    </modify-function>
    <modify-function signature="setData(const char*,uint)">
        <modify-argument index="1">
            <replace-type modified-type="PyBuffer"/>
            <conversion-rule class="target">
                <insert-template name="const_char_pybuffer"/>
            </conversion-rule>
            <conversion-rule class="native">
                <insert-template name="pybuffer_const_char"/>
            </conversion-rule>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
            <conversion-rule class="native">
                <insert-template name="uint_remove"/>
            </conversion-rule>
        </modify-argument>
    </modify-function>
  </value-type>
  <value-type name="QRegion" >
    <enum-type name="RegionType"/>
    <add-function signature="__len__" return-type="int">
        <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qregion-len"/>
    </add-function>
    <add-function signature="__getitem__">
        <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qregion-getitem"/>
    </add-function>
    <!-- ### This operator does not make sense in Python. -->
    <modify-function signature="operator&amp;=(QRegion)" remove="all"/>
    <modify-function signature="operator&amp;=(QRect)" remove="all"/>
    <!-- ### -->
  </value-type>

  <value-type name="QTextBlock">
    <value-type name="iterator" >
      <include file-name="QTextBlock" location="global"/>
      <!-- ### These operators where removed because they don't make sense in Python.
           The iterator methods (__iter__, next) replace this functionality. -->
      <modify-function signature="operator++()" remove="all"/>
      <modify-function signature="operator--()" remove="all"/>
      <!-- ### -->
      <add-function signature="__iter__()" return-type="PyObject*">
        <inject-code class="target" position="beginning">
          <insert-template name="__iter__"/>
        </inject-code>
      </add-function>
      <add-function signature="__next__()" return-type="PyObject*">
        <inject-code class="target" position="beginning">
          <insert-template name="__next__">
            <replace from="%CPPSELF_TYPE" to="QTextBlock::iterator"/>
          </insert-template>
        </inject-code>
      </add-function>
    </value-type>
    <add-function signature="__iter__()" return-type="PyObject*">
        <inject-code class="target" position="beginning">
          <insert-template name="__iter_parent__">
            <replace from="%CPPSELF_TYPE" to="QTextBlock::iterator"/>
          </insert-template>
        </inject-code>
    </add-function>
    <modify-function signature="setUserData(QTextBlockUserData*)">
        <inject-code class="target" position="end" file="../glue/qtgui.cpp" snippet="qtextblock-setuserdata"/>
    </modify-function>
    <modify-function signature="userData()const">
        <modify-argument index="return">
            <define-ownership class="target" owner="default"/>
        </modify-argument>
        <inject-code class="target" position="end" file="../glue/qtgui.cpp" snippet="qtextblock-userdata"/>
    </modify-function>
  </value-type>
  <value-type name="QTextBlockFormat">
    <enum-type name="LineHeightTypes" revision="4800"/>
    <enum-type name="MarkerType"/>
  </value-type>
  <value-type name="QTextTableCellFormat"/>
  <value-type name="QTextCharFormat" >
    <enum-type name="FontPropertiesInheritanceBehavior"/>
    <enum-type name="UnderlineStyle"/>
    <enum-type name="VerticalAlignment"/>
  </value-type>
  <value-type name="QTextFormat" >
    <enum-type name="FormatType" python-type="IntEnum"/>
    <enum-type name="ObjectTypes" python-type="IntEnum"/>
    <enum-type name="PageBreakFlag" flags="PageBreakFlags"/>
    <enum-type name="Property" python-type="IntEnum"/>
  </value-type>
  <value-type name="QTextListFormat">
      <enum-type name="Style"/>
  </value-type>
  <value-type name="QPolygon">
    <extra-includes>
      <include file-name="QTransform" location="global"/>
    </extra-includes>
    <!-- Expose operator==, != inherited from QList, which the parser does
         not see due to the TMP expression of the return type. -->
    <add-function signature="operator==(const QPolygon&amp;)" return-type="bool"/>
    <add-function signature="operator!=(const QPolygon&amp;)" return-type="bool"/>

    <add-function signature="__reduce__" return-type="str">
        <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qpolygon-reduce">
            <insert-template name="reduce_code">
              <replace from="%REDUCE_FORMAT" to="N"/>
              <replace from="%REDUCE_ARGS" to="points"/>
            </insert-template>
        </inject-code>
    </add-function>

    <!-- ### "QPolygon(int, const int*)" is an internal constructor. -->
    <modify-function signature="QPolygon(int,const int*)" remove="all"/>
    <!-- ### A QList parameter, for no defined type, will generate wrong code. -->
    <modify-function signature="operator+=(QList&lt;QPoint&gt;)" remove="all"/>
    <add-function signature="operator&lt;&lt;(QPoint)">
        <inject-code file="../glue/qtgui.cpp" snippet="qpolygon-operatorlowerlower"/>
    </add-function>
    <add-function signature="operator&lt;&lt;(QList&lt;QPoint&gt;)">
        <inject-code file="../glue/qtgui.cpp" snippet="qpolygon-operatorlowerlower"/>
    </add-function>
    <!-- ### -->
  </value-type>
  <value-type name="QPolygonF">
    <extra-includes>
      <include file-name="QTransform" location="global"/>
    </extra-includes>
    <!-- ### A QList parameter, for no defined type, will generate wrong code. -->
    <modify-function signature="operator+=(QList&lt;QPointF&gt;)" remove="all"/>
    <!-- ### See bug 777 -->
    <modify-function signature="operator&lt;&lt;(QList&lt;QPointF&gt;)" remove="all"/>
    <!-- ### -->
  </value-type>
  <value-type name="QIcon" >
    <enum-type name="Mode"/>
    <enum-type name="State"/>
    <enum-type name="ThemeIcon" since="6.7"/>
    <modify-function signature="QIcon(QIconEngine*)">
      <modify-argument index="1">
        <no-null-pointer/>
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <!-- PYSIDE-1669: Replace implicit conversion by a better explicit version -->
    <add-function signature="addPixmap(PyPathLike@path@)">
        <inject-code file="../glue/qtgui.cpp" snippet="qicon-addpixmap"/>
    </add-function>
  </value-type>

  <value-type name="QPixmap" >
    <add-function signature="QPixmap(const QImage&amp;@image@)">
        <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qpixmap"/>
    </add-function>
    <modify-function signature="QPixmap(const char*const[])">
        <modify-argument index="1">
            <replace-type modified-type="PySequence"/>
        </modify-argument>
        <inject-code class="target" position="beginning"
                     file="../glue/qtgui.cpp" snippet="qpixmap-load-xpm"/>
    </modify-function>
    <modify-function signature="QPixmap(const QString &amp;, const char *, QFlags&lt;Qt::ImageConversionFlag&gt;)">
        <modify-argument index="1"><replace-type modified-type="PyPathLike"/></modify-argument>
        <inject-code class="target" position="beginning" file="../glue/qtcore.cpp" snippet="qfile-path-1"/>
    </modify-function>
    <!-- ### Operator ! doesn't make sense in Python. -->
    <modify-function signature="operator!()const" remove="all"/>

    <modify-function signature="loadFromData(const uchar*,uint,const char*,QFlags&lt;Qt::ImageConversionFlag&gt;)">
        <inject-documentation format="target" mode="append">
        This method must be used with an QPixmap object, not the class:

        ::

            # Wrong
            pixmap = QPixmap.loadFromData(...)

            # Right
            pixmap = QPixmap().loadFromData(...)

        </inject-documentation>
      <modify-argument index="1">
        <replace-type modified-type="PyBytes"/>
        <conversion-rule class="native">
            <insert-template name="pybytes_const_uchar"/>
        </conversion-rule>
      </modify-argument>
      <modify-argument index="2">
        <remove-argument/>
        <conversion-rule class="native">
            <insert-template name="pybytes_uint"/>
        </conversion-rule>
      </modify-argument>
    </modify-function>
  </value-type>
  <value-type name="QTextCursor" >
    <extra-includes>
      <include file-name="QTextBlock" location="global"/>
      <include file-name="QTextDocumentFragment" location="global"/>
    </extra-includes>
    <enum-type name="MoveMode"/>
    <enum-type name="MoveOperation"/>
    <enum-type name="SelectionType"/>
    <modify-function signature="selectedTableCells(int*,int*,int*,int*)const">
        <modify-argument index="0">
            <replace-type modified-type="PyObject"/>
        </modify-argument>
        <modify-argument index="1">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="4">
            <remove-argument/>
        </modify-argument>
        <inject-code class="target" position="beginning">
            <insert-template name="fix_number*,number*,number*,number*">
                <replace from="$TYPE" to="int"/>
            </insert-template>
        </inject-code>
        <inject-code class="native" position="end">
            <insert-template name="fix_native_return_number*,number*,number*,number*">
                <replace from="$TYPE" to="int"/>
            </insert-template>
        </inject-code>
    </modify-function>
    <modify-function signature="currentFrame()const">
      <modify-argument index="return">
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="currentTable()const">
      <modify-argument index="return">
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="currentList()const">
      <modify-argument index="return">
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertTable(int,int,const QTextTableFormat &amp;)">
      <modify-argument index="return">
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertTable(int,int)">
      <modify-argument index="return">
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>
  </value-type>

  <value-type name="QConicalGradient" polymorphic-id-expression="%B-&gt;type() == QGradient::ConicalGradient"/>
  <value-type name="QFontInfo"/>
  <value-type name="QRadialGradient" polymorphic-id-expression="%B-&gt;type() == QGradient::RadialGradient"/>
  <value-type name="QFont" >
    <enum-type name="Capitalization"/>
    <enum-type name="SpacingType"/>
    <enum-type name="Stretch" python-type="IntEnum"/>
    <enum-type name="Style"/>
    <enum-type name="StyleHint"/>
    <enum-type name="StyleStrategy" python-type="Flag"/>
    <enum-type name="Weight" python-type="IntEnum"/>
    <enum-type name="HintingPreference" revision="4800"/>
    <extra-includes>
      <include file-name="QStringList" location="global"/>
    </extra-includes>
    <value-type name="Tag" since="6.7">
        <inject-code class="native" position="beginning"
                     file="../glue/qtgui.cpp" snippet="qfont-tag-from-str-helper"/>
        <add-function signature="Tag(QString@name@)">
            <inject-code class="target" position="beginning"
                         file="../glue/qtgui.cpp" snippet="qfont-tag-init-str"/>
        </add-function>
        <add-function signature="fromString(QString@name@)"
                      static="true" return-type="QFont::Tag">
            <inject-code class="target" position="beginning"
                         file="../glue/qtgui.cpp" snippet="qfont-tag-fromString"/>
        </add-function>
        <add-function signature="fromValue(int@value@)"
                      static="true" return-type="QFont::Tag">
            <inject-code class="target" position="beginning"
                         file="../glue/qtgui.cpp" snippet="qfont-tag-fromValue"/>
        </add-function>
    </value-type>
    <!-- PYSIDE-1685: QFont(QString) should be checked first, else it will be interpreted as sequence -->
    <modify-function signature="QFont(const QString&amp;,int,int, bool)" overload-number="0"/>
    <modify-function signature="QFont(const QStringList &amp;,int,int, bool)" overload-number="1"/>
    <modify-function signature="setStyleHint(QFont::StyleHint,QFont::StyleStrategy)">
      <modify-argument index="2">
        <rename to="strategy"/>
      </modify-argument>
    </modify-function>
  </value-type>
  <value-type name="QFontVariableAxis" since="6.9"/>
  <value-type name="QTextTableCell" >
    <extra-includes>
      <include file-name="QTextCursor" location="global"/>
    </extra-includes>
  </value-type> <!--
  <function signature="QImageCleanupFunction(void*)">
    <extra-includes>
      <include file-name="QImage" location="global"/>
    </extra-includes>
  </function> -->
  <primitive-type name="QImageCleanupFunction"/>

  <value-type name="QImage">
    <enum-type name="Format"/>
    <enum-type name="InvertMode"/>
    <extra-includes>
      <include file-name="QStringList" location="global"/>
    </extra-includes>
    <inject-code class="native" position="beginning"
                 file="../glue/qtgui.cpp" snippet="qimage-decref-image-data"/>

    <modify-function signature="load(const QString&amp;, const char*)" allow-thread="yes"/>
    <modify-function signature="load(QIODevice*,const char*)" allow-thread="yes"/>
    <modify-function signature="save(const QString&amp;,const char*,int)const" allow-thread="yes"/>
    <modify-function signature="save(QIODevice*,const char*,int)const" allow-thread="yes"/>
    <modify-function signature="QImage(uchar*,int,int,qsizetype,QImage::Format,QImageCleanupFunction,void*)">
        <modify-argument index="1">
            <replace-type modified-type="PyBuffer"/>
        </modify-argument>
        <inject-code>
            <insert-template name="qimage_buffer_constructor">
                <replace from="%ARGS" to="%2, %3, %4, %5"/>
            </insert-template>
        </inject-code>
    </modify-function>
    <modify-function signature="QImage(uchar*,int,int,QImage::Format,QImageCleanupFunction,void*)">
        <modify-argument index="1">
            <replace-type modified-type="PyBuffer"/>
        </modify-argument>
        <inject-code>
            <insert-template name="qimage_buffer_constructor">
                <replace from="%ARGS" to="%2, %3, %4"/>
            </insert-template>
        </inject-code>
    </modify-function>

    <modify-function signature="QImage(const QString&amp;, const char *)" allow-thread="yes">
        <modify-argument index="1"><replace-type modified-type="PyPathLike"/></modify-argument>
        <inject-code class="target" position="beginning" file="../glue/qtcore.cpp" snippet="qfile-path-1"/>
    </modify-function>

    <add-function signature="QImage(QString&amp;,int@width@,int@height@,int@bytes_per_line@,QImage::Format@format@)">
        <inject-code>
            <insert-template name="qimage_buffer_constructor">
                <replace from="%ARGS" to="%2, %3, %4, %5"/>
            </insert-template>
        </inject-code>
    </add-function>
    <add-function signature="QImage(QString&amp;,int@width@,int@height@,QImage::Format@format@)">
        <inject-code>
            <insert-template name="qimage_buffer_constructor">
                <replace from="%ARGS" to="%2, %3, %4"/>
            </insert-template>
        </inject-code>
    </add-function>

    <!-- The non-const versions are already used -->
    <modify-function signature="QImage(const uchar*,int,int,qsizetype,QImage::Format,QImageCleanupFunction,void*)" remove="all"/>
    <modify-function signature="QImage(const uchar*,int,int,QImage::Format,QImageCleanupFunction,void*)" remove="all"/>
    <!-- ### -->

    <modify-function signature="QImage(const char*const[])">
        <modify-argument index="1">
            <replace-type modified-type="PySequence"/>
        </modify-argument>
        <inject-code class="target" position="beginning"
                     file="../glue/qtgui.cpp" snippet="qpixmap-load-xpm"/>
    </modify-function>
    <!-- ### There is already an fromData with a QByteArray type (that is convertible from Python's str) as the first type. -->
    <modify-function signature="fromData(const uchar*,int,const char*)" remove="all"/>
    <!-- ### There is already an loadFromData with a QByteArray type (that is convertible from Python's str) as the first type. -->
    <modify-function signature="loadFromData(const uchar*,int,const char*)" remove="all"/>

    <modify-function signature="constBits()const">
        <inject-code file="../glue/qtgui.cpp" snippet="qimage-constbits"/>
    </modify-function>
    <modify-function signature="bits()">
        <inject-code file="../glue/qtgui.cpp" snippet="qimage-bits"/>
    </modify-function>
    <modify-function signature="constScanLine(int)const">
        <inject-code file="../glue/qtgui.cpp" snippet="qimage-constscanline"/>
        <modify-argument index="return">
            <replace-type modified-type="PyObject"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="scanLine(int)">
        <inject-code file="../glue/qtgui.cpp" snippet="qimage-scanline"/>
        <modify-argument index="return">
            <replace-type modified-type="PyObject"/>
        </modify-argument>
    </modify-function>
    <!--
        Only the non-const version of bits() and scanLine() is exported to Python
        If the user don't want to detach the QImage data he must use constBits or constScanLine
        as Python doesn't have the concept of constness.
    -->
    <modify-function signature="bits()const" remove="all"/>
    <modify-function signature="scanLine(int)const" remove="all"/>
    <modify-function signature="invertPixels(QImage::InvertMode)">
      <modify-argument index="1">
        <rename to="mode"/>
      </modify-argument>
    </modify-function>
  </value-type>

  <value-type name="QCursor" >
    <extra-includes>
      <include file-name="QPixmap" location="global"/>
      <include file-name="Qt" location="global"/>
    </extra-includes>
  </value-type>
  <value-type name="QFontDatabase" >
    <extra-includes>
      <include file-name="QStringList" location="global"/>
    </extra-includes>
    <enum-type name="SystemFont"/>
    <enum-type name="WritingSystem"/>
  </value-type>
  <value-type name="QPen">
    <extra-includes>
      <include file-name="QBrush" location="global"/>
    </extra-includes>
  </value-type>
  <value-type name="QBrush">
    <extra-includes>
      <include file-name="QPixmap" location="global"/>
    </extra-includes>
  </value-type>

  <value-type name="QColor">
    <enum-type name="NameFormat"/>
    <enum-type name="Spec"/>
    <extra-includes>
      <include file-name="QVariant" location="global"/>
    </extra-includes>
    <add-function signature="__repr__" return-type="str">
        <inject-code class="target" position="beginning"
                     file="../glue/qtgui.cpp" snippet="qcolor-repr"/>
    </add-function>
    <add-function signature="__str__" return-type="str">
        <inject-code class="target" position="beginning"
                     file="../glue/qtgui.cpp" snippet="qcolor-repr"/>
    </add-function>
    <add-function signature="__setstate__(PyObject*)" return-type="PyObject">
        <inject-code file="../glue/qtgui.cpp" snippet="qcolor-setstate"/>
    </add-function>
    <add-function signature="__reduce__" return-type="str">
        <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qcolor-reduce"/>
    </add-function>

    <add-function signature="toTuple" return-type="PyObject">
        <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qcolor-totuple"/>
    </add-function>
    <!-- ### "QColor(QColor::Spec)" is an internal method. -->
    <modify-function signature="QColor(QColor::Spec)" remove="all"/>
    <!-- ### Constructor removed because we already have an overload using QString. -->
    <modify-function signature="QColor(const char*)" remove="all"/>
    <!-- ### -->
    <add-function signature="QColor(QVariant@color@)">
        <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qcolor"/>
    </add-function>
    <!-- get* methods. Inject code -->
    <modify-function signature="getCmyk(int*,int*,int*,int*,int*)const">
        <modify-argument index="0">
            <replace-type modified-type="PyObject*"/>
        </modify-argument>
        <modify-argument index="1">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="4">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="5">
            <remove-argument/>
            <remove-default-expression/>
        </modify-argument>

        <inject-code class="target" position="beginning">
            <insert-template name="fix_number*,number*,number*,number*,number*">
                <replace from="$TYPE" to="int"/>
            </insert-template>
        </inject-code>
    </modify-function>
    <modify-function signature="getCmykF(float*,float*,float*,float*,float*)const">
        <modify-argument index="0">
            <replace-type modified-type="PyObject*"/>
        </modify-argument>
        <modify-argument index="1">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="4">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="5">
            <remove-argument/>
            <remove-default-expression/>
        </modify-argument>

        <inject-code class="target" position="beginning">
            <insert-template name="fix_number*,number*,number*,number*,number*">
                <replace from="$TYPE" to="float"/>
            </insert-template>
        </inject-code>
    </modify-function>
    <modify-function signature="getHsl(int*,int*,int*,int*)const">
        <modify-argument index="0">
            <replace-type modified-type="PyObject*"/>
        </modify-argument>
        <modify-argument index="1">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="4">
            <remove-argument/>
            <remove-default-expression/>
        </modify-argument>
        <inject-code class="target" position="beginning">
            <insert-template name="fix_number*,number*,number*,number*">
                <replace from="$TYPE" to="int"/>
            </insert-template>
        </inject-code>
    </modify-function>
    <modify-function signature="getHslF(float*,float*,float*,float*)const">
        <modify-argument index="0">
            <replace-type modified-type="PyObject*"/>
        </modify-argument>
        <modify-argument index="1">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="4">
            <remove-argument/>
            <remove-default-expression/>
        </modify-argument>
        <inject-code class="target" position="beginning">
            <insert-template name="fix_number*,number*,number*,number*">
                <replace from="$TYPE" to="float"/>
            </insert-template>
        </inject-code>
    </modify-function>
    <modify-function signature="getHsv(int*,int*,int*,int*)const">
        <modify-argument index="0">
            <replace-type modified-type="PyObject*"/>
        </modify-argument>
        <modify-argument index="1">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="4">
            <remove-argument/>
            <remove-default-expression/>
        </modify-argument>
        <inject-code class="target" position="beginning">
            <insert-template name="fix_number*,number*,number*,number*">
                <replace from="$TYPE" to="int"/>
            </insert-template>
        </inject-code>
    </modify-function>
    <modify-function signature="getHsvF(float*,float*,float*,float*)const">
        <modify-argument index="0">
            <replace-type modified-type="PyObject*"/>
        </modify-argument>
        <modify-argument index="1">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="4">
            <remove-argument/>
            <remove-default-expression/>
        </modify-argument>
        <inject-code class="target" position="beginning">
            <insert-template name="fix_number*,number*,number*,number*">
                <replace from="$TYPE" to="float"/>
            </insert-template>
        </inject-code>
    </modify-function>
    <modify-function signature="getRgb(int*,int*,int*,int*)const">
        <modify-argument index="0">
            <replace-type modified-type="PyObject*"/>
        </modify-argument>
        <modify-argument index="1">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="4">
            <remove-argument/>
            <remove-default-expression/>
        </modify-argument>
        <inject-code class="target" position="beginning">
            <insert-template name="fix_number*,number*,number*,number*">
                <replace from="$TYPE" to="int"/>
            </insert-template>
        </inject-code>
    </modify-function>
    <modify-function signature="getRgbF(float*,float*,float*,float*)const">
        <modify-argument index="0">
            <replace-type modified-type="PyObject*"/>
        </modify-argument>
        <modify-argument index="1">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="4">
            <remove-argument/>
            <remove-default-expression/>
        </modify-argument>
        <inject-code class="target" position="beginning">
            <insert-template name="fix_number*,number*,number*,number*">
                <replace from="$TYPE" to="float"/>
            </insert-template>
        </inject-code>
    </modify-function>
  </value-type>

  <namespace-type name="QColorConstants">
    <extra-includes>
        <include file-name="QColor" location="global"/>
    </extra-includes>
    <namespace-type name="Svg">
        <extra-includes>
            <include file-name="QColor" location="global"/>
        </extra-includes>
    </namespace-type>
  </namespace-type>

  <value-type name="QColorSpace">
      <enum-type name="ColorModel" since="6.8"/>
      <enum-type name="NamedColorSpace"/>
      <enum-type name="Primaries"/>
      <enum-type name="TransferFunction"/>
      <enum-type name="TransformModel" since="6.8"/>
  </value-type>

  <value-type name="QColorTransform"/>

  <value-type name="QRgba64"/>

  <value-type name="QFontMetricsF" >

    <modify-function signature="boundingRect(QChar)const" rename="boundingRectChar">
      <modify-argument index="1">
        <replace-type modified-type="char"/>
      </modify-argument>
      <inject-code class="target" position="beginning"
                   file="../glue/qtgui.cpp" snippet="qfontmetrics-qfontcharfix"/>
    </modify-function>

    <modify-function signature="horizontalAdvance(QChar)const" rename="horizontalAdvanceChar">
      <modify-argument index="1">
        <replace-type modified-type="char"/>
      </modify-argument>
      <inject-code class="target" position="beginning"
                   file="../glue/qtgui.cpp" snippet="qfontmetrics-qfontcharfix"/>
    </modify-function>

    <modify-function signature="boundingRect(QRectF,int,QString,int,int*)const">
      <modify-argument index="5">
          <replace-type modified-type="PyObject"/>
          <array/>
      </modify-argument>
      <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qfontmetricsf-boundingrect"/>
    </modify-function>

    <modify-function signature="size(int,QString,int,int*)const">
      <modify-argument index="4">
          <replace-type modified-type="PyObject"/>
          <array/>
      </modify-argument>
      <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qfontmetricsf-size"/>
    </modify-function>
  </value-type>
  <value-type name="QFontMetrics" >

    <modify-function signature="boundingRect(QChar)const" rename="boundingRectChar">
      <modify-argument index="1">
        <replace-type modified-type="char"/>
      </modify-argument>
      <inject-code class="target" position="beginning"
                   file="../glue/qtgui.cpp" snippet="qfontmetrics-qfontcharfix"/>
    </modify-function>

    <modify-function signature="horizontalAdvance(QChar)const" rename="horizontalAdvanceChar">
      <modify-argument index="1">
        <replace-type modified-type="char"/>
      </modify-argument>
      <inject-code class="target" position="beginning"
                   file="../glue/qtgui.cpp" snippet="qfontmetrics-qfontcharfix"/>
    </modify-function>

    <modify-function signature="boundingRect(int,int,int,int,int,QString,int,int*)const">
      <modify-argument index="8">
          <replace-type modified-type="PyObject"/>
          <array/>
      </modify-argument>
      <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qfontmetrics-boundingrect-1"/>
    </modify-function>

    <modify-function signature="boundingRect(QRect,int,QString,int,int*)const">
      <modify-argument index="5">
          <replace-type modified-type="PyObject"/>
          <array/>
      </modify-argument>
      <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qfontmetrics-boundingrect-2"/>
    </modify-function>

    <modify-function signature="size(int,QString,int,int*)const">
      <modify-argument index="4">
          <replace-type modified-type="PyObject"/>
          <array/>
      </modify-argument>
      <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qfontmetrics-size"/>
    </modify-function>
  </value-type>
  <value-type name="QGradient" polymorphic-id-expression="%B-&gt;type() == QGradient::NoGradient">
      <enum-type name="CoordinateMode"/>
      <enum-type name="InterpolationMode"/>
      <enum-type name="Preset"/>
      <enum-type name="Spread"/>
      <enum-type name="Type"/>
  </value-type>
  <value-type name="QLinearGradient" polymorphic-id-expression="%B-&gt;type() == QGradient::LinearGradient"/>
  <object-type name="QPaintDevice">
      <enum-type name="PaintDeviceMetric"/>
      <!-- inline error -->
      <modify-function signature="getDecodedMetricF(QPaintDevice::PaintDeviceMetric,QPaintDevice::PaintDeviceMetric)const" remove="all"/>
  </object-type>
  <object-type name="QPagedPaintDevice">
    <enum-type name="PdfVersion"/>
  </object-type>
  <object-type name="QAbstractTextDocumentLayout">
    <value-type name="PaintContext" >
      <include file-name="QAbstractTextDocumentLayout" location="global"/>
    </value-type>
    <value-type name="Selection"/>
    <modify-function signature="setPaintDevice(QPaintDevice*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="draw(QPainter*,QAbstractTextDocumentLayout::PaintContext)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="drawInlineObject(QPainter*,QRectF,QTextInlineObject,int,QTextFormat)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="update(const QRectF &amp;)" allow-thread="yes"/>
  </object-type>
  <object-type name="QPyTextObject"/>

  <object-type name="QDesktopServices">
    <modify-function signature="openUrl(const QUrl&amp;)" allow-thread="yes"/>
  </object-type>
  <object-type name="QDoubleValidator">
      <enum-type name="Notation"/>
  </object-type>
  <object-type name="QIconEngine">
    <object-type name="ScaledPixmapArgument"/>
    <enum-type name="IconEngineHook"/>
    <modify-function signature="paint(QPainter*,QRect,QIcon::Mode,QIcon::State)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="read(QDataStream&amp;)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="write(QDataStream&amp;)const">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <!-- ### This makes little sense in Python. Could be reassessed later. -->
    <modify-function signature="virtual_hook(int,void*)" remove="all"/>
  </object-type>
  <object-type name="QImageWriter">
    <enum-type name="ImageWriterError"/>
    <modify-function signature="setDevice(QIODevice*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="write(const QImage&amp;)" allow-thread="yes"/>
  </object-type>
  <object-type name="QIntValidator"/>

  <object-type name="QPainterPathStroker" copyable="false"/>
  <object-type name="QPixmapCache">
    <value-type name="Key"/>
    <add-function signature="find(QPixmapCache::Key&amp;@key@)">
        <inject-code file="../glue/qtgui.cpp" snippet="qpixmapcache-find"/>
    </add-function>
    <add-function signature="find(const QString&amp;@key@)">
        <inject-code file="../glue/qtgui.cpp" snippet="qpixmapcache-find"/>
    </add-function>
  </object-type>

  <object-type name="QRegularExpressionValidator"/>

  <object-type name="QStandardItem">
    <enum-type name="ItemType"/>
    <modify-function signature="operator&lt;(QStandardItem)const">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="read(QDataStream&amp;)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="write(QDataStream&amp;)const">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>

    <modify-function signature="appendColumn(const QList&lt;QStandardItem*&gt; &amp;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="appendRow(const QList&lt;QStandardItem*&gt; &amp;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="appendRow(QStandardItem*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="appendRows(const QList&lt;QStandardItem*&gt; &amp;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="insertColumn(int,const QList&lt;QStandardItem*&gt; &amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertRow(int,const QList&lt;QStandardItem*&gt; &amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertRows(int,const QList&lt;QStandardItem*&gt; &amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="setChild(int,int,QStandardItem*)">
      <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qstandarditem-setchild-1"/>
      <modify-argument index="3">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setChild(int,QStandardItem*)">
      <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qstandarditem-setchild-2"/>
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

     <modify-function signature="takeChild(int,int)">
      <modify-argument index="return">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="clone()const">
      <modify-argument index="return">
        <define-ownership class="native" owner="c++"/>
        <define-ownership class="target" owner="target"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QSyntaxHighlighter">
    <modify-function signature="setCurrentBlockUserData(QTextBlockUserData*)">
      <modify-argument index="1">
        <define-ownership class="target" owner="c++"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setDocument(QTextDocument*)">
      <modify-argument index="1" pyi-type="Optional[PySide6.QtGui.QTextDocument]">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="document()const">
      <modify-argument index="this">
        <parent index="return" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QTextBlockGroup"/>
  <object-type name="QTextBlockUserData"/>
  <object-type name="QTextItem">
      <enum-type name="RenderFlag" flags="RenderFlags"/>
  </object-type>
  <object-type name="QTextList"/>
  <object-type name="QTextObject"/>
  <object-type name="QTextObjectInterface" >
    <modify-function signature="drawObject(QPainter*,QRectF,QTextDocument*,int,QTextFormat)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
  </object-type>

  <object-type name="QValidator">
    <enum-type name="State"/>
    <modify-function signature="fixup(QString &amp;)const">
      <modify-argument index="return">
        <replace-type modified-type="QString"/>
      </modify-argument>
      <inject-code class="native" position="end">
        <insert-template name="return_QString_native"/>
      </inject-code>
      <inject-code class="target" position="end" file="../glue/qtcore.cpp" snippet="qstring-return"/>
    </modify-function>
    <modify-function signature="validate(QString &amp;,int &amp;)const">
      <modify-argument index="return">
        <replace-type modified-type="PyObject"/>
        <conversion-rule class="native">
            <insert-template name="validator_conversionrule"/>
        </conversion-rule>
      </modify-argument>
      <inject-code class="target" position="end">
        <insert-template name="return_tuple_QValidator_QString_int"/>
      </inject-code>
    </modify-function>
  </object-type>

  <object-type name="QActionEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::ActionAdded || %B-&gt;type() == QEvent::ActionRemoved || %B-&gt;type() == QEvent::ActionChanged"/>
  <object-type name="QCloseEvent" polymorphic-id-expression="%B-&gt;type() == QEvent::Close"/>
  <object-type name="QContextMenuEvent" polymorphic-id-expression="%B-&gt;type() == QEvent::ContextMenu">
      <enum-type name="Reason"/>
  </object-type>

  <value-type name="QEventPoint">
      <enum-type name="State"/>
  </value-type>
  <object-type name="QDragEnterEvent" polymorphic-id-expression="%B-&gt;type() == QEvent::DragEnter">
      <add-function signature="__repr__" return-type="str">
          <inject-code class="target" position="beginning">
              <insert-template name="repr_qdebug_gui"/>
          </inject-code>
      </add-function>
  </object-type>
  <object-type name="QDragLeaveEvent" polymorphic-id-expression="%B-&gt;type() == QEvent::DragLeave">
      <add-function signature="__repr__" return-type="str">
          <inject-code class="target" position="beginning">
              <insert-template name="repr_qdebug_gui"/>
          </inject-code>
      </add-function>
  </object-type>
  <object-type name="QDragMoveEvent" polymorphic-id-expression="%B-&gt;type() == QEvent::DragMove">
      <add-function signature="__repr__" return-type="str">
          <inject-code class="target" position="beginning">
              <insert-template name="repr_qdebug_gui"/>
          </inject-code>
      </add-function>
  </object-type>
  <object-type name="QDropEvent" polymorphic-id-expression="%B-&gt;type() == QEvent::Drop">
    <modify-function signature="source()const">
      <modify-argument index="return">
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>
    <add-function signature="__repr__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="repr_qdebug_gui"/>
        </inject-code>
    </add-function>
  </object-type>
  <object-type name="QChildWindowEvent" since="6.7"
               polymorphic-id-expression="%B-&gt;type() == QEvent::ChildWindowAdded || %B-&gt;type() == QEvent::ChildWindowRemoved"/>
  <object-type name="QEnterEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::Enter"/>
  <object-type name="QExposeEvent" polymorphic-id-expression="%B-&gt;type() == QEvent::Expose"/>
  <object-type name="QFileOpenEvent" polymorphic-id-expression="%B-&gt;type() == QEvent::FileOpen"/>
  <object-type name="QFocusEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::FocusIn || %B-&gt;type() == QEvent::FocusOut">
      <add-function signature="__repr__" return-type="str">
          <inject-code class="target" position="beginning">
              <insert-template name="repr_qdebug_gui"/>
          </inject-code>
      </add-function>
  </object-type>
  <object-type name="QHelpEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::ToolTip || %B-&gt;type() == QEvent::WhatsThis"/>
  <object-type name="QHideEvent" polymorphic-id-expression="%B-&gt;type() == QEvent::Hide"/>
  <object-type name="QHoverEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::HoverEnter || %B-&gt;type() == QEvent::HoverLeave || %B-&gt;type() == QEvent::HoverMove"/>
  <object-type name="QIconDragEvent" polymorphic-id-expression="%B-&gt;type() == QEvent::IconDrag"/>

  <object-type name="QInputMethodEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::InputMethod">
    <!-- only declare this if ndef QT_NO_INPUTMETHOD -->
    <enum-type name="AttributeType"/>
    <value-type name="Attribute">
      <include file-name="QInputMethodEvent" location="global"/>
    </value-type>
    <add-function signature="__repr__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="repr_qdebug_gui"/>
        </inject-code>
    </add-function>
    <!-- endif ndef QT_NO_INPUTMETHOD -->
  </object-type>
  <object-type name="QInputMethodQueryEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::InputMethodQuery"/>

  <object-type name="QMoveEvent" polymorphic-id-expression="%B-&gt;type() == QEvent::Move">
      <add-function signature="__repr__" return-type="str">
          <inject-code class="target" position="beginning">
              <insert-template name="repr_qdebug_gui"/>
          </inject-code>
      </add-function>
  </object-type>
  <object-type name="QNativeGestureEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::NativeGesture"/>
  <object-type name="QPlatformSurfaceEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::PlatformSurface">
     <enum-type name="SurfaceEventType"/>
  </object-type>
  <object-type name="QResizeEvent" polymorphic-id-expression="%B-&gt;type() == QEvent::Resize">
      <add-function signature="__repr__" return-type="str">
          <inject-code class="target" position="beginning">
              <insert-template name="repr_qdebug_gui"/>
          </inject-code>
      </add-function>
  </object-type>
  <object-type name="QShortcutEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::Shortcut">
  </object-type>
  <object-type name="QShowEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::Show"/>
  <object-type name="QSinglePointEvent"/>
  <object-type name="QStatusTipEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::StatusTip"/>
  <object-type name="QTabletEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::TabletMove || %B-&gt;type() == QEvent::TabletPress || %B-&gt;type() == QEvent::TabletRelease">
      <add-function signature="__repr__" return-type="str">
          <inject-code class="target" position="beginning">
              <insert-template name="repr_qdebug_gui"/>
          </inject-code>
      </add-function>
  </object-type>
  <object-type name="QToolBarChangeEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::ToolBarChange"/>
  <object-type name="QWhatsThisClickedEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::WhatsThisClicked"/>
  <object-type name="QWheelEvent" polymorphic-id-expression="%B-&gt;type() == QEvent::Wheel">
      <add-function signature="__repr__" return-type="str">
          <inject-code class="target" position="beginning">
              <insert-template name="repr_qdebug_gui"/>
          </inject-code>
      </add-function>
  </object-type>

  <object-type name="QWindowStateChangeEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::WindowStateChange">
      <add-function signature="__repr__" return-type="str">
          <inject-code class="target" position="beginning">
              <insert-template name="repr_qdebug_gui"/>
          </inject-code>
      </add-function>
  </object-type>
  <object-type name="QInputEvent"/>
  <object-type name="QKeyEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::KeyPress || %B-&gt;type() == QEvent::KeyRelease || %B-&gt;type() == QEvent::ShortcutOverride">
      <add-function signature="operator!=(QKeySequence::StandardKey@standard_key@)">
          <inject-code class="target" file="../glue/qtgui.cpp" snippet="qkeyevent-operatornotequal"/>
      </add-function>
      <add-function signature="__repr__" return-type="str">
          <inject-code class="target" position="beginning">
              <insert-template name="repr_qdebug_gui"/>
          </inject-code>
      </add-function>
  </object-type>
  <object-type name="QMouseEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::MouseButtonDblClick || %B-&gt;type() == QEvent::MouseButtonPress || %B-&gt;type() == QEvent::MouseButtonRelease || %B-&gt;type() == QEvent::MouseMove">
      <modify-function signature="globalPos() const" deprecated="yes"/>
      <modify-function signature="localPos() const" deprecated="yes"/>
      <modify-function signature="pos() const" deprecated="yes"/>
      <modify-function signature="windowPos() const" deprecated="yes"/>
      <modify-function signature="screenPos() const" deprecated="yes"/>
      <modify-function signature="x() const" deprecated="yes"/>
      <modify-function signature="y() const" deprecated="yes"/>
      <modify-function signature="globalX() const" deprecated="yes"/>
      <modify-function signature="globalY() const" deprecated="yes"/>
      <add-function signature="__repr__" return-type="str">
          <inject-code class="target" position="beginning">
              <insert-template name="repr_qdebug_gui"/>
          </inject-code>
      </add-function>
  </object-type>
  <object-type name="QPaintEvent" polymorphic-id-expression="%B-&gt;type() == QEvent::Paint"/>
  <object-type name="QScrollEvent" polymorphic-id-expression="%B-&gt;type() == QEvent::Scroll">
      <enum-type name="ScrollState"/>
  </object-type>
  <object-type name="QPointerEvent" copyable= "false">
      <add-function signature="__repr__" return-type="str">
          <inject-code class="target" position="beginning">
              <insert-template name="repr_qdebug_gui"/>
          </inject-code>
      </add-function>
  </object-type>
  <object-type name="QScrollPrepareEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::ScrollPrepare"/>

  <object-type name="QTextFrame" >
    <extra-includes>
      <include file-name="QTextCursor" location="global"/>
    </extra-includes>
    <value-type name="iterator" >
      <include file-name="QTextFrame" location="global"/>
      <add-function signature="__iter__()" return-type="PyObject*">
        <inject-code class="target" position="beginning">
          <insert-template name="__iter__"/>
        </inject-code>
      </add-function>
      <add-function signature="__next__()" return-type="PyObject*">
        <inject-code class="target" position="beginning">
          <insert-template name="__next__">
            <replace from="%CPPSELF_TYPE" to="QTextFrame::iterator"/>
          </insert-template>
        </inject-code>
      </add-function>
    </value-type>
    <add-function signature="__iter__()" return-type="PyObject*">
        <inject-code class="target" position="beginning">
          <insert-template name="__iter_parent__">
            <replace from="%CPPSELF_TYPE" to="QTextFrame::iterator"/>
          </insert-template>
        </inject-code>
    </add-function>
  </object-type>
  <object-type name="QImageIOHandler">
    <extra-includes>
      <include file-name="QRect" location="global"/>
    </extra-includes>
    <enum-type name="ImageOption"/>
    <enum-type name="Transformation" flags="Transformations"/>
    <modify-function signature="setDevice(QIODevice*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>

  <object-type name="QImageReader">
    <extra-includes>
      <include file-name="QColor" location="global"/>
      <include file-name="QRect" location="global"/>
      <include file-name="QSize" location="global"/>
      <include file-name="QStringList" location="global"/>
      <include file-name="QImage" location="global"/>
    </extra-includes>
    <enum-type name="ImageReaderError"/>
    <!-- ### This method does not make sense in Python.
         Update: perhaps it does, but no one is missing it. -->
    <modify-function signature="read(QImage*)" remove="all"/>
    <modify-function signature="read()" allow-thread="yes"/>
    <modify-function signature="setDevice(QIODevice*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QMovie">
    <extra-includes>
      <include file-name="QColor" location="global"/>
      <include file-name="QImage" location="global"/>
      <include file-name="QPixmap" location="global"/>
      <include file-name="QRect" location="global"/>
      <include file-name="QSize" location="global"/>
    </extra-includes>
    <enum-type name="CacheMode"/>
    <enum-type name="MovieState"/>
    <!-- ### "cacheMode()" is an internal method. -->
    <modify-function signature="setDevice(QIODevice*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QDrag">
    <extra-includes>
      <include file-name="QPoint" location="global"/>
      <include file-name="QPixmap" location="global"/>
    </extra-includes>
    <modify-function signature="QDrag(QObject*)">
      <modify-argument index="this">
        <parent index="1" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="source()const">
      <modify-argument index="return">
        <define-ownership owner="target"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="target()const">
      <modify-argument index="return">
        <define-ownership owner="target"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setMimeData(QMimeData*)">
      <modify-argument index="1">
        <!-- TODO: maybe this is not the best solution -->
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="exec(QFlags&lt;Qt::DropAction&gt;)" allow-thread="yes"/>
    <add-function signature="exec_(QFlags&lt;Qt::DropAction&gt; @supportedActions@ = Qt::MoveAction)" return-type="Qt::DropAction">
        <inject-code file="../glue/qtgui.cpp" snippet="qdrag-exec-arg1"/>
    </add-function>
    <modify-function signature="exec(QFlags&lt;Qt::DropAction&gt;,Qt::DropAction)" allow-thread="yes"/>
    <add-function signature="exec_(QFlags&lt;Qt::DropAction&gt;,Qt::DropAction)" return-type="Qt::DropAction">
        <inject-code file="../glue/qtgui.cpp" snippet="qdrag-exec-arg2"/>
    </add-function>
  </object-type>

  <object-type name="QStandardItemModel" polymorphic-id-expression="qobject_cast&lt;QStandardItemModel*&gt;(%B)">
    <extra-includes>
      <include file-name="QStringList" location="global"/>
      <include file-name="QSize" location="global"/>
    </extra-includes>
    <modify-function signature="takeItem(int,int)">
      <modify-argument index="return">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="takeHorizontalHeaderItem(int)">
      <modify-argument index="return">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="takeVerticalHeaderItem(int)">
      <modify-argument index="return">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="verticalHeaderItem(int)const">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="horizontalHeaderItem(int)const">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="invisibleRootItem()const">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="item(int,int)const">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="itemFromIndex(const QModelIndex&amp;)const">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="itemPrototype()const">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="appendRow(const QList&lt;QStandardItem*&gt;&amp;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="appendRow(QStandardItem*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="insertRow(int,QStandardItem*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setHorizontalHeaderItem(int,QStandardItem*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="setItem(int,int,QStandardItem*)">
      <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qstandarditemmodel-setitem-1"/>
      <modify-argument index="3">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setItem(int,QStandardItem*)">
      <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qstandarditemmodel-setitem-2"/>
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setItemPrototype(const QStandardItem*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="setVerticalHeaderItem(int,QStandardItem*)">
      <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qstandarditemmodel-setverticalheaderitem"/>
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="takeColumn(int)">
      <modify-argument index="return">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="takeRow(int)">
      <modify-argument index="return">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="findItems(const QString&amp;,QFlags&lt;Qt::MatchFlag&gt;,int)const">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="insertColumn(int,const QList&lt;QStandardItem*&gt;&amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="insertRow(int,const QList&lt;QStandardItem*&gt;&amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="appendColumn(const QList&lt;QStandardItem*&gt;&amp;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="clear()">
        <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qstandarditemmodel-clear"/>
    </modify-function>
  </object-type>
  <object-type name="QClipboard">
    <extra-includes>
      <include file-name="QImage" location="global"/>
      <include file-name="QPixmap" location="global"/>
    </extra-includes>
    <enum-type name="Mode"/>
    <modify-function signature="setPixmap(const QPixmap&amp;, QClipboard::Mode)">
      <modify-argument index="1" pyi-type="PySide6.QtGui.QPixmap"/>
    </modify-function>
    <add-function signature="setPixmap(PyPathLike@path@)">
      <inject-code file="../glue/qtgui.cpp" snippet="qclipboard-setpixmap"/>
    </add-function>
    <modify-function signature="setImage(const QImage&amp;, QClipboard::Mode)">
      <modify-argument index="1" pyi-type="PySide6.QtGui.QImage"/>
    </modify-function>
    <add-function signature="setImage(PyPathLike@path@)">
      <inject-code file="../glue/qtgui.cpp" snippet="qclipboard-setimage"/>
    </add-function>
    <modify-function signature="setMimeData(QMimeData*,QClipboard::Mode)" allow-thread="yes">
      <modify-argument index="1">
        <!-- TODO: maybe this is not the best solution -->
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="text(QString&amp;,QClipboard::Mode)const">
      <modify-argument index="return" pyi-type="Tuple[str, PySide6.QtGui.QClipboard.Mode]">
        <replace-type modified-type="(retval, subtype)"/>
      </modify-argument>
      <inject-code class="target" position="end" file="../glue/qtgui.cpp" snippet="qclipboard-text"/>
    </modify-function>
    <modify-function signature="mimeData(QClipboard::Mode)const">
      <modify-argument index="return">
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QPaintEngineState">
    <extra-includes>
      <include file-name="QPainterPath" location="global"/>
    </extra-includes>
  </object-type>
  <object-type name="QTextLayout">
    <extra-includes>
      <include file-name="QTextOption" location="global"/>
    </extra-includes>
    <enum-type name="CursorMode"/>
    <enum-type name="GlyphRunRetrievalFlag" flags="GlyphRunRetrievalFlags" since="6.5"/>
    <value-type name="FormatRange">
      <include file-name="QTextLayout" location="global"/>
    </value-type>
  </object-type>
  <object-type name="QTextDocument">
    <extra-includes>
      <include file-name="QTextBlock" location="global"/>
      <include file-name="QTextFormat" location="global"/>
      <include file-name="QTextCursor" location="global"/>
    </extra-includes>
    <enum-type name="FindFlag" flags="FindFlags"/>
    <enum-type name="MarkdownFeature" flags="MarkdownFeatures"/>
    <enum-type name="MetaInformation"/>
    <enum-type name="ResourceType" python-type="IntEnum"/>
    <enum-type name="Stacks"/>
    <modify-function signature="setDocumentLayout(QAbstractTextDocumentLayout*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="object(int)const">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="objectForFormat(const QTextFormat&amp;)const">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="createObject(const QTextFormat&amp;)">
      <modify-argument index="return">
        <define-ownership class="native" owner="c++"/>
        <define-ownership class="target" owner="target"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="print(QPagedPaintDevice*)const" allow-thread="yes" rename="print_"/>
  </object-type>
  <object-type name="QTextDocumentWriter"/>
  <object-type name="QTextTable">
    <extra-includes>
      <include file-name="QTextCursor" location="global"/>
    </extra-includes>
  </object-type>
  <object-type name="QPaintEngine">
    <enum-type name="DirtyFlag" flags="DirtyFlags"/>
    <enum-type name="PaintEngineFeature" flags="PaintEngineFeatures"/>
    <enum-type name="PolygonDrawMode"/>
    <enum-type name="Type"/>

    <modify-function signature="drawLines(const QLine*,int)">
      <modify-argument index="1">
        <replace-type modified-type="PySequence"/>
        <conversion-rule class="native">
          <insert-template name="pysequence-to-c-array"> <!-- Binding -->
            <replace from="%TYPE" to="QLine"/>
          </insert-template>
        </conversion-rule>
        <conversion-rule class="target"> <!-- Virtual override -->
          <insert-template name="c-array-to-pysequence">
            <replace from="%TYPE" to="QLine"/>
            <replace from="%COUNT" to="lineCount"/>
          </insert-template>
        </conversion-rule>
      </modify-argument>
      <modify-argument index="2">
        <remove-argument/>
        <conversion-rule class="native"> <!-- Provide parameter for binding -->
          <insert-template name="pysequencesize_int"/>
        </conversion-rule>
      </modify-argument>
    </modify-function>

    <modify-function signature="drawLines(const QLineF*,int)">
      <modify-argument index="1">
        <replace-type modified-type="PySequence"/>
        <conversion-rule class="native">
          <insert-template name="pysequence-to-c-array"> <!-- Binding -->
            <replace from="%TYPE" to="QLineF"/>
          </insert-template>
        </conversion-rule>
        <conversion-rule class="target"> <!-- Virtual override -->
          <insert-template name="c-array-to-pysequence">
            <replace from="%TYPE" to="QLineF"/>
            <replace from="%COUNT" to="lineCount"/>
          </insert-template>
        </conversion-rule>
      </modify-argument>
      <modify-argument index="2">
        <remove-argument/>
        <conversion-rule class="native"> <!-- Provide parameter for binding -->
          <insert-template name="pysequencesize_int"/>
        </conversion-rule>
      </modify-argument>
    </modify-function>

    <modify-function signature="drawPoints(const QPoint*,int)">
      <modify-argument index="1">
        <replace-type modified-type="PySequence"/>
        <conversion-rule class="native">
          <insert-template name="pysequence-to-c-array"> <!-- Binding -->
            <replace from="%TYPE" to="QPoint"/>
          </insert-template>
        </conversion-rule>
        <conversion-rule class="target"> <!-- Virtual override -->
          <insert-template name="c-array-to-pysequence">
            <replace from="%TYPE" to="QPoint"/>
            <replace from="%COUNT" to="pointCount"/>
          </insert-template>
        </conversion-rule>
      </modify-argument>
      <modify-argument index="2">
        <remove-argument/>
        <conversion-rule class="native"> <!-- Provide parameter for binding -->
          <insert-template name="pysequencesize_int"/>
        </conversion-rule>
      </modify-argument>
    </modify-function>

    <modify-function signature="drawPoints(const QPointF*,int)">
      <modify-argument index="1">
        <replace-type modified-type="PySequence"/>
        <conversion-rule class="native">
          <insert-template name="pysequence-to-c-array"> <!-- Binding -->
            <replace from="%TYPE" to="QPointF"/>
          </insert-template>
        </conversion-rule>
        <conversion-rule class="target"> <!-- Virtual override -->
          <insert-template name="c-array-to-pysequence">
            <replace from="%TYPE" to="QPointF"/>
            <replace from="%COUNT" to="pointCount"/>
          </insert-template>
        </conversion-rule>
      </modify-argument>
      <modify-argument index="2">
        <remove-argument/>
        <conversion-rule class="native"> <!-- Provide parameter for binding -->
          <insert-template name="pysequencesize_int"/>
        </conversion-rule>
      </modify-argument>
    </modify-function>

    <modify-function signature="drawPolygon(const QPoint*,int,QPaintEngine::PolygonDrawMode)">
      <modify-argument index="1">
        <replace-type modified-type="PySequence"/>
        <conversion-rule class="native">
          <insert-template name="pysequence-to-c-array"> <!-- Binding -->
            <replace from="%TYPE" to="QPoint"/>
          </insert-template>
        </conversion-rule>
        <conversion-rule class="target"> <!-- Virtual override -->
          <insert-template name="c-array-to-pysequence">
            <replace from="%TYPE" to="QPoint"/>
            <replace from="%COUNT" to="pointCount"/>
          </insert-template>
        </conversion-rule>
      </modify-argument>
      <modify-argument index="2">
        <remove-argument/>
        <conversion-rule class="native"> <!-- Provide parameter for binding -->
          <insert-template name="pysequencesize_int"/>
        </conversion-rule>
      </modify-argument>
    </modify-function>

    <modify-function signature="drawPolygon(const QPointF*,int,QPaintEngine::PolygonDrawMode)">
      <modify-argument index="1">
        <replace-type modified-type="PySequence"/>
        <conversion-rule class="native">
          <insert-template name="pysequence-to-c-array"> <!-- Binding -->
            <replace from="%TYPE" to="QPointF"/>
          </insert-template>
        </conversion-rule>
        <conversion-rule class="target"> <!-- Virtual override -->
          <insert-template name="c-array-to-pysequence">
            <replace from="%TYPE" to="QPointF"/>
            <replace from="%COUNT" to="pointCount"/>
          </insert-template>
        </conversion-rule>
      </modify-argument>
      <modify-argument index="2">
        <remove-argument/>
        <conversion-rule class="native"> <!-- Provide parameter for binding -->
          <insert-template name="pysequencesize_int"/>
        </conversion-rule>
      </modify-argument>
    </modify-function>

    <modify-function signature="drawRects(const QRect*,int)">
      <modify-argument index="1">
        <replace-type modified-type="PySequence"/>
        <conversion-rule class="native">
          <insert-template name="pysequence-to-c-array"> <!-- Binding -->
            <replace from="%TYPE" to="QRect"/>
          </insert-template>
        </conversion-rule>
        <conversion-rule class="target"> <!-- Virtual override -->
          <insert-template name="c-array-to-pysequence">
            <replace from="%TYPE" to="QRect"/>
            <replace from="%COUNT" to="rectCount"/>
          </insert-template>
        </conversion-rule>
      </modify-argument>
      <modify-argument index="2">
        <remove-argument/>
        <conversion-rule class="native"> <!-- Provide parameter for binding -->
          <insert-template name="pysequencesize_int"/>
        </conversion-rule>
      </modify-argument>
    </modify-function>

    <modify-function signature="drawRects(const QRectF*,int)">
      <modify-argument index="1">
        <replace-type modified-type="PySequence"/>
        <conversion-rule class="native">
          <insert-template name="pysequence-to-c-array"> <!-- Binding -->
            <replace from="%TYPE" to="QRectF"/>
          </insert-template>
        </conversion-rule>
        <conversion-rule class="target"> <!-- Virtual override -->
          <insert-template name="c-array-to-pysequence">
            <replace from="%TYPE" to="QRectF"/>
            <replace from="%COUNT" to="rectCount"/>
          </insert-template>
        </conversion-rule>
      </modify-argument>
      <modify-argument index="2">
        <remove-argument/>
        <conversion-rule class="native"> <!-- Provide parameter for binding -->
          <insert-template name="pysequencesize_int"/>
        </conversion-rule>
      </modify-argument>
    </modify-function>

    <modify-function signature="begin(QPaintDevice*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="updateState(QPaintEngineState)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="drawTextItem(QPointF,QTextItem)">
      <modify-argument index="2" invalidate-after-use="yes"/>
    </modify-function>
    <extra-includes>
      <include file-name="QVarLengthArray" location="global"/>
    </extra-includes>
    <!-- ### "setPaintDevice(QPaintDevice*)" is an internal method. -->
    <modify-function signature="setPaintDevice(QPaintDevice*)" remove="all"/>
  </object-type>

  <object-type name="QPainter">
    <extra-includes>
      <include file-name="QPainterPath" location="global"/>
      <include file-name="QPixmap" location="global"/>
      <include file-name="pyside_numpy.h" location="global"/>
    </extra-includes>
    <enum-type name="CompositionMode"/>
    <enum-type name="PixmapFragmentHint" flags="PixmapFragmentHints"/>
    <enum-type name="RenderHint" flags="RenderHints"/>
    <value-type name="PixmapFragment">
        <include file-name="QPainter" location="global"/>
    </value-type>

    <!-- ### "drawText(...)" is an internal method. -->
    <modify-function signature="drawText(const QPointF&amp;,const QString&amp;,int,int)" remove="all"/>


    <modify-function signature="drawConvexPolygon(const QPoint*,int)" remove="all"/>
    <add-function signature="drawConvexPolygon(QList&lt;QPoint>@points@)">
        <inject-code>
            <insert-template name="qpainter_drawlist"/>
        </inject-code>
    </add-function>
    <modify-function signature="drawConvexPolygon(const QPointF*,int)" remove="all"/>
    <add-function signature="drawConvexPolygon(QList&lt;QPointF>@points@)">
        <inject-code>
            <insert-template name="qpainter_drawlist"/>
        </inject-code>
    </add-function>

    <!-- PYSIDE-1540: Preferably use the QPolygon overloads first to avoid
         a costly sequence type check on QPolygon. -->
    <modify-function signature="drawPoints(const QPoint*,int)" remove="all"/>
    <add-function signature="drawPoints(QList&lt;QPoint>@points@)" overload-number="2">
        <inject-code>
            <insert-template name="qpainter_drawlist"/>
        </inject-code>
    </add-function>
    <add-function signature="drawPoints(QList&lt;QPointF>@points@)" overload-number="3">
        <inject-code>
            <insert-template name="qpainter_drawlist"/>
        </inject-code>
    </add-function>
    <modify-function signature="drawPoints(const QPolygon&amp;)" overload-number="0"/>
    <modify-function signature="drawPoints(const QPolygonF&amp;)" overload-number="1"/>
    <!-- Add numpy versions as separate functions since passing ndarrays to other
         typecheck macros causes:
         FIXME Subscripted generics cannot be used with class and instance checks -->
    <add-function signature="drawPointsNp(PyArrayObject *@x@, PyArrayObject *@y@)">
        <inject-code file="../glue/qtgui.cpp" snippet="qpainter-drawpointsnp-numpy-x-y"/>
        <inject-documentation format="target" mode="append">
        Draws the points specified by two one-dimensional, equally sized numpy arrays
        representing the x, y values, respectively.
        </inject-documentation>
    </add-function>

    <modify-function signature="drawPolygon(const QPoint*,int,Qt::FillRule)" remove="all"/>
    <add-function signature="drawPolygon(QList&lt;QPoint>@points@,Qt::FillRule@fill_rule@)">
        <inject-code file="../glue/qtgui.cpp" snippet="qpainter-drawpolygon"/>
    </add-function>
    <modify-function signature="drawPolygon(const QPointF*,int,Qt::FillRule)" remove="all"/>
    <add-function signature="drawPolygon(QList&lt;QPointF>@points@,Qt::FillRule@fill_rule@)">
        <inject-code file="../glue/qtgui.cpp" snippet="qpainter-drawpolygon"/>
    </add-function>
    <modify-function signature="drawPolyline(const QPoint*,int)" remove="all"/>
    <add-function signature="drawPolyline(QList&lt;QPoint>@points@)">
        <inject-code>
            <insert-template name="qpainter_drawlist"/>
        </inject-code>
    </add-function>
    <!-- PYSIDE-1366: Preferably use the QPolygon overloads first to avoid
         a costly sequence type check on QPolygon. -->
    <modify-function signature="drawPolyline(const QPointF*,int)" remove="all"/>
    <modify-function signature="drawPolyline(const QPolygon&amp;)" overload-number="0"/>
    <modify-function signature="drawPolyline(const QPolygonF&amp;)" overload-number="1"/>
    <add-function signature="drawPolyline(QList&lt;QPointF@points@>)">
        <inject-code>
            <insert-template name="qpainter_drawlist"/>
        </inject-code>
    </add-function>
    <modify-function signature="drawTiledPixmap(const QRect&amp;,const QPixmap&amp;,const QPoint&amp;)">
      <modify-argument index="3">
        <rename to="pos"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="QPainter(QPaintDevice*)">
      <modify-argument index="1">
        <no-null-pointer/>
      </modify-argument>
    </modify-function>
    <modify-function signature="begin(QPaintDevice*)">
      <modify-argument index="1">
        <no-null-pointer/>
      </modify-argument>
    </modify-function>
    <modify-function signature="drawText(QRectF,int,QString,QRectF*)">
      <modify-argument index="4">
        <remove-argument/>
        <remove-default-expression/>
      </modify-argument>
      <modify-argument index="return">
        <replace-type modified-type="QRectF"/>
      </modify-argument>
      <inject-code class="target" position="beginning">
        <insert-template name="fix_args,QRectF*"/>
      </inject-code>
      <inject-documentation format="target" mode="prepend">
      The function returns the bounding rectangle enclosing the whole text.
      </inject-documentation>
    </modify-function>
    <modify-function signature="drawText(QRect,int,QString,QRect*)">
      <modify-argument index="4">
        <remove-argument/>
        <remove-default-expression/>
      </modify-argument>
      <modify-argument index="return">
        <replace-type modified-type="QRect"/>
      </modify-argument>
      <inject-code class="target" position="beginning">
        <insert-template name="fix_args,QRect*"/>
      </inject-code>
      <inject-documentation format="target" mode="prepend">
      The function returns the bounding rectangle enclosing the whole text.
      </inject-documentation>
    </modify-function>
    <modify-function signature="drawText(int,int,int,int,int,QString,QRect*)">
      <modify-argument index="7">
        <remove-argument/>
        <remove-default-expression/>
      </modify-argument>
      <inject-code class="target" position="beginning">
        <insert-template name="fix_args,QRect*"/>
      </inject-code>
      <inject-documentation format="target" mode="prepend">
      The function returns the bounding rectangle enclosing the whole text.
      </inject-documentation>
    </modify-function>
    <add-function signature="__enter__()" return-type="QPainter">
        <inject-code file="../glue/qtgui.cpp" snippet="qpainter-enter"/>
    </add-function>
    <add-function signature="__exit__(PyObject*,PyObject*,PyObject*)">
        <inject-code file="../glue/qtgui.cpp" snippet="qpainter-exit"/>
    </add-function>
  </object-type>

  <object-type name="QPainterStateGuard" since="6.9">
      <enum-type name="InitialState"/>
      <add-function signature="__enter__()" return-type="QPainterStateGuard">
          <inject-code file="../glue/qtcore.cpp" snippet="default-enter"/>
      </add-function>
      <add-function signature="__exit__(PyObject*,PyObject*,PyObject*)">
          <inject-code file="../glue/qtgui.cpp" snippet="qpainterstateguard-restore"/>
      </add-function>
  </object-type>

  <value-type name="QGenericMatrix" generate="no"/>
  <value-type name="QMatrix2x2">
    <modify-function signature="QMatrix2x2(const float*)" remove="all"/>
    <modify-function signature="copyDataTo(float*) const" remove="all"/>
    <modify-function signature="constData()const" remove="all"/>
    <add-function signature="__repr__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="repr_code_matrix">
               <replace from="%MATRIX_SIZE" to="4"/>
               <replace from="%MATRIX_TYPE" to="float"/>
             </insert-template>
        </inject-code>
    </add-function>

    <add-function signature="__reduce__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="reduce_code_matrix">
              <replace from="%MATRIX_TYPE" to="float"/>
              <replace from="%MATRIX_SIZE" to="4"/>
            </insert-template>
        </inject-code>
    </add-function>
    <add-function signature="QMatrix2x2(PySequence*@elements@)">
      <inject-code class="target" position="beginning">
        <insert-template name="matrix_constructor">
          <replace from="%SIZE" to="4"/>
        </insert-template>
      </inject-code>
    </add-function>
    <add-function signature="data()" return-type="float">
      <inject-code class="target" position="beginning">
        <insert-template name="matrix_data_function">
          <replace from="%MATRIX_SIZE" to="4"/>
        </insert-template>
      </inject-code>
    </add-function>
  </value-type>

  <value-type name="QMatrix2x3">
    <modify-function signature="QMatrix2x3(const float*)" remove="all"/>
    <modify-function signature="copyDataTo(float*) const" remove="all"/>
    <modify-function signature="constData()const" remove="all"/>
    <add-function signature="__repr__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="repr_code_matrix">
               <replace from="%MATRIX_SIZE" to="6"/>
               <replace from="%MATRIX_TYPE" to="float"/>
             </insert-template>
        </inject-code>
    </add-function>

    <add-function signature="__reduce__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="reduce_code_matrix">
              <replace from="%MATRIX_TYPE" to="float"/>
              <replace from="%MATRIX_SIZE" to="6"/>
            </insert-template>
        </inject-code>
    </add-function>
    <add-function signature="QMatrix2x3(PySequence*@elements@)">
      <inject-code class="target" position="beginning">
        <insert-template name="matrix_constructor">
          <replace from="%SIZE" to="6"/>
        </insert-template>
      </inject-code>
    </add-function>
    <add-function signature="data()" return-type="float">
      <inject-code class="target" position="beginning">
        <insert-template name="matrix_data_function">
          <replace from="%MATRIX_SIZE" to="6"/>
        </insert-template>
      </inject-code>
    </add-function>
  </value-type>

  <value-type name="QMatrix2x4">
    <modify-function signature="QMatrix2x4(const float*)" remove="all"/>
    <modify-function signature="copyDataTo(float*) const" remove="all"/>
    <modify-function signature="constData()const" remove="all"/>
    <add-function signature="__repr__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="repr_code_matrix">
               <replace from="%MATRIX_SIZE" to="8"/>
               <replace from="%MATRIX_TYPE" to="float"/>
             </insert-template>
        </inject-code>
    </add-function>

    <add-function signature="__reduce__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="reduce_code_matrix">
              <replace from="%MATRIX_TYPE" to="float"/>
              <replace from="%MATRIX_SIZE" to="8"/>
            </insert-template>
        </inject-code>
    </add-function>
    <add-function signature="QMatrix2x4(PySequence*@elements@)">
      <inject-code class="target" position="beginning">
        <insert-template name="matrix_constructor">
          <replace from="%SIZE" to="8"/>
        </insert-template>
      </inject-code>
    </add-function>
    <add-function signature="data()" return-type="float">
      <inject-code class="target" position="beginning">
        <insert-template name="matrix_data_function">
          <replace from="%MATRIX_SIZE" to="8"/>
        </insert-template>
      </inject-code>
    </add-function>
  </value-type>

  <value-type name="QMatrix3x2">
    <modify-function signature="QMatrix3x2(const float*)" remove="all"/>
    <modify-function signature="copyDataTo(float*) const" remove="all"/>
    <modify-function signature="constData()const" remove="all"/>
    <add-function signature="__repr__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="repr_code_matrix">
               <replace from="%MATRIX_SIZE" to="6"/>
               <replace from="%MATRIX_TYPE" to="float"/>
             </insert-template>
        </inject-code>
    </add-function>

    <add-function signature="__reduce__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="reduce_code_matrix">
              <replace from="%MATRIX_TYPE" to="float"/>
              <replace from="%MATRIX_SIZE" to="6"/>
            </insert-template>
        </inject-code>
    </add-function>
    <add-function signature="QMatrix3x2(PySequence*@elements@)">
      <inject-code class="target" position="beginning">
        <insert-template name="matrix_constructor">
          <replace from="%SIZE" to="6"/>
        </insert-template>
      </inject-code>
    </add-function>
    <add-function signature="data()" return-type="float">
      <inject-code class="target" position="beginning">
        <insert-template name="matrix_data_function">
          <replace from="%MATRIX_SIZE" to="6"/>
        </insert-template>
      </inject-code>
    </add-function>
  </value-type>

  <value-type name="QMatrix3x3">
    <modify-function signature="QMatrix3x3(const float*)" remove="all"/>
    <modify-function signature="copyDataTo(float*) const" remove="all"/>
    <modify-function signature="constData()const" remove="all"/>
    <add-function signature="__repr__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="repr_code_matrix">
               <replace from="%MATRIX_SIZE" to="9"/>
               <replace from="%MATRIX_TYPE" to="float"/>
             </insert-template>
        </inject-code>
    </add-function>

    <add-function signature="__reduce__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="reduce_code_matrix">
              <replace from="%MATRIX_TYPE" to="float"/>
              <replace from="%MATRIX_SIZE" to="9"/>
            </insert-template>
        </inject-code>
    </add-function>
    <add-function signature="QMatrix3x3(PySequence*@elements@)">
      <inject-code class="target" position="beginning">
        <insert-template name="matrix_constructor">
          <replace from="%SIZE" to="9"/>
        </insert-template>
      </inject-code>
    </add-function>
    <add-function signature="data()" return-type="float">
      <inject-code class="target" position="beginning">
        <insert-template name="matrix_data_function">
          <replace from="%MATRIX_SIZE" to="9"/>
        </insert-template>
      </inject-code>
    </add-function>
  </value-type>

  <value-type name="QMatrix3x4">
    <modify-function signature="QMatrix3x4(const float*)" remove="all"/>
    <modify-function signature="copyDataTo(float*) const" remove="all"/>
    <modify-function signature="constData()const" remove="all"/>
    <add-function signature="__repr__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="repr_code_matrix">
               <replace from="%MATRIX_SIZE" to="12"/>
               <replace from="%MATRIX_TYPE" to="float"/>
             </insert-template>
        </inject-code>
    </add-function>

    <add-function signature="__reduce__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="reduce_code_matrix">
              <replace from="%MATRIX_TYPE" to="float"/>
              <replace from="%MATRIX_SIZE" to="12"/>
            </insert-template>
        </inject-code>
    </add-function>
    <add-function signature="QMatrix3x4(PySequence*@elements@)">
      <inject-code class="target" position="beginning">
        <insert-template name="matrix_constructor">
          <replace from="%SIZE" to="12"/>
        </insert-template>
      </inject-code>
    </add-function>
    <add-function signature="data()" return-type="float">
      <inject-code class="target" position="beginning">
        <insert-template name="matrix_data_function">
          <replace from="%MATRIX_SIZE" to="12"/>
        </insert-template>
      </inject-code>
    </add-function>
  </value-type>

  <value-type name="QMatrix4x2">
    <modify-function signature="QMatrix4x2(const float*)" remove="all"/>
    <modify-function signature="copyDataTo(float*) const" remove="all"/>
    <modify-function signature="constData()const" remove="all"/>
    <add-function signature="__repr__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="repr_code_matrix">
               <replace from="%MATRIX_SIZE" to="8"/>
               <replace from="%MATRIX_TYPE" to="float"/>
             </insert-template>
        </inject-code>
    </add-function>

    <add-function signature="__reduce__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="reduce_code_matrix">
              <replace from="%MATRIX_TYPE" to="float"/>
              <replace from="%MATRIX_SIZE" to="8"/>
            </insert-template>
        </inject-code>
    </add-function>
    <add-function signature="QMatrix4x2(PySequence*@elements@)">
      <inject-code class="target" position="beginning">
        <insert-template name="matrix_constructor">
          <replace from="%SIZE" to="8"/>
        </insert-template>
      </inject-code>
    </add-function>
    <add-function signature="data()" return-type="float">
      <inject-code class="target" position="beginning">
        <insert-template name="matrix_data_function">
          <replace from="%MATRIX_SIZE" to="8"/>
        </insert-template>
      </inject-code>
    </add-function>
  </value-type>

  <value-type name="QMatrix4x3">
    <modify-function signature="QMatrix4x3(const float*)" remove="all"/>
    <modify-function signature="copyDataTo(float*) const" remove="all"/>
    <modify-function signature="constData()const" remove="all"/>
    <add-function signature="__repr__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="repr_code_matrix">
               <replace from="%MATRIX_SIZE" to="12"/>
               <replace from="%MATRIX_TYPE" to="float"/>
             </insert-template>
        </inject-code>
    </add-function>

    <add-function signature="__reduce__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="reduce_code_matrix">
              <replace from="%MATRIX_TYPE" to="float"/>
              <replace from="%MATRIX_SIZE" to="12"/>
            </insert-template>
        </inject-code>
    </add-function>
    <add-function signature="QMatrix4x3(PySequence*@elements@)">
      <inject-code class="target" position="beginning">
        <insert-template name="matrix_constructor">
          <replace from="%SIZE" to="12"/>
        </insert-template>
      </inject-code>
    </add-function>
    <add-function signature="data()" return-type="float">
      <inject-code class="target" position="beginning">
        <insert-template name="matrix_data_function">
          <replace from="%MATRIX_SIZE" to="12"/>
        </insert-template>
      </inject-code>
    </add-function>
  </value-type>

  <value-type name="QMatrix4x4">
    <enum-type name="Flag" flags="Flags"/>

    <!-- Qt5: HAIRY TRICK ALERT ahead!
        Qt5 partially replaced 'qreal' by float.
        That had the side effect that all matrix types did not work any longer.

        Reason (I guess):

        The signature "QList<qreal>" is needed by the __reduce__ methods,
        but created by some other object used elsewhere.
        After the matrix type was changed, "QList<float>" was created nowhere.

        I don't know an explicit way to produce the right conversion function, so what I did
        was to create a dummy function and immediately delete it again.
        This has the desired effect of creating the implicitly needed "QList<float>"
        conversion, although the dummy function goes away.

        Q: Do you know a better solution?
    -->
    <add-function signature="__dummy(const QList&lt;float &gt; &amp;)"/>
    <modify-function signature="__dummy(const QList&lt;float &gt; &amp;)" remove="all"/>
    <!-- that was the trick ^^^^^^^^^^^^^^^^^^^^^^ -->

    <add-function signature="__repr__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="repr_code_matrix">
               <replace from="%MATRIX_SIZE" to="16"/>
               <replace from="%MATRIX_TYPE" to="float"/>
             </insert-template>
        </inject-code>
    </add-function>

    <add-function signature="__reduce__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="reduce_code_matrix">
              <replace from="%MATRIX_TYPE" to="float"/>
              <replace from="%MATRIX_SIZE" to="16"/>
            </insert-template>
        </inject-code>
    </add-function>

    <!-- ### "QMatrix4x4(const float*,int,int)" is an internal constructor. -->
    <modify-function signature="QMatrix4x4(const float*,int,int)" remove="all"/>

    <modify-function signature="QMatrix4x4(const float*)">
      <modify-argument index="1">
        <replace-type modified-type="PySequence"/>
        <array/>
      </modify-argument>
      <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qmatrix4x4"/>
    </modify-function>
    <modify-function signature="data()">
      <inject-code class="target" position="beginning">
        <insert-template name="matrix_data_function">
          <replace from="%MATRIX_SIZE" to="16"/>
        </insert-template>
      </inject-code>
    </modify-function>
    <modify-function signature="copyDataTo(float*)const">
      <modify-argument index="1">
        <remove-argument/>
      </modify-argument>
      <modify-argument index="return">
        <replace-type modified-type="Tuple"/>
      </modify-argument>
      <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qmatrix4x4-copydatato"/>
    </modify-function>

    <modify-function signature="inverted(bool*)const">
      <modify-argument index="1">
        <remove-argument/>
      </modify-argument>
      <modify-argument index="return" pyi-type="Tuple[PySide6.QtGui.QMatrix4x4, bool]">
        <replace-type modified-type="PyTuple"/>
      </modify-argument>
      <inject-code class="target" position="beginning">
        <insert-template name="fix_bool*"/>
      </inject-code>
    </modify-function>

    <!-- ### "constData() const" and "data() const" are unnecessary in Python and their function is performed by "data()". -->
    <modify-function signature="data()const" remove="all"/>
    <modify-function signature="constData()const" remove="all"/>
    <!-- ### -->
    <modify-function signature="operator()(int,int)const" remove="all"/>
    <modify-function signature="operator()(int,int)" remove="all"/>
    <add-function signature="__mgetitem__" return-type="PyObject*">
       <inject-code file="../glue/qtgui.cpp" snippet="qmatrix4x4-mgetitem"/>
    </add-function>
  </value-type>

    _______ end of matrix block _______ -->

  <value-type name="QQuaternion">
    <add-function signature="__repr__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="repr_code">
               <replace from="%REPR_FORMAT" to="%f, %f, %f, %f"/>
               <replace from="%REPR_ARGS" to="%CPPSELF.scalar(), %CPPSELF.x(), %CPPSELF.y(), %CPPSELF.z()"/>
             </insert-template>
        </inject-code>
    </add-function>

    <add-function signature="__reduce__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="reduce_code">
              <replace from="%REDUCE_FORMAT" to="dddd"/>
              <replace from="%REDUCE_ARGS" to="%CPPSELF.scalar(), %CPPSELF.x(), %CPPSELF.y(), %CPPSELF.z()"/>
            </insert-template>
        </inject-code>
    </add-function>

    <!-- There can be only one return type.  -->
    <modify-function signature="getAxisAndAngle(float*,float*,float*,float*)const" remove="all"/>
    <modify-function signature="getAxisAndAngle(QVector3D*,float*)const">
        <modify-argument index="return" pyi-type="Tuple[PySide6.QtGui.QVector3D, float]">
            <replace-type modified-type="(QVector3D, float)"/>
        </modify-argument>
        <modify-argument index="1"><remove-argument/></modify-argument>
        <modify-argument index="2"><remove-argument/></modify-argument>
        <inject-code class="target" position="beginning"
                     file="../glue/qtgui.cpp" snippet="qquaternion-getaxisandangle-vector3d-float"/>
    </modify-function>
    <modify-function signature="getEulerAngles(float*,float*,float*)const">
        <modify-argument index="return" pyi-type="Tuple[float, float, float]">
            <replace-type modified-type="(float, float, float)"/>
        </modify-argument>
        <modify-argument index="1"><remove-argument/></modify-argument>
        <modify-argument index="2"><remove-argument/></modify-argument>
        <modify-argument index="3"><remove-argument/></modify-argument>
        <inject-code class="target" position="beginning"
                     file="../glue/qtgui.cpp" snippet="qquaternion-geteulerangles"/>
    </modify-function>
  </value-type>

  <object-type name="QTouchEvent">
      <add-function signature="__repr__" return-type="str">
          <inject-code class="target" position="beginning">
              <insert-template name="repr_qdebug_gui"/>
          </inject-code>
      </add-function>
  </object-type>

  <object-type name="QInputDevice">
    <enum-type name="Capability" flags="Capabilities"/>
    <enum-type name="DeviceType" flags="DeviceTypes"/>
  </object-type>

  <value-type name="QVector2D">
    <add-function signature="__repr__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="repr_code">
               <replace from="%REPR_FORMAT" to="%f, %f"/>
               <replace from="%REPR_ARGS" to="%CPPSELF.x(), %CPPSELF.y()"/>
             </insert-template>
        </inject-code>
    </add-function>

    <add-function signature="__reduce__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="reduce_code">
              <replace from="%REDUCE_FORMAT" to="dd"/>
              <replace from="%REDUCE_ARGS" to="%CPPSELF.x(), %CPPSELF.y()"/>
            </insert-template>
        </inject-code>
    </add-function>

    <add-function signature="toTuple" return-type="PyObject*">
        <inject-code class="target" position="beginning">
            <insert-template name="to_tuple">
                <replace from="%TT_FORMAT" to="dd"/>
                <replace from="%TT_ARGS" to="%CPPSELF.x(), %CPPSELF.y()"/>
            </insert-template>
        </inject-code>
    </add-function>

  </value-type>
  <value-type name="QVector3D">
    <extra-includes>
      <include file-name="QMatrix4x4" location="global"/>
    </extra-includes>
    <add-function signature="__repr__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="repr_code">
               <replace from="%REPR_FORMAT" to="%f, %f, %f"/>
               <replace from="%REPR_ARGS" to="%CPPSELF.x(), %CPPSELF.y(), %CPPSELF.z()"/>
             </insert-template>
        </inject-code>
    </add-function>

    <add-function signature="__reduce__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="reduce_code">
              <replace from="%REDUCE_FORMAT" to="ddd"/>
              <replace from="%REDUCE_ARGS" to="%CPPSELF.x(), %CPPSELF.y(), %CPPSELF.z()"/>
            </insert-template>
        </inject-code>
    </add-function>

    <add-function signature="toTuple" return-type="PyObject*">
        <inject-code class="target" position="beginning">
            <insert-template name="to_tuple">
                <replace from="%TT_FORMAT" to="ddd"/>
                <replace from="%TT_ARGS" to="%CPPSELF.x(), %CPPSELF.y(), %CPPSELF.z()"/>
            </insert-template>
        </inject-code>
    </add-function>

  </value-type>
  <value-type name="QVector4D">
    <extra-includes>
      <include file-name="QMatrix4x4" location="global"/>
    </extra-includes>
    <add-function signature="__repr__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="repr_code">
               <replace from="%REPR_FORMAT" to="%f, %f, %f, %f"/>
               <replace from="%REPR_ARGS" to="%CPPSELF.x(), %CPPSELF.y(), %CPPSELF.z(), %CPPSELF.w()"/>
             </insert-template>
        </inject-code>
    </add-function>

    <add-function signature="__reduce__" return-type="str">
        <inject-code class="target" position="beginning">
            <insert-template name="reduce_code">
              <replace from="%REDUCE_FORMAT" to="dddd"/>
              <replace from="%REDUCE_ARGS" to="%CPPSELF.x(), %CPPSELF.y(), %CPPSELF.z(), %CPPSELF.w()"/>
            </insert-template>
        </inject-code>
    </add-function>

    <add-function signature="toTuple" return-type="PyObject*">
        <inject-code class="target" position="beginning">
            <insert-template name="to_tuple">
                <replace from="%TT_FORMAT" to="dddd"/>
                <replace from="%TT_ARGS" to="%CPPSELF.x(), %CPPSELF.y(), %CPPSELF.z(), %CPPSELF.w()"/>
            </insert-template>
        </inject-code>
    </add-function>
  </value-type>

  <object-type name="QSessionManager">
    <extra-includes>
    </extra-includes>
    <enum-type name="RestartHint"/>
     <!-- ### -->
  </object-type>

  <!-- Qt5: here the new QWindow stuff and what it pulls in -->
  <object-type name="QBackingStore"/>
  <object-type name="QSurface">
    <enum-type name="SurfaceClass"/>
    <enum-type name="SurfaceType"/>
  </object-type>
  <rejection class="QSurface" field-name="m_reserved"/>
  <value-type name="QSurfaceFormat">
    <enum-type name="ColorSpace"/>
    <enum-type name="FormatOption" flags="FormatOptions"/>
    <enum-type name="OpenGLContextProfile"/>
    <enum-type name="RenderableType"/>
    <enum-type name="SwapBehavior"/>
  </value-type>
  <object-type name="QOffscreenSurface"/>
  <primitive-type name="QPlatformSurface">
    <extra-includes>
      <include file-name="QtGui/qpa/qplatformsurface.h" location="global"/>
    </extra-includes>
  </primitive-type>

  <object-type name="QWindow" delete-in-main-thread="true" polymorphic-base="true">
    <enum-type name="AncestorMode"/>
    <enum-type name="Visibility"/>
    <modify-function signature="raise()" rename="raise_"/>
    <!-- see QWidget::nativeEvent(), QAbstractNativeEventFilter::nativeEventFilter() -->
    <modify-function signature="nativeEvent(const QByteArray &amp;,void*,qintptr*)">
      <modify-argument index="3">
        <remove-argument/>
        <conversion-rule class="native">
            <insert-template name="return_native_eventfilter_conversion_variables"/>
        </conversion-rule>
      </modify-argument>
      <modify-argument index="return">
        <replace-type modified-type="PyObject"/>
        <conversion-rule class="native">
            <insert-template name="return_native_eventfilter_conversion"/>
        </conversion-rule>
      </modify-argument>
      <inject-code position="end"
                   file="../glue/qtcore.cpp" snippet="return-native-eventfilter"/>
    </modify-function>
    <modify-function signature="fromWinId(WId)">
      <modify-argument index="1">
          <replace-type modified-type="long"/>
      </modify-argument>
      <inject-code file="../glue/qtgui.cpp" snippet="qwindow-fromWinId"/>
    </modify-function>
    <modify-function signature="show()" allow-thread="yes"/>
    <modify-function signature="showFullScreen()" allow-thread="yes"/>
    <modify-function signature="showMaximized()" allow-thread="yes"/>
    <modify-function signature="showMinimized()" allow-thread="yes"/>
    <modify-function signature="showNormal()" allow-thread="yes"/>
  </object-type>

  <object-type name="QGuiApplication">
    <extra-includes>
      <include file-name="QBasicTimer" location="global"/>
      <include file-name="QFont" location="global"/>
      <include file-name="QFontMetrics" location="global"/>
      <include file-name="QPalette" location="global"/>
      <include file-name="QIcon" location="global"/>
      <include file-name="QLocale" location="global"/>
      <include file-name="pysideqapp.h" location="global"/>
      <include file-name="pysidecleanup.h" location="global"/>
      <!-- QOverrideCursorGuard -->
      <include file-name="qtguihelper.h" location="local"/>
    </extra-includes>
    <modify-function signature="QGuiApplication(int&amp;,char**,int)" access="private"/>
    <add-function signature="QGuiApplication(QStringList@arguments@)">
        <inject-code file="../glue/qtgui.cpp" snippet="qguiapplication-1"/>
    </add-function>
    <add-function signature="QGuiApplication()">
        <inject-code file="../glue/qtgui.cpp" snippet="qguiapplication-2"/>
    </add-function>
    <inject-code class="native" position="beginning" file="../glue/qtgui.cpp" snippet="qguiapplication-init"/>
    <modify-function signature="exec()" allow-thread="yes"/>
    <add-function signature="exec_()" return-type="int">
        <inject-code file="../glue/qtgui.cpp" snippet="qguiapplication-exec"/>
    </add-function>
    <add-function signature="nativeInterface()const" return-type="PyObject">
        <modify-argument index="return"> <!-- Suppress return value heuristics -->
            <define-ownership class="target" owner="default"/>
        </modify-argument>
        <inject-code class="target" position="beginning" file="../glue/qtgui.cpp"
                     snippet="qguiapplication-nativeInterface"/>
    </add-function>
    <modify-function signature="setOverrideCursor(const QCursor&amp;)">
        <modify-argument index="return" pyi-type="PyObject">
            <replace-type modified-type="QtGuiHelper::QOverrideCursorGuard*"/>
        </modify-argument>
        <inject-code class="target" position="end" file="../glue/qtgui.cpp"
                     snippet="qguiapplication-setoverridecursor"/>
    </modify-function>
  </object-type>

  <namespace-type name="QNativeInterface" private="yes" since="6.7">
      <object-type name="QX11Application" private="yes" disable-wrapper="yes"
                   force-abstract="yes">
          <configuration condition="QT_CONFIG(xcb)"/>
          <modify-function signature="display()const">
              <modify-argument index="return">
                  <replace-type modified-type="int"/>
              </modify-argument>
              <inject-code class="target" position="end" file="../glue/qtgui.cpp"
                           snippet="qx11application-resource-ptr"/>
          </modify-function>
          <modify-function signature="connection()const">
              <modify-argument index="return">
                  <replace-type modified-type="int"/>
              </modify-argument>
              <inject-code class="target" position="end" file="../glue/qtgui.cpp"
                           snippet="qx11application-resource-ptr"/>
          </modify-function>
      </object-type>
      <object-type name="QWindowsScreen" private="yes" disable-wrapper="yes"
                   force-abstract="yes">
          <configuration condition="#ifdef Q_OS_WIN"/>
      </object-type>
  </namespace-type>

  <object-type name="QOpenGLContext">
    <enum-type name="OpenGLModuleType"/>
  </object-type>
  <object-type name="QOpenGLContextGroup"/>
  <!-- Compile error on Windows: QOpenGLPaintDevice::QOpenGLPaintDevice(const QOpenGLPaintDevice &)': attempting to reference a deleted function
  <object-type name="QOpenGLPaintDevice"/>
  -->
  <object-type name="QOpenGLExtraFunctions">
      <extra-includes>
        <include file-name="QtCore/QVarLengthArray" location="global"/>
        <include file-name="sbkcpptonumpy.h" location="global"/>
      </extra-includes>
      <inject-code class="native" position="beginning" file="../glue/qtgui.cpp"
                          snippet="qopenglextrafunctions-glgeti-v-return-size"/>

      <!-- Exlusions due to compile errors -->
      <modify-function signature="glEndTransformFeedback()" remove="all"/>
      <modify-function signature="glPauseTransformFeedback()" remove="all"/>
      <modify-function signature="glResumeTransformFeedback()" remove="all"/>
      <modify-function signature="^glClearBuffer.*\(.*\*.*$">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glDebugMessageControl(GLenum,GLenum,GLenum,GLsizei,const GLuint*,GLboolean)">
          <modify-argument index="5"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glDeleteProgramPipelines(GLsizei,const GLuint*)">
          <modify-argument index="2"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glDeleteQueries(GLsizei,const GLuint*)">
          <modify-argument index="2"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glDeleteSamplers(GLsizei,const GLuint*)">
          <modify-argument index="2"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glDeleteTransformFeedbacks(GLsizei,const GLuint*)">
          <modify-argument index="2"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glDeleteVertexArrays(GLsizei,const GLuint*)">
          <modify-argument index="2"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glDrawBuffers(GLsizei,const GLenum*)">
          <modify-argument index="2"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGenProgramPipelines(GLsizei,GLuint*)">
          <modify-argument index="2"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGenQueries(GLsizei,GLuint*)">
          <modify-argument index="2"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGenSamplers(GLsizei,GLuint*)">
          <modify-argument index="2"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGenTransformFeedbacks(GLsizei,GLuint*)">
          <modify-argument index="2"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGenVertexArrays(GLsizei,GLuint*)">
          <modify-argument index="2"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetActiveUniformBlockiv(GLuint,GLuint,GLenum,GLint*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetActiveUniformsiv(GLuint,GLsizei,const GLuint*,GLenum,GLint*)">
          <modify-argument index="3"><array/></modify-argument>
          <modify-argument index="5"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetFramebufferParameteriv(GLenum,GLenum,GLint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetBooleani_v(GLenum,GLuint,GLboolean*)">
          <modify-argument index="return" pyi-type="Union[bool,numpy.ndarray]">
              <replace-type modified-type="PyObject"/>
          </modify-argument>
          <modify-argument index="3">
              <remove-argument/>
          </modify-argument>
          <inject-code class="target" position="beginning" file="../glue/qtgui.cpp"
                       snippet="qopenglextrafunctions-glgetbooleani-v"/>
      </modify-function>
      <!-- FIXME PYSIDE 7: Use the remove array modification and use
                           the version with return value instead -->
      <modify-function signature="glGetIntegeri_v(GLenum,GLuint,GLint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <add-function signature="glGetIntegeri_v(GLuint,GLuint)" return-type="PyObject">
          <modify-argument index="return" pyi-type="Union[int,numpy.ndarray]"/>
          <inject-code class="target" position="beginning" file="../glue/qtgui.cpp"
                       snippet="qopenglextrafunctions-glgetintegeri-v"/>
      </add-function>
      <modify-function signature="glGetInternalformativ(GLenum,GLenum,GLenum,GLsizei,GLint*)">
          <modify-argument index="5"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetMultisamplefv(GLenum,GLuint,GLfloat*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetProgramInterfaceiv(GLuint,GLenum,GLenum,GLint*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetProgramPipelineiv(GLuint,GLenum,GLint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetProgramResourceiv(GLuint,GLenum,GLuint,GLsizei,const GLenum*,GLsizei,GLsizei*,GLint*)">
          <modify-argument index="5"><array/></modify-argument>
          <modify-argument index="7"><array/></modify-argument>
          <modify-argument index="8"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetQueryObjectuiv(GLuint,GLenum,GLuint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetQueryiv(GLenum,GLenum,GLint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetSamplerParameterfv(GLuint,GLenum,GLfloat*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetSamplerParameteriv(GLuint,GLenum,GLint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetSamplerParameterIiv(GLuint,GLenum,GLint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetSamplerParameterIuiv(GLuint,GLenum,GLuint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetTexLevelParameteriv(GLenum,GLint,GLenum,GLint*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetTexLevelParameterfv(GLenum,GLint,GLenum,GLfloat*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetTexParameterIiv(GLenum,GLenum,GLint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetTexParameterIuiv(GLenum,GLenum,GLuint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetnUniformiv(GLuint,GLint,GLsizei,GLint*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetnUniformuiv(GLuint,GLint,GLsizei,GLuint*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetUniformuiv(GLuint,GLint,GLuint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetVertexAttribIiv(GLuint,GLenum,GLint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetVertexAttribIuiv(GLuint,GLenum,GLuint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glInvalidateFramebuffer(GLenum,GLsizei,const GLenum*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glInvalidateSubFramebuffer(GLenum,GLsizei,const GLenum*,GLint,GLint,GLsizei,GLsizei)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniform1fv(GLuint,GLint,GLsizei,const GLfloat*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniform1iv(GLuint,GLint,GLsizei,const GLint*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniform1uiv(GLuint,GLint,GLsizei,const GLuint*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniform2fv(GLuint,GLint,GLsizei,const GLfloat*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniform2iv(GLuint,GLint,GLsizei,const GLint*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniform2uiv(GLuint,GLint,GLsizei,const GLuint*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniform3fv(GLuint,GLint,GLsizei,const GLfloat*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniform3iv(GLuint,GLint,GLsizei,const GLint*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniform3uiv(GLuint,GLint,GLsizei,const GLuint*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniform4fv(GLuint,GLint,GLsizei,const GLfloat*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniform4iv(GLuint,GLint,GLsizei,const GLint*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniform4uiv(GLuint,GLint,GLsizei,const GLuint*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniformMatrix2fv(GLuint,GLint,GLsizei,GLboolean,const GLfloat*)">
          <modify-argument index="5"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniformMatrix2x3fv(GLuint,GLint,GLsizei,GLboolean,const GLfloat*)">
          <modify-argument index="5"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniformMatrix2x4fv(GLuint,GLint,GLsizei,GLboolean,const GLfloat*)">
          <modify-argument index="5"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniformMatrix3fv(GLuint,GLint,GLsizei,GLboolean,const GLfloat*)">
          <modify-argument index="5"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniformMatrix3x2fv(GLuint,GLint,GLsizei,GLboolean,const GLfloat*)">
          <modify-argument index="5"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniformMatrix3x4fv(GLuint,GLint,GLsizei,GLboolean,const GLfloat*)">
          <modify-argument index="5"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniformMatrix4fv(GLuint,GLint,GLsizei,GLboolean,const GLfloat*)">
          <modify-argument index="5"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniformMatrix4x2fv(GLuint,GLint,GLsizei,GLboolean,const GLfloat*)">
          <modify-argument index="5"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glProgramUniformMatrix4x3fv(GLuint,GLint,GLsizei,GLboolean,const GLfloat*)">
          <modify-argument index="5"><array/></modify-argument>
      </modify-function>
      <modify-function signature="^glSamplerParameterI?u?[fi]v\(.*$">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="^glTexParameterI?u?[fi]v\(.*$">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glUniform1uiv(GLint,GLsizei,const GLuint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glUniform2uiv(GLint,GLsizei,const GLuint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glUniform3uiv(GLint,GLsizei,const GLuint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glUniform4uiv(GLint,GLsizei,const GLuint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glUniformMatrix2x3fv(GLint,GLsizei,GLboolean,const GLfloat*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glUniformMatrix2x4fv(GLint,GLsizei,GLboolean,const GLfloat*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glUniformMatrix3x2fv(GLint,GLsizei,GLboolean,const GLfloat*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glUniformMatrix3x4fv(GLint,GLsizei,GLboolean,const GLfloat*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glUniformMatrix4x2fv(GLint,GLsizei,GLboolean,const GLfloat*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glUniformMatrix4x3fv(GLint,GLsizei,GLboolean,const GLfloat*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glVertexAttribI4iv(GLuint,const GLint*)">
          <modify-argument index="2"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glVertexAttribI4uiv(GLuint,const GLuint*)">
          <modify-argument index="2"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetStringi(GLenum,GLuint)">
          <modify-argument index="return">
              <replace-type modified-type="QString"/>
          </modify-argument>
          <inject-code class="target" position="end" file="../glue/qtgui.cpp"
                       snippet="glgetstring-return"/>
      </modify-function>
  </object-type>
  <object-type name="QOpenGLFunctions">
      <extra-includes>
        <include file-name="QtCore/QVarLengthArray" location="global"/>
        <include file-name="sbkcpptonumpy.h" location="global"/>
      </extra-includes>
      <inject-code class="native" position="beginning" file="../glue/qtgui.cpp"
                   snippet="qopenglfunctions-glgetv-return-size"/>

      <enum-type name="OpenGLFeature" flags="OpenGLFeatures"/>
      <add-function signature="glGetShaderSource(GLuint @shader@)" return-type="const char *">
          <inject-code class="target" position="beginning" file="../glue/qtgui.cpp"
                       snippet="glgetshadersource"/>
      </add-function>
      <add-function signature="glShaderSource(GLuint @shader@,const QString &amp; @source@)">
          <inject-code class="target" position="beginning" file="../glue/qtgui.cpp"
                       snippet="glshadersource"/>
      </add-function>
      <modify-function signature="glGetAttachedShaders(GLuint,GLsizei,GLsizei*,GLuint*)">
          <modify-argument index="3"><array/></modify-argument>
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetBufferParameteriv(GLenum,GLenum,GLint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetFramebufferAttachmentParameteriv(GLenum,GLenum,GLenum,GLint*)">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetBooleanv(GLenum,GLboolean*)">
          <modify-argument index="return" pyi-type="Union[bool,numpy.ndarray]">
              <replace-type modified-type="PyObject"/>
          </modify-argument>
          <modify-argument index="2">
              <remove-argument/>
          </modify-argument>
          <inject-code class="target" position="beginning" file="../glue/qtgui.cpp"
                       snippet="qopenglfunctions-glgetbooleanv"/>
      </modify-function>
      <!-- FIXME PYSIDE 7: Use the remove array modification and use
                           the version with return value instead -->
      <modify-function signature="glGetFloatv(GLenum,GLfloat*)">
          <modify-argument index="2"><array/></modify-argument>
      </modify-function>
      <add-function signature="glGetFloatv(GLuint)" return-type="PyObject">
          <modify-argument index="return" pyi-type="Union[float,numpy.ndarray]"/>
          <inject-code class="target" position="beginning" file="../glue/qtgui.cpp"
                       snippet="qopenglfunctions-glgetfloatv"/>
      </add-function>
      <!-- FIXME PYSIDE 7: Use the remove array modification and use
                           the version with return value instead -->
      <modify-function signature="glGetIntegerv(GLenum,GLint*)">
           <modify-argument index="2"><array/></modify-argument>
      </modify-function>
      <add-function signature="glGetIntegerv(GLuint)" return-type="PyObject">
          <modify-argument index="return" pyi-type="Union[int,numpy.ndarray]"/>
          <inject-code class="target" position="beginning" file="../glue/qtgui.cpp"
                       snippet="qopenglfunctions-glgetintegerv"/>
      </add-function>
      <modify-function signature="glGetProgramiv(GLuint,GLenum,GLint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetRenderbufferParameteriv(GLenum,GLenum,GLint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetShaderiv(GLuint,GLenum,GLint*)">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetShaderPrecisionFormat(GLenum,GLenum,GLint*,GLint*)">
          <modify-argument index="3"><array/></modify-argument>
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="^glDelete.*s\(.*$">
          <modify-argument index="2"><array/></modify-argument>
      </modify-function>
      <modify-function signature="^glGen[A-Z].*s\(.*$">
          <modify-argument index="2"><array/></modify-argument>
      </modify-function>
      <modify-function signature="^gl(Get)?TexParameterI?u?[fi]v\(.*$">
         <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="^glUniformMatrix\dfv\(.*$">
          <modify-argument index="4"><array/></modify-argument>
      </modify-function>
      <modify-function signature="^gl(Get)?Uniform\d?.v\(.*$">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="^glVertexAttrib\dfv\(.*$">
          <modify-argument index="2"><array/></modify-argument>
      </modify-function>
      <modify-function signature="^glGetVertexAttrib[fi]v\(.*$">
          <modify-argument index="3"><array/></modify-argument>
      </modify-function>
      <modify-function signature="glGetString(GLenum)">
          <modify-argument index="return">
              <replace-type modified-type="QString"/>
          </modify-argument>
          <inject-code class="target" position="end" file="../glue/qtgui.cpp"
                       snippet="glgetstring-return"/>
      </modify-function>
      <modify-function signature="glShaderBinary(GLint,const GLuint*,GLenum,const void*,GLint)">
          <modify-argument index="2"><array/></modify-argument>
      </modify-function>
  </object-type>
  <!-- Classes are result of a macro expansion in src/gui/opengl/qopenglversionfunctions.h
  <object-type name="QOpenGLFunctions_1_0"/>
  <object-type name="QOpenGLFunctions_1_1"/>
  <object-type name="QOpenGLFunctions_1_2"/>
  <object-type name="QOpenGLFunctions_1_3"/>
  <object-type name="QOpenGLFunctions_1_4"/>
  <object-type name="QOpenGLFunctions_1_5"/>
  <object-type name="QOpenGLFunctions_2_0">
  <object-type name="QOpenGLFunctions_2_1"/>
  <object-type name="QOpenGLFunctions_3_0"/>
  <object-type name="QOpenGLFunctions_3_1"/>
  <object-type name="QOpenGLFunctions_3_2_Compatibility"/>
  <object-type name="QOpenGLFunctions_3_2_Core"/>
  <object-type name="QOpenGLFunctions_3_3_Compatibility"/>
  <object-type name="QOpenGLFunctions_3_3_Core"/>
  <object-type name="QOpenGLFunctions_4_0_Compatibility"/>
  <object-type name="QOpenGLFunctions_4_0_Core"/>
  <object-type name="QOpenGLFunctions_4_1_Compatibility"/>
  <object-type name="QOpenGLFunctions_4_1_Core"/>
  <object-type name="QOpenGLFunctions_4_2_Compatibility"/>
  <object-type name="QOpenGLFunctions_4_2_Core"/>
  <object-type name="QOpenGLFunctions_4_3_Compatibility"/>
  <object-type name="QOpenGLFunctions_4_3_Core"/>
  <object-type name="QOpenGLFunctions_4_4_Compatibility" since="5.5/>
  <object-type name="QOpenGLFunctions_4_4_Core"/>
  <object-type name="QOpenGLFunctions_4_5_Compatibility/>
  <object-type name="QOpenGLFunctions_4_5_Core"/>
  <object-type name="QOpenGLFunctions_ES2"/>
  -->
  <value-type name="QPageLayout">
      <enum-type name="Mode"/>
      <enum-type name="Orientation"/>
      <enum-type name="OutOfBoundsPolicy" since="6.8"/>
      <enum-type name="Unit"/>
  </value-type>
  <value-type name="QPageRanges">
      <value-type name="Range"/>
  </value-type>
  <value-type name="QPageSize">
      <enum-type name="PageSizeId"/>
      <enum-type name="Unit"/>
      <enum-type name="SizeMatchPolicy"/>
  </value-type>
  <object-type name="QPaintDeviceWindow"/>
  <value-type name="QPixelFormat">
      <enum-type name="AlphaUsage"/>
      <enum-type name="AlphaPosition"/>
      <enum-type name="AlphaPremultiplied"/>
      <enum-type name="ByteOrder"/>
      <enum-type name="ColorModel"/>
      <enum-type name="TypeInterpretation"/>
      <enum-type name="YUVLayout"/>
  </value-type>
  <value-type name="QPdfOutputIntent"/>
  <object-type name="QPdfWriter">
      <enum-type name="ColorModel" since="6.8"/>
  </object-type>
  <object-type name="QPointingDevice">
    <enum-type name="PointerType" flags="PointerTypes"/>
    <enum-type name="GrabTransition"/>
  </object-type>
  <value-type name="QPointingDeviceUniqueId"/>
  <value-type name="QRawFont">
      <enum-type name="AntialiasingType"/>
      <enum-type name="LayoutFlag" flags="LayoutFlags"/>
      <modify-function signature="advancesForGlyphIndexes(const quint32*,QPointF*,int)const" remove="all"/>
      <modify-function signature="advancesForGlyphIndexes(const quint32*,QPointF*,int,QFlags&lt;QRawFont::LayoutFlag&gt;)const" remove="all"/>
      <modify-function signature="glyphIndexesForChars(const QChar*,int,quint32*,int*)const" remove="all"/>
      <modify-function signature="loadFromData(const QByteArray&amp;,qreal, QFont::HintingPreference)" allow-thread="yes"/>
      <modify-function signature="loadFromFile(const QString&amp;,qreal, QFont::HintingPreference)" allow-thread="yes"/>
  </value-type>
  <object-type name="QRasterWindow"/>
  <object-type name="QScreen">
      <modify-function signature="grabWindow(WId,int,int,int,int)">
        <modify-argument index="1">
            <replace-type modified-type="long"/>
        </modify-argument>
        <inject-code file="../glue/qtgui.cpp" snippet="qscreen-grabWindow"/>
      </modify-function>
      <add-function signature="nativeInterface()const" return-type="PyObject">
          <modify-argument index="return"> <!-- Suppress return value heuristics -->
              <define-ownership class="target" owner="default"/>
          </modify-argument>
          <inject-code class="target" position="beginning" file="../glue/qtgui.cpp"
                       snippet="qscreen-nativeInterface"/>
      </add-function>
  </object-type>
  <object-type name="QStyleHints"/>

  <object-type name="QUndoCommand">
    <modify-function signature="mergeWith(const QUndoCommand*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
  </object-type>
  <object-type name="QUndoGroup">
    <modify-function signature="addStack(QUndoStack*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="removeStack(QUndoStack*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QUndoStack">
    <modify-function signature="createUndoAction(QObject*,const QString&amp;)const">
      <modify-argument index="return">
        <parent index="1" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="createRedoAction(QObject*,const QString&amp;)const">
      <modify-argument index="return">
        <parent index="1" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="push(QUndoCommand*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>

  <namespace-type name="QtGuiHelper" visible="no">
      <object-type name="QOverrideCursorGuard" copyable="no">
      <add-function signature="__enter__()" return-type="QtGuiHelper::QOverrideCursorGuard">
          <inject-code file="../glue/qtcore.cpp" snippet="default-enter"/>
      </add-function>
      <add-function signature="__exit__(PyObject*,PyObject*,PyObject*)">
          <inject-code>%CPPSELF.restoreOverrideCursor();</inject-code>
      </add-function>
      </object-type>
  </namespace-type>

  <opaque-container name="QList" opaque-containers="QVector2D:QVector2DList;QVector3D:QVector3DList;QVector4D:QVector4DList"/>

</typesystem>
