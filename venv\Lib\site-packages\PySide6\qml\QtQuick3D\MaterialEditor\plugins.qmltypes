import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "materialadapter.h"
        name: "MaterialAdapter"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick3D.MaterialEditor/MaterialAdapter 1.0"]
        exportMetaObjectRevisions: [256]
        Property {
            name: "material"
            type: "QQuick3DCustomMaterial"
            isPointer: true
            read: "material"
            notify: "materialChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "rootNode"
            type: "QQuick3DNode"
            isPointer: true
            read: "rootNode"
            write: "setRootNode"
            notify: "rootNodeChanged"
            index: 1
        }
        Property {
            name: "fragmentShader"
            type: "QString"
            read: "fragmentShader"
            write: "setFragmentShader"
            notify: "fragmentShaderChanged"
            index: 2
        }
        Property {
            name: "vertexShader"
            type: "QString"
            read: "vertexShader"
            write: "setVertexShader"
            notify: "vertexShaderChanged"
            index: 3
        }
        Property {
            name: "vertexStatus"
            type: "ShaderBuildMessage"
            read: "vertexStatus"
            notify: "vertexStatusChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "fragmentStatus"
            type: "ShaderBuildMessage"
            read: "fragmentStatus"
            notify: "fragmentStatusChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "uniformModel"
            type: "UniformModel"
            isPointer: true
            read: "uniformModel"
            write: "setUniformModel"
            notify: "uniformModelChanged"
            index: 6
        }
        Property {
            name: "unsavedChanges"
            type: "bool"
            read: "unsavedChanges"
            write: "setUnsavedChanges"
            notify: "unsavedChangesChanged"
            index: 7
        }
        Property {
            name: "materialSaveFile"
            type: "QUrl"
            read: "materialSaveFile"
            write: "setMaterialSaveFile"
            notify: "materialSaveFileChanged"
            index: 8
        }
        Property {
            name: "cullMode"
            type: "QQuick3DMaterial::CullMode"
            read: "cullMode"
            write: "setCullMode"
            notify: "cullModeChanged"
            index: 9
        }
        Property {
            name: "depthDrawMode"
            type: "QQuick3DMaterial::DepthDrawMode"
            read: "depthDrawMode"
            write: "setDepthDrawMode"
            notify: "depthDrawModeChanged"
            index: 10
        }
        Property {
            name: "shadingMode"
            type: "QQuick3DCustomMaterial::ShadingMode"
            read: "shadingMode"
            write: "setShadingMode"
            notify: "shadingModeChanged"
            index: 11
        }
        Property {
            name: "sourceBlend"
            type: "QQuick3DCustomMaterial::BlendMode"
            read: "srcBlend"
            write: "setSrcBlend"
            notify: "srcBlendChanged"
            index: 12
        }
        Property {
            name: "destinationBlend"
            type: "QQuick3DCustomMaterial::BlendMode"
            read: "dstBlend"
            write: "setDstBlend"
            notify: "dstBlendChanged"
            index: 13
        }
        Signal { name: "materialChanged" }
        Signal { name: "fragmentShaderChanged" }
        Signal { name: "vertexShaderChanged" }
        Signal { name: "vertexStatusChanged" }
        Signal { name: "uniformModelChanged" }
        Signal { name: "fragmentStatusChanged" }
        Signal { name: "unsavedChangesChanged" }
        Signal { name: "materialSaveFileChanged" }
        Signal { name: "errorOccurred" }
        Signal { name: "postMaterialSaved" }
        Signal { name: "rootNodeChanged" }
        Signal { name: "cullModeChanged" }
        Signal { name: "depthDrawModeChanged" }
        Signal { name: "shadingModeChanged" }
        Signal { name: "srcBlendChanged" }
        Signal { name: "dstBlendChanged" }
        Method {
            name: "importFragmentShader"
            Parameter { name: "shaderFile"; type: "QUrl" }
        }
        Method {
            name: "importVertexShader"
            Parameter { name: "shaderFile"; type: "QUrl" }
        }
        Method { name: "save"; type: "bool" }
        Method {
            name: "saveMaterial"
            type: "bool"
            Parameter { name: "materialFile"; type: "QUrl" }
        }
        Method {
            name: "loadMaterial"
            type: "bool"
            Parameter { name: "materialFile"; type: "QUrl" }
        }
        Method {
            name: "exportQmlComponent"
            type: "bool"
            Parameter { name: "componentFile"; type: "QUrl" }
            Parameter { name: "vertName"; type: "QString" }
            Parameter { name: "fragName"; type: "QString" }
        }
        Method { name: "reset" }
        Method { name: "getSupportedImageFormatsFilter"; type: "QString"; isMethodConstant: true }
    }
    Component {
        file: "qsyntaxhighlighter.h"
        name: "QSyntaxHighlighter"
        accessSemantics: "reference"
        prototype: "QObject"
        Method { name: "rehighlight" }
        Method {
            name: "rehighlightBlock"
            Parameter { name: "block"; type: "QTextBlock" }
        }
        Method {
            name: "_q_reformatBlocks"
            Parameter { name: "from"; type: "int" }
            Parameter { name: "charsRemoved"; type: "int" }
            Parameter { name: "charsAdded"; type: "int" }
        }
        Method { name: "_q_delayedRehighlight" }
    }
    Component {
        file: "buildmessage.h"
        name: "ShaderBuildMessage"
        accessSemantics: "value"
        exports: ["QtQuick3D.MaterialEditor/shaderStatus 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [256]
        Enum {
            name: "Status"
            isScoped: true
            values: ["Success", "Error"]
        }
        Enum {
            name: "Stage"
            isScoped: true
            values: ["Vertex", "Fragment"]
        }
        Property { name: "filename"; type: "QString"; read: "filename"; index: 0; isReadonly: true }
        Property { name: "message"; type: "QString"; read: "message"; index: 1; isReadonly: true }
        Property { name: "identifier"; type: "QString"; read: "identifier"; index: 2; isReadonly: true }
        Property { name: "line"; type: "qlonglong"; read: "line"; index: 3; isReadonly: true }
        Property { name: "status"; type: "Status"; read: "status"; index: 4; isReadonly: true }
        Property { name: "stage"; type: "Stage"; read: "stage"; index: 5; isReadonly: true }
    }
    Component {
        file: "buildmessage.h"
        name: "ShaderBuildMessageDerived"
        accessSemantics: "none"
        prototype: "ShaderBuildMessage"
        exports: ["QtQuick3D.MaterialEditor/ShaderConstants 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [256]
    }
    Component {
        file: "syntaxhighlighter.h"
        name: "SyntaxHighlighter"
        accessSemantics: "reference"
        prototype: "QSyntaxHighlighter"
        exports: ["QtQuick3D.MaterialEditor/SyntaxHighlighter 1.0"]
        exportMetaObjectRevisions: [256]
        Property {
            name: "document"
            type: "QQuickTextDocument"
            isPointer: true
            read: "document"
            write: "setDocument"
            notify: "documentChanged"
            index: 0
        }
        Signal { name: "documentChanged" }
    }
    Component {
        file: "uniformmodel.h"
        name: "UniformModel"
        accessSemantics: "reference"
        prototype: "QAbstractListModel"
        exports: ["QtQuick3D.MaterialEditor/UniformModel 1.0"]
        exportMetaObjectRevisions: [256]
        Enum {
            name: "UniformType"
            values: [
                "Bool",
                "Int",
                "Float",
                "Vec2",
                "Vec3",
                "Vec4",
                "Mat44",
                "Sampler"
            ]
        }
        Method {
            name: "insertRow"
            type: "bool"
            Parameter { name: "rowIndex"; type: "int" }
            Parameter { name: "type"; type: "int" }
            Parameter { name: "id"; type: "QString" }
        }
        Method {
            name: "removeRow"
            Parameter { name: "rowIndex"; type: "int" }
            Parameter { name: "rows"; type: "int" }
        }
        Method {
            name: "removeRow"
            isCloned: true
            Parameter { name: "rowIndex"; type: "int" }
        }
    }
}
