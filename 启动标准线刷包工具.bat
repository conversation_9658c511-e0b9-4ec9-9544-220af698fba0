@echo off
chcp 65001 >nul
title 标准晶晨线刷包制作工具 v2.0

echo ========================================
echo 🔧 标准晶晨线刷包制作工具 v2.0
echo Copyright (c) 2025 By.举个🌰
echo ========================================
echo.
echo ✨ 新功能:
echo   - 正确生成DDR.USB和UBOOT.USB文件
echo   - 标准格式的platform.conf配置
echo   - 标准格式的image.cfg配置
echo   - 完整的USB Burning Tool兼容性
echo.

echo 正在启动工具...
echo.

REM 激活虚拟环境并启动程序
call venv\Scripts\activate.bat
python "标准晶晨线刷包制作工具.py"

echo.
echo 程序已退出，按任意键关闭窗口...
pause >nul
