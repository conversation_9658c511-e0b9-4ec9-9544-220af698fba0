[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "Keyframe"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "QQuickKeyframe", "lineNumber": 31, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "frame", "notify": "frameChanged", "read": "frame", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "set<PERSON>rame"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "easing", "notify": "easingCurveChanged", "read": "easing", "required": false, "scriptable": true, "stored": true, "type": "QEasingCurve", "user": false, "write": "setEasing"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "value", "notify": "valueChanged", "read": "value", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setValue"}], "qualifiedClassName": "QQuickKeyframe", "signals": [{"access": "public", "index": 0, "name": "frameChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "easingCurveChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "valueChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "KeyframeGroup"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "DefaultProperty", "value": "keyframes"}], "className": "QQuickKeyframeGroup", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 67, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "target", "notify": "targetChanged", "read": "target", "required": false, "scriptable": true, "stored": true, "type": "QObject*", "user": false, "write": "setTargetObject"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "property", "notify": "propertyChanged", "read": "property", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setProperty"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "keyframes", "read": "keyframes", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QQuickKeyframe>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "keyframeSource", "notify": "keyframeSourceChanged", "read": "keyframeSource", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setKeyframeSource"}], "qualifiedClassName": "QQuickKeyframeGroup", "signals": [{"access": "public", "index": 0, "name": "targetChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "propertyChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "keyframeSourceChanged", "returnType": "void", "revision": 257}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qquickkeyframe_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Timeline"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "DefaultProperty", "value": "keyframeGroups"}], "className": "QQuickTimeline", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 28, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "startFrame", "notify": "startFrameChanged", "read": "startFrame", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setStartFrame"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "endFrame", "notify": "endFrameChanged", "read": "endFrame", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setEndFrame"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "currentFrame", "notify": "currentFrameChanged", "read": "currentFrame", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setCurrentFrame"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "keyframeGroups", "read": "keyframeGroups", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QQuickKeyframeGroup>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "animations", "read": "animations", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QQuickTimelineAnimation>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "enabled", "notify": "enabledChanged", "read": "enabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setEnabled"}], "qualifiedClassName": "QQuickTimeline", "signals": [{"access": "public", "index": 0, "name": "enabledChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "startFrameChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "endFrameChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "currentFrameChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qquicktimeline_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "TimelineAnimation"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "QQuickTimelineAnimation", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "ping<PERSON>ong", "notify": "ping<PERSON><PERSON><PERSON><PERSON>ed", "read": "ping<PERSON>ong", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPingPong"}], "qualifiedClassName": "QQuickTimelineAnimation", "signals": [{"access": "public", "index": 0, "name": "ping<PERSON><PERSON><PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "index": 1, "name": "finished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickNumberAnimation"}]}], "inputFile": "qquicktimelineanimation_p.h", "outputRevision": 69}]