@echo off
chcp 65001 >nul
title 晶晨本地刷机包制作工具

echo ========================================
echo 🔧 晶晨本地刷机包制作工具
echo Copyright (c) 2025 By.举个🌰
echo ========================================
echo.
echo ✨ 功能特点:
echo   - 使用本地已提取的文件制作刷机包
echo   - 自动从bootloader.PARTITION提取DDR.USB和UBOOT.USB
echo   - 生成标准格式的platform.conf和image.cfg
echo   - 完全兼容USB Burning Tool
echo.

echo 正在启动工具...
echo.

REM 激活虚拟环境并启动程序
call venv\Scripts\activate.bat
python "晶晨本地刷机包制作.py"

echo.
echo 程序已退出，按任意键关闭窗口...
pause >nul
