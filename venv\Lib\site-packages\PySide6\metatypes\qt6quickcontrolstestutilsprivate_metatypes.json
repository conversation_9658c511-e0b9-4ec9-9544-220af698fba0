[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "auto"}, {"name": "QML.Singleton", "value": "true"}], "className": "ComponentCreator", "lineNumber": 61, "methods": [{"access": "public", "arguments": [{"name": "data", "type": "QByteArray"}], "index": 0, "name": "createComponent", "returnType": "QQmlComponent*"}], "object": true, "qualifiedClassName": "QQuickControlsTestUtils::ComponentCreator", "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "auto"}, {"name": "QML.Singleton", "value": "true"}], "className": "StyleInfo", "lineNumber": 72, "object": true, "properties": [{"constant": true, "designable": true, "final": true, "index": 0, "name": "styleName", "read": "styleName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}], "qualifiedClassName": "QQuickControlsTestUtils::StyleInfo", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "controlstestutils_p.h", "outputRevision": 69}]