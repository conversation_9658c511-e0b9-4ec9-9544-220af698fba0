<!--
// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<modify-function signature="^glClearNamedFramebufferu?[fi]v\(.*$">
    <modify-argument index="4"><array/></modify-argument>
</modify-function>
<modify-function signature="^glInvalidateNamedFramebuffer(Sub)?Data\(.*$">
    <modify-argument index="3"><array/></modify-argument>
</modify-function>
<modify-function signature="^glNamedFramebufferDrawBuffers\(.*$">
    <modify-argument index="3"><array/></modify-argument>
</modify-function>
<modify-function signature="^glTextureParameterI?u?[fi]v\(.*$">
    <modify-argument index="3"><array/></modify-argument>
</modify-function>
<modify-function signature="glVertexArrayVertexBuffers(<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,GLsizei,const GLuint *,const G<PERSON>intptr*,const GLsizei*)">
    <modify-argument index="4"><array/></modify-argument>
    <modify-argument index="5"><array/></modify-argument>
    <modify-argument index="6"><array/></modify-argument>
</modify-function>
