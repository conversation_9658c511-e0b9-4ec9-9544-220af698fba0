[{"classes": [{"className": "QDBusAbstractAdaptor", "lineNumber": 16, "object": true, "qualifiedClassName": "QDBusAbstractAdaptor", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdbusabstractadaptor.h", "outputRevision": 69}, {"classes": [{"className": "QDBusAdaptorConnector", "lineNumber": 56, "object": true, "qualifiedClassName": "QDBusAdaptorConnector", "signals": [{"access": "public", "arguments": [{"name": "obj", "type": "QObject*"}, {"name": "metaObject", "type": "const QMetaObject*"}, {"name": "sid", "type": "int"}, {"name": "args", "type": "QVariantList"}], "index": 0, "name": "relaySignal", "returnType": "void"}], "slots": [{"access": "public", "index": 1, "name": "relaySlot", "returnType": "void"}, {"access": "public", "index": 2, "name": "polish", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdbusabstractadaptor_p.h", "outputRevision": 69}, {"classes": [{"className": "QDBusAbstractInterface", "lineNumber": 42, "object": true, "qualifiedClassName": "QDBusAbstractInterface", "slots": [{"access": "private", "arguments": [{"type": "QString"}, {"type": "QString"}, {"type": "QString"}], "index": 0, "name": "_q_serviceOwnerChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDBusAbstractInterfaceBase"}]}], "inputFile": "qdbusabstractinterface.h", "outputRevision": 69}, {"classes": [{"className": "QDBusConnection", "enums": [{"isClass": false, "isFlag": false, "name": "BusType", "values": ["SessionBus", "SystemBus", "ActivationBus"]}, {"alias": "RegisterOption", "isClass": false, "isFlag": true, "name": "RegisterOptions", "values": ["ExportAdaptors", "ExportScriptableSlots", "ExportScriptableSignals", "ExportScriptableProperties", "ExportScriptableInvokables", "ExportScriptableContents", "ExportNonScriptableSlots", "ExportNonScriptableSignals", "ExportNonScriptableProperties", "ExportNonScriptableInvokables", "ExportNonScriptableContents", "ExportAllSlots", "ExportAllSignals", "ExportAllProperties", "ExportAllInvokables", "ExportAllContents", "ExportAllSignal", "ExportChildObjects"]}, {"isClass": false, "isFlag": false, "name": "UnregisterMode", "values": ["UnregisterNode", "UnregisterTree"]}], "gadget": true, "lineNumber": 41, "qualifiedClassName": "QDBusConnection"}], "inputFile": "qdbusconnection.h", "outputRevision": 69}, {"classes": [{"className": "QDBusConnectionPrivate", "lineNumber": 74, "object": true, "qualifiedClassName": "QDBusConnectionPrivate", "signals": [{"access": "public", "index": 0, "name": "dispatchStatusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "msg", "type": "QDBusMessage"}], "index": 1, "name": "spyHooksFinished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pcall", "type": "QDBusPendingCallPrivate*"}, {"name": "msg", "type": "void*"}, {"name": "timeout", "type": "int"}], "index": 2, "name": "messageNeedsSending", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pcall", "type": "QDBusPendingCallPrivate*"}, {"name": "msg", "type": "void*"}], "index": 3, "isCloned": true, "name": "messageNeedsSending", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}, {"name": "old<PERSON>wner", "type": "QString"}, {"name": "new<PERSON>wner", "type": "QString"}], "index": 4, "name": "serviceOwnerChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QDBusError"}, {"name": "message", "type": "QDBusMessage"}], "index": 5, "name": "callWithCallbackFailed", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "index": 6, "name": "setDispatchEnabled", "returnType": "void"}, {"access": "public", "index": 7, "name": "doDispatch", "returnType": "void"}, {"access": "public", "arguments": [{"type": "qintptr"}], "index": 8, "name": "socketRead", "returnType": "void"}, {"access": "public", "arguments": [{"type": "qintptr"}], "index": 9, "name": "socketWrite", "returnType": "void"}, {"access": "public", "arguments": [{"name": "o", "type": "QObject*"}], "index": 10, "name": "objectDestroyed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "obj", "type": "QObject*"}, {"type": "const QMetaObject*"}, {"name": "signalId", "type": "int"}, {"name": "args", "type": "QVariantList"}], "index": 11, "name": "relaySignal", "returnType": "void"}, {"access": "private", "arguments": [{"name": "name", "type": "QString"}, {"name": "old<PERSON>wner", "type": "QString"}, {"name": "new<PERSON>wner", "type": "QString"}], "index": 12, "name": "serviceOwnerChangedNoLock", "returnType": "void"}, {"access": "private", "arguments": [{"name": "serviceName", "type": "QString"}], "index": 13, "name": "registerServiceNoLock", "returnType": "void"}, {"access": "private", "arguments": [{"name": "serviceName", "type": "QString"}], "index": 14, "name": "unregisterServiceNoLock", "returnType": "void"}, {"access": "private", "index": 15, "name": "handleDBusDisconnection", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdbusconnection_p.h", "outputRevision": 69}, {"classes": [{"className": "QDBusConnectionInterface", "enums": [{"isClass": false, "isFlag": false, "name": "ServiceQueueOptions", "values": ["DontQueueService", "QueueService", "ReplaceExistingService"]}, {"isClass": false, "isFlag": false, "name": "ServiceReplacementOptions", "values": ["DontAllowReplacement", "AllowReplacement"]}, {"isClass": false, "isFlag": false, "name": "RegisterServiceReply", "values": ["ServiceNotRegistered", "ServiceRegistered", "ServiceQueued"]}], "lineNumber": 25, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "registeredServiceNames", "read": "registeredServiceNames", "required": false, "scriptable": true, "stored": true, "type": "QDBusReply<QStringList>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "activatableServiceNames", "read": "activatableServiceNames", "required": false, "scriptable": true, "stored": true, "type": "QDBusReply<QStringList>", "user": false}], "qualifiedClassName": "QDBusConnectionInterface", "signals": [{"access": "public", "arguments": [{"name": "service", "type": "QString"}], "index": 0, "name": "serviceRegistered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "service", "type": "QString"}], "index": 1, "name": "serviceUnregistered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}, {"name": "old<PERSON>wner", "type": "QString"}, {"name": "new<PERSON>wner", "type": "QString"}], "index": 2, "name": "serviceOwnerChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QDBusError"}, {"name": "call", "type": "QDBusMessage"}], "index": 3, "name": "callWithCallbackFailed", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QString"}], "index": 4, "name": "NameAcquired", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QString"}], "index": 5, "name": "NameLost", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QString"}, {"type": "QString"}, {"type": "QString"}], "index": 6, "name": "NameOwnerChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 7, "isConst": true, "name": "registeredServiceNames", "returnType": "QDBusReply<QStringList>"}, {"access": "public", "index": 8, "isConst": true, "name": "activatableServiceNames", "returnType": "QDBusReply<QStringList>"}, {"access": "public", "arguments": [{"name": "serviceName", "type": "QString"}], "index": 9, "isConst": true, "name": "isServiceRegistered", "returnType": "QDBusReply<bool>"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 10, "isConst": true, "name": "serviceOwner", "returnType": "QDBusReply<QString>"}, {"access": "public", "arguments": [{"name": "serviceName", "type": "QString"}], "index": 11, "name": "unregisterService", "returnType": "QDBusReply<bool>"}, {"access": "public", "arguments": [{"name": "serviceName", "type": "QString"}, {"name": "qoption", "type": "ServiceQueueOptions"}, {"name": "roption", "type": "ServiceReplacementOptions"}], "index": 12, "name": "registerService", "returnType": "QDBusReply<QDBusConnectionInterface::RegisterServiceReply>"}, {"access": "public", "arguments": [{"name": "serviceName", "type": "QString"}, {"name": "qoption", "type": "ServiceQueueOptions"}], "index": 13, "isCloned": true, "name": "registerService", "returnType": "QDBusReply<QDBusConnectionInterface::RegisterServiceReply>"}, {"access": "public", "arguments": [{"name": "serviceName", "type": "QString"}], "index": 14, "isCloned": true, "name": "registerService", "returnType": "QDBusReply<QDBusConnectionInterface::RegisterServiceReply>"}, {"access": "public", "arguments": [{"name": "serviceName", "type": "QString"}], "index": 15, "isConst": true, "name": "servicePid", "returnType": "QDBusReply<uint>"}, {"access": "public", "arguments": [{"name": "serviceName", "type": "QString"}], "index": 16, "isConst": true, "name": "serviceUid", "returnType": "QDBusReply<uint>"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 17, "name": "startService", "returnType": "QDBusReply<void>"}], "superClasses": [{"access": "public", "name": "QDBusAbstractInterface"}]}], "inputFile": "qdbusconnectioninterface.h", "outputRevision": 69}, {"classes": [{"className": "QDBusConnectionManager", "lineNumber": 30, "object": true, "qualifiedClassName": "QDBusConnectionManager", "superClasses": [{"access": "public", "name": "QDaemonThread"}]}], "inputFile": "qdbusconnectionmanager_p.h", "outputRevision": 69}, {"classes": [{"className": "QDBusError", "enums": [{"isClass": false, "isFlag": false, "name": "ErrorType", "values": ["NoError", "Other", "Failed", "NoMemory", "ServiceUnknown", "NoReply", "<PERSON><PERSON><PERSON><PERSON>", "NotSupported", "LimitsExceeded", "AccessDenied", "NoServer", "Timeout", "NoNetwork", "AddressInUse", "Disconnected", "InvalidArgs", "<PERSON><PERSON><PERSON><PERSON>", "TimedOut", "InvalidSignature", "UnknownInterface", "UnknownObject", "UnknownProperty", "PropertyReadOnly", "InternalError", "InvalidService", "InvalidObjectPath", "InvalidInterface", "InvalidMember", "LastErrorType"]}], "gadget": true, "lineNumber": 20, "qualifiedClassName": "QDBusError"}], "inputFile": "qdbuserror.h", "outputRevision": 69}, {"classes": [{"className": "QDBusPendingCallWatcher", "lineNumber": 60, "object": true, "qualifiedClassName": "QDBusPendingCallWatcher", "signals": [{"access": "public", "arguments": [{"name": "self", "type": "QDBusPendingCallWatcher*"}], "index": 0, "name": "finished", "returnType": "void"}, {"access": "public", "index": 1, "isCloned": true, "name": "finished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDBusPendingCall"}]}], "inputFile": "qdbuspendingcall.h", "outputRevision": 69}, {"classes": [{"className": "QDBusPendingCallWatcherHelper", "lineNumber": 76, "object": true, "qualifiedClassName": "QDBusPendingCallWatcherHelper", "signals": [{"access": "public", "index": 0, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "msg", "type": "QDBusMessage"}], "index": 1, "name": "reply", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QDBusError"}, {"name": "msg", "type": "QDBusMessage"}], "index": 2, "name": "error", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdbuspendingcall_p.h", "outputRevision": 69}, {"classes": [{"className": "QDBusServer", "lineNumber": 20, "object": true, "qualifiedClassName": "QDBusServer", "signals": [{"access": "public", "arguments": [{"name": "connection", "type": "QDBusConnection"}], "index": 0, "name": "newConnection", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdbusserver.h", "outputRevision": 69}, {"classes": [{"className": "QDBusServiceWatcher", "enums": [{"alias": "WatchModeFlag", "isClass": false, "isFlag": true, "name": "WatchMode", "values": ["WatchForRegistration", "WatchForUnregistration", "WatchForOwnerChange"]}], "lineNumber": 22, "object": true, "properties": [{"bindable": "bindableWatchedServices", "constant": false, "designable": true, "final": false, "index": 0, "name": "watchedServices", "read": "watchedServices", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setWatchedServices"}, {"bindable": "bindableWatchMode", "constant": false, "designable": true, "final": false, "index": 1, "name": "watchMode", "read": "watchMode", "required": false, "scriptable": true, "stored": true, "type": "WatchMode", "user": false, "write": "setWatchMode"}], "qualifiedClassName": "QDBusServiceWatcher", "signals": [{"access": "public", "arguments": [{"name": "service", "type": "QString"}], "index": 0, "name": "serviceRegistered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "service", "type": "QString"}], "index": 1, "name": "serviceUnregistered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "service", "type": "QString"}, {"name": "old<PERSON>wner", "type": "QString"}, {"name": "new<PERSON>wner", "type": "QString"}], "index": 2, "name": "serviceOwnerChanged", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"type": "QString"}, {"type": "QString"}, {"type": "QString"}], "index": 3, "name": "_q_serviceOwnerChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdbusservicewatcher.h", "outputRevision": 69}, {"classes": [{"className": "QDBusVirtualObject", "lineNumber": 19, "object": true, "qualifiedClassName": "QDBusVirtualObject", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdbusvirtualobject.h", "outputRevision": 69}]