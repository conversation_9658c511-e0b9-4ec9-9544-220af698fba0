#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试修复后的ADB工具
Copyright (c) 2025 By.举个🌰
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 晶晨ADB备份工具 import ADBManager

def main():
    print("🧪 快速测试修复后的ADB工具")
    print("=" * 50)
    
    # 测试指定设备的分区获取
    adb_manager = ADBManager('192.168.233.180', '5555')
    success, msg = adb_manager.connect_device()
    print(f'连接结果: {success}, {msg}')

    if success:
        print("\n🔍 开始获取分区...")
        partitions = adb_manager.get_partitions()
        print(f'\n✅ 找到 {len(partitions)} 个分区')
        
        if partitions:
            print("\n📋 分区列表:")
            for i, p in enumerate(partitions):
                print(f'  {i+1:2d}. {p["name"]:<12} -> {p["path"]:<25} ({p["size"]})')
        else:
            print("❌ 未找到任何分区")
    else:
        print("❌ 设备连接失败")

if __name__ == "__main__":
    main()
