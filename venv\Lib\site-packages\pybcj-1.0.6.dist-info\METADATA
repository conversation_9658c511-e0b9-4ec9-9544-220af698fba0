Metadata-Version: 2.4
Name: pybcj
Version: 1.0.6
Summary: bcj filter library
Author-email: <PERSON><PERSON><PERSON> <<EMAIL>>
License: LGPL-2.1-or-later
Project-URL: Source, https://codeberg.org/miurahr/pybcj
Keywords: lzma,bcj
Classifier: Development Status :: 5 - Production/Stable
Classifier: License :: OSI Approved :: GNU Lesser General Public License v2 or later (LGPLv2+)
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX
Classifier: Operating System :: POSIX :: Linux
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
Provides-Extra: test
Requires-Dist: pytest>=6.0; extra == "test"
Requires-Dist: pytest-cov; extra == "test"
Requires-Dist: hypothesis; extra == "test"
Requires-Dist: coverage[toml]>=5.2; extra == "test"
Provides-Extra: check
Requires-Dist: mypy>=1.10.0; extra == "check"
Requires-Dist: check-manifest; extra == "check"
Requires-Dist: flake8<5; extra == "check"
Requires-Dist: flake8-black; extra == "check"
Requires-Dist: flake8-colors; extra == "check"
Requires-Dist: flake8-isort; extra == "check"
Requires-Dist: flake8-pyi; extra == "check"
Requires-Dist: flake8-typing-imports; extra == "check"
Requires-Dist: readme-renderer; extra == "check"
Requires-Dist: pygments; extra == "check"
Dynamic: license-file

=======================================
BCJ(Branch-Call-Jump) filter for python
=======================================

.. image:: https://badge.fury.io/py/pybcj.svg
  :target: https://badge.fury.io/py/pybcj

.. image:: https://img.shields.io/conda/vn/conda-forge/pybcj
  :target: https://anaconda.org/conda-forge/pybcj

.. image:: https://coveralls.io/repos/github/miurahr/pybcj/badge.svg?branch=main
  :target: https://coveralls.io/github/miurahr/pybcj?branch=main


In data compression, BCJ, short for Branch-Call-Jump, refers to a technique that improves the compression of
machine code of executable binaries by replacing relative branch addresses with absolute ones.
This allows a LZMA compressor to identify duplicate targets and archive higher compression rate.

BCJ is used in 7-zip compression utility as default filter for executable binaries.

pybcj is a python bindings with BCJ implementation by C language.
The C codes are derived from p7zip, portable 7-zip implementation.
pybcj support Intel/Amd x86/x86_64, Arm/Arm64, ArmThumb, Sparc, PPC, and IA64.


Development status
==================

A development status is considered as ``Beta`` state.


Installation
============

As usual, you can install pybcj using python standard pip command.

.. code-block::

    pip install pybcj

Alternatively, one can also use conda:

.. code-block::

    conda install -c conda-forge pybcj

WARNING
-------

* When use it on MSYS2/Mingw64 environment, please set environment variable
  `SETUPTOOLS_USE_DISTUTILS=stdlib` to install.

License
=======

- SPDX-License-Identifier: LGPL-2.1-or-later
- SPDX-URL: https://spdx.org/licenses/LGPL-2.1-or-later.html

* Copyright (C) 2020-2025 Hiroshi Miura
* Copyright (C) 1999-2010 Igor Pavlov
