[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "WavefrontMesh"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "QWavefrontMesh", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "InvalidSourceError", "UnsupportedFaceShapeError", "UnsupportedIndexSizeError", "FileNotFoundError", "NoAttributesError", "MissingPositionAttributeError", "MissingTextureCoordinateAttributeError", "MissingPositionAndTextureCoordinateAttributesError", "TooManyAttributesError", "InvalidPlaneDefinitionError"]}], "lineNumber": 28, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "lastError", "notify": "lastError<PERSON><PERSON>ed", "read": "lastError", "required": false, "scriptable": true, "stored": true, "type": "Error", "user": false}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "projectionPlaneV", "notify": "projectionPlaneVChanged", "read": "projectionPlaneV", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setProjectionPlaneV"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "projectionPlaneW", "notify": "projectionPlaneWChanged", "read": "projectionPlaneW", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setProjectionPlaneW"}], "qualifiedClassName": "QWavefrontMesh", "signals": [{"access": "public", "index": 0, "name": "sourceChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "lastError<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "index": 2, "name": "projectionPlaneVChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "projectionPlaneWChanged", "returnType": "void"}], "slots": [{"access": "protected", "index": 4, "name": "readData", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickShaderEffectMesh"}]}], "inputFile": "qwavefrontmesh_p.h", "outputRevision": 69}]