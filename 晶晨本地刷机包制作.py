#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
晶晨本地刷机包制作工具
使用本地已提取的文件制作标准USB Burning Tool刷机包
Copyright (c) 2025 By.举个🌰
"""

import sys
import os
import shutil
import time
import struct
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

class BootloaderExtractor:
    """Bootloader文件提取器"""
    
    @staticmethod
    def extract_ddr_uboot(bootloader_file, output_dir):
        """从bootloader文件提取DDR.USB和UBOOT.USB"""
        try:
            with open(bootloader_file, 'rb') as f:
                data = f.read()

            print(f"📁 Bootloader文件大小: {len(data)} bytes")

            # 检查是否有现有的DDR.USB和UBOOT.USB作为参考
            ddr_reference_size = None
            uboot_reference_size = None

            # 查找参考文件（在当前目录或父目录）
            for search_dir in [output_dir, os.path.dirname(output_dir), "."]:
                ddr_ref_path = os.path.join(search_dir, "DDR.USB")
                uboot_ref_path = os.path.join(search_dir, "UBOOT.USB")

                if os.path.exists(ddr_ref_path) and ddr_reference_size is None:
                    ddr_reference_size = os.path.getsize(ddr_ref_path)
                    print(f"📋 找到DDR.USB参考文件，大小: {ddr_reference_size} bytes")

                if os.path.exists(uboot_ref_path) and uboot_reference_size is None:
                    uboot_reference_size = os.path.getsize(uboot_ref_path)
                    print(f"📋 找到UBOOT.USB参考文件，大小: {uboot_reference_size} bytes")

            # 查找AMLC位置
            amlc_pos = data.find(b'AMLC')
            if amlc_pos == -1:
                return False, "未找到AMLC标识"

            print(f"🔍 找到AMLC位置: 0x{amlc_pos:x}")

            # DDR部分提取
            if ddr_reference_size:
                # 使用参考文件大小
                ddr_data = data[:ddr_reference_size]
                print(f"📏 使用参考DDR大小: {ddr_reference_size} bytes")
            else:
                # 默认策略：从开头到AMLC位置，但限制在合理范围
                ddr_size = min(amlc_pos, 131072)  # 最大128KB
                ddr_data = data[:ddr_size]
                print(f"📏 计算DDR大小: {ddr_size} bytes")

            # U-Boot部分提取
            if uboot_reference_size:
                # 使用参考文件大小，从AMLC位置开始
                uboot_data = data[amlc_pos:amlc_pos + uboot_reference_size]
                print(f"📏 使用参考U-Boot大小: {uboot_reference_size} bytes")
            else:
                # 默认策略：从AMLC到文件末尾，但限制最大大小
                uboot_size = min(len(data) - amlc_pos, 1048576)  # 最大1MB
                uboot_data = data[amlc_pos:amlc_pos + uboot_size]
                print(f"📏 计算U-Boot大小: {uboot_size} bytes")

            # 验证提取的数据
            if b'AML' in ddr_data[:100]:
                print("✅ DDR数据包含AML标识")
            else:
                print("⚠️  DDR数据未包含AML标识")

            if b'AMLC' in uboot_data[:100]:
                print("✅ U-Boot数据包含AMLC标识")
            else:
                print("⚠️  U-Boot数据未包含AMLC标识")

            # 保存DDR.USB
            ddr_path = os.path.join(output_dir, "DDR.USB")
            with open(ddr_path, 'wb') as f:
                f.write(ddr_data)

            # 保存UBOOT.USB
            uboot_path = os.path.join(output_dir, "UBOOT.USB")
            with open(uboot_path, 'wb') as f:
                f.write(uboot_data)

            return True, f"DDR.USB: {len(ddr_data)} bytes, UBOOT.USB: {len(uboot_data)} bytes"

        except Exception as e:
            return False, f"提取失败: {str(e)}"

class LocalFirmwareBuilder:
    """本地固件包构建器"""
    
    def __init__(self, source_dir, output_dir, device_info):
        self.source_dir = source_dir
        self.output_dir = output_dir
        self.device_info = device_info
        self.package_dir = os.path.join(output_dir, "USB_Burning_Tool")
        
    def create_package_structure(self):
        """创建刷机包目录结构"""
        try:
            os.makedirs(self.package_dir, exist_ok=True)
            os.makedirs(os.path.join(self.package_dir, "platform"), exist_ok=True)
            return True, "目录结构创建成功"
        except Exception as e:
            return False, f"目录结构创建失败: {str(e)}"
    
    def copy_partition_files(self):
        """复制分区文件"""
        try:
            copied_files = []
            
            # 定义需要复制的文件映射
            file_mappings = {
                # 分区文件
                'boot.PARTITION': 'boot.PARTITION',
                'bootloader.PARTITION': 'bootloader.PARTITION', 
                'recovery.PARTITION': 'recovery.PARTITION',
                'system.PARTITION': 'system.PARTITION',
                'vendor.PARTITION': 'vendor.PARTITION',
                'product.PARTITION': 'product.PARTITION',
                'logo.PARTITION': 'logo.PARTITION',
                'param.PARTITION': 'param.PARTITION',
                'vbmeta.PARTITION': 'vbmeta.PARTITION',
                'metadata.PARTITION': 'metadata.PARTITION',
                'cri_data.PARTITION': 'cri_data.PARTITION',
                'misc.PARTITION': 'misc.PARTITION',
                'cache.PARTITION': 'cache.PARTITION',
                
                # DTB文件
                'meson1.dtb': 'meson1.dtb',
            }
            
            for source_name, target_name in file_mappings.items():
                source_path = os.path.join(self.source_dir, source_name)
                target_path = os.path.join(self.package_dir, target_name)
                
                if os.path.exists(source_path):
                    shutil.copy2(source_path, target_path)
                    file_size = os.path.getsize(target_path)
                    copied_files.append({
                        'name': source_name,
                        'size': self._format_size(file_size)
                    })
            
            return True, f"复制了 {len(copied_files)} 个文件", copied_files
            
        except Exception as e:
            return False, f"文件复制失败: {str(e)}", []
    
    def extract_bootloader_files(self):
        """从bootloader.PARTITION提取DDR.USB和UBOOT.USB"""
        try:
            bootloader_path = os.path.join(self.source_dir, "bootloader.PARTITION")
            
            if not os.path.exists(bootloader_path):
                return False, "未找到bootloader.PARTITION文件"
            
            success, message = BootloaderExtractor.extract_ddr_uboot(bootloader_path, self.package_dir)
            return success, message
            
        except Exception as e:
            return False, f"Bootloader提取失败: {str(e)}"
    
    def create_image_cfg(self, copied_files):
        """创建标准的image.cfg文件"""
        try:
            cfg_lines = []

            # 文件列表部分 - 使用制表符分隔格式
            cfg_lines.append("[LIST_NORMAL]")

            # 添加DDR和UBOOT - 使用制表符格式
            cfg_lines.append('file="DDR.USB"\t\tmain_type="USB"\t\tsub_type="DDR"')
            cfg_lines.append('file="UBOOT.USB"\t\tmain_type="USB"\t\tsub_type="UBOOT"')

            # 添加DTB
            cfg_lines.append('file="meson1.dtb"\t\tmain_type="dtb"\t\tsub_type="meson1"')

            # 添加platform.conf
            cfg_lines.append('file="platform.conf"\t\tmain_type="conf"\t\tsub_type="platform"')

            cfg_lines.append("")

            # 验证列表部分 - 分区文件放在这里
            cfg_lines.append("[LIST_VERIFY]")

            # 添加分区文件 - 使用标准的分区映射
            partition_mappings = {
                'boot.PARTITION': ('PARTITION', 'boot'),
                'bootloader.PARTITION': ('PARTITION', 'bootloader'),
                'cri_data.PARTITION': ('PARTITION', 'cri_data'),
                'logo.PARTITION': ('PARTITION', 'logo'),
                'metadata.PARTITION': ('PARTITION', 'metadata'),
                'param.PARTITION': ('PARTITION', 'param'),
                'product.PARTITION': ('PARTITION', 'product'),
                'recovery.PARTITION': ('PARTITION', 'recovery'),
                'system.PARTITION': ('PARTITION', 'system'),
                'vbmeta.PARTITION': ('PARTITION', 'vbmeta'),
                'vendor.PARTITION': ('PARTITION', 'vendor'),
                'misc.PARTITION': ('PARTITION', 'misc'),
                'cache.PARTITION': ('PARTITION', 'cache')
            }

            # 按字母顺序添加分区文件
            partition_files = []
            for file_info in copied_files:
                file_name = file_info['name']
                if file_name in partition_mappings:
                    main_type, sub_type = partition_mappings[file_name]
                    partition_files.append((file_name, main_type, sub_type))

            # 排序并添加到配置
            partition_files.sort(key=lambda x: x[2])  # 按sub_type排序
            for file_name, main_type, sub_type in partition_files:
                cfg_lines.append(f'file="{file_name}"\t\tmain_type="{main_type}"\t\tsub_type="{sub_type}"')

            cfg_lines.append("")

            # 写入文件
            cfg_path = os.path.join(self.package_dir, "image.cfg")
            with open(cfg_path, 'w', encoding='utf-8', newline='\n') as f:
                f.write('\n'.join(cfg_lines))

            return True, "image.cfg创建成功"

        except Exception as e:
            return False, f"image.cfg创建失败: {str(e)}"
    
    def create_platform_conf(self):
        """创建标准的platform.conf文件"""
        try:
            # 使用标准的USB Burning Tool platform.conf格式
            conf_content = f"""[platform]
name={self.device_info.get('model', 'CM311-1e')}
brand={self.device_info.get('brand', 'CM311-1e')}
chip={self.device_info.get('chip', 'amlogic')}
platform={self.device_info.get('platform', 'amlogic')}
variant=
board_version=

[configs]
DDR_FILE=DDR.USB
UBOOT_FILE=UBOOT.USB
PARTITION_FILE=image.cfg

[burning]
erase_bootloader=true
erase_flash=false
reboot=true
verify=true

[advanced]
ddr_timing_test=false
secure_boot=false
"""

            # 写入文件到根目录（与image.cfg同级）
            conf_path = os.path.join(self.package_dir, "platform.conf")
            with open(conf_path, 'w', encoding='utf-8', newline='\n') as f:
                f.write(conf_content)

            return True, "platform.conf创建成功"

        except Exception as e:
            return False, f"platform.conf创建失败: {str(e)}"
    
    def create_readme(self, copied_files):
        """创建README文件"""
        try:
            readme_content = f"""# {self.device_info.get('model', 'CM311-1e')} 刷机包

## 设备信息
- 型号: {self.device_info.get('model', 'CM311-1e')}
- 品牌: {self.device_info.get('brand', 'CM311-1e')}
- 芯片: {self.device_info.get('chip', 'amlogic')}
- 平台: {self.device_info.get('platform', 'amlogic')}

## 刷机包内容
### 引导文件
- DDR.USB: DDR初始化文件
- UBOOT.USB: U-Boot引导文件
- meson1.dtb: 设备树文件

### 分区文件
"""
            
            for file_info in copied_files:
                readme_content += f"- {file_info['name']}: {file_info['size']}\n"
            
            readme_content += f"""
### 配置文件
- image.cfg: 镜像配置文件
- platform/platform.conf: 平台配置文件

## 使用方法
1. 使用晶晨USB Burning Tool
2. 选择此刷机包目录
3. 按照工具提示进行刷机

## 注意事项
- 刷机有风险，请确保设备型号匹配
- 刷机前请备份重要数据
- 刷机过程中请勿断电或拔线

## 制作信息
- 制作时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
- 制作工具: 晶晨本地刷机包制作工具
- Copyright (c) 2025 By.举个🌰

## 技术规格
- 刷机包格式: USB Burning Tool标准格式
- DDR文件: 从bootloader.PARTITION提取
- U-Boot文件: 从bootloader.PARTITION提取
- 分区映射: 标准晶晨分区布局
"""
            
            readme_path = os.path.join(self.package_dir, "README.md")
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            return True, "README.md创建成功"
            
        except Exception as e:
            return False, f"README.md创建失败: {str(e)}"
    
    def _format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        units = ['B', 'KB', 'MB', 'GB']
        unit_index = 0
        size = float(size_bytes)
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        if unit_index == 0:
            return f"{int(size)} {units[unit_index]}"
        else:
            return f"{size:.1f} {units[unit_index]}"

class BuildWorker(QThread):
    """构建工作线程"""
    progress_updated = Signal(int, str)
    finished = Signal(bool, str)

    def __init__(self, source_dir, output_dir, device_info):
        super().__init__()
        self.source_dir = source_dir
        self.output_dir = output_dir
        self.device_info = device_info

    def run(self):
        """执行构建"""
        try:
            # 创建输出目录
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            package_name = f"{self.device_info.get('model', 'CM311-1e')}_Firmware_{timestamp}"
            full_output_dir = os.path.join(self.output_dir, package_name)

            builder = LocalFirmwareBuilder(self.source_dir, full_output_dir, self.device_info)

            # 步骤1: 创建目录结构
            self.progress_updated.emit(10, "创建目录结构...")
            success, msg = builder.create_package_structure()
            if not success:
                self.finished.emit(False, msg)
                return

            # 步骤2: 提取DDR.USB和UBOOT.USB
            self.progress_updated.emit(20, "从bootloader提取DDR.USB和UBOOT.USB...")
            success, msg = builder.extract_bootloader_files()
            if success:
                self.progress_updated.emit(30, f"Bootloader提取成功: {msg}")
            else:
                self.progress_updated.emit(30, f"Bootloader提取警告: {msg}")

            # 步骤3: 复制分区文件
            self.progress_updated.emit(40, "复制分区文件...")
            success, msg, copied_files = builder.copy_partition_files()
            if success:
                self.progress_updated.emit(60, f"文件复制成功: {msg}")
            else:
                self.finished.emit(False, msg)
                return

            # 步骤4: 创建image.cfg
            self.progress_updated.emit(70, "创建image.cfg配置文件...")
            success, msg = builder.create_image_cfg(copied_files)
            if success:
                self.progress_updated.emit(80, msg)
            else:
                self.progress_updated.emit(80, f"配置文件警告: {msg}")

            # 步骤5: 创建platform.conf
            self.progress_updated.emit(85, "创建platform.conf配置文件...")
            success, msg = builder.create_platform_conf()
            if success:
                self.progress_updated.emit(90, msg)
            else:
                self.progress_updated.emit(90, f"配置文件警告: {msg}")

            # 步骤6: 创建README
            self.progress_updated.emit(95, "创建README文件...")
            success, msg = builder.create_readme(copied_files)
            if success:
                self.progress_updated.emit(98, msg)

            self.progress_updated.emit(100, "刷机包制作完成！")
            self.finished.emit(True, builder.package_dir)

        except Exception as e:
            self.finished.emit(False, f"构建过程异常: {str(e)}")

class MainWindow(QMainWindow):
    """主窗口"""

    def __init__(self):
        super().__init__()
        self.source_dir = None
        self.output_dir = None
        self.build_worker = None
        self.device_info = {
            'model': 'CM311-1e',
            'brand': 'CM311-1e',
            'chip': 'amlogic',
            'platform': 'amlogic'
        }
        self.init_ui()
        self.apply_style()

    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("晶晨本地刷机包制作工具 - By.举个🌰")
        self.setGeometry(100, 100, 700, 500)
        self.setWindowIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))

        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 标题区域
        title_label = QLabel("🔧 晶晨本地刷机包制作工具")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #bd93f9; padding: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # 功能说明
        desc_label = QLabel("✨ 使用本地已提取的文件制作标准USB Burning Tool刷机包")
        desc_label.setStyleSheet("font-size: 12px; color: #6272a4; padding: 5px;")
        desc_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(desc_label)

        # 源文件目录选择
        source_group = QGroupBox("源文件目录")
        source_layout = QHBoxLayout(source_group)

        self.source_label = QLabel("源文件目录: 未选择")
        self.select_source_btn = QPushButton("选择源文件目录")
        self.select_source_btn.clicked.connect(self.select_source_dir)

        source_layout.addWidget(self.source_label)
        source_layout.addWidget(self.select_source_btn)

        # 输出目录选择
        output_group = QGroupBox("输出目录")
        output_layout = QHBoxLayout(output_group)

        self.output_label = QLabel("输出目录: 未选择")
        self.select_output_btn = QPushButton("选择输出目录")
        self.select_output_btn.clicked.connect(self.select_output_dir)

        output_layout.addWidget(self.output_label)
        output_layout.addWidget(self.select_output_btn)

        # 设备信息设置
        device_group = QGroupBox("设备信息")
        device_layout = QGridLayout(device_group)

        device_layout.addWidget(QLabel("设备型号:"), 0, 0)
        self.model_edit = QLineEdit(self.device_info['model'])
        device_layout.addWidget(self.model_edit, 0, 1)

        device_layout.addWidget(QLabel("设备品牌:"), 0, 2)
        self.brand_edit = QLineEdit(self.device_info['brand'])
        device_layout.addWidget(self.brand_edit, 0, 3)

        device_layout.addWidget(QLabel("芯片平台:"), 1, 0)
        self.chip_edit = QLineEdit(self.device_info['chip'])
        device_layout.addWidget(self.chip_edit, 1, 1)

        device_layout.addWidget(QLabel("硬件平台:"), 1, 2)
        self.platform_edit = QLineEdit(self.device_info['platform'])
        device_layout.addWidget(self.platform_edit, 1, 3)

        # 制作区域
        build_group = QGroupBox("刷机包制作")
        build_layout = QVBoxLayout(build_group)

        # 制作按钮
        self.build_btn = QPushButton("🚀 制作刷机包")
        self.build_btn.clicked.connect(self.build_firmware)
        self.build_btn.setEnabled(False)
        self.build_btn.setStyleSheet("font-weight: bold; font-size: 14px; padding: 10px;")

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_label = QLabel("等待开始...")

        build_layout.addWidget(self.build_btn)
        build_layout.addWidget(self.progress_bar)
        build_layout.addWidget(self.progress_label)

        # 日志区域
        log_group = QGroupBox("制作日志")
        log_layout = QVBoxLayout(log_group)
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        # 添加到主布局
        main_layout.addWidget(source_group)
        main_layout.addWidget(output_group)
        main_layout.addWidget(device_group)
        main_layout.addWidget(build_group)
        main_layout.addWidget(log_group)

    def apply_style(self):
        """应用样式"""
        style = """
        QMainWindow {
            background-color: #282a36;
            color: #f8f8f2;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #6272a4;
            border-radius: 5px;
            margin-top: 1ex;
            color: #f8f8f2;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #bd93f9;
        }
        QPushButton {
            background-color: #44475a;
            border: 1px solid #6272a4;
            border-radius: 4px;
            padding: 8px;
            color: #f8f8f2;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #6272a4;
        }
        QPushButton:pressed {
            background-color: #bd93f9;
        }
        QPushButton:disabled {
            background-color: #44475a;
            color: #6272a4;
        }
        QTextEdit, QLineEdit {
            background-color: #44475a;
            border: 1px solid #6272a4;
            border-radius: 4px;
            color: #f8f8f2;
            padding: 5px;
        }
        QProgressBar {
            border: 1px solid #6272a4;
            border-radius: 4px;
            text-align: center;
            color: #f8f8f2;
        }
        QProgressBar::chunk {
            background-color: #50fa7b;
            border-radius: 3px;
        }
        QLabel {
            color: #f8f8f2;
        }
        """
        self.setStyleSheet(style)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

    def select_source_dir(self):
        """选择源文件目录"""
        dialog = QFileDialog()
        path = dialog.getExistingDirectory(self, "选择包含分区文件的源目录")

        if path:
            self.source_dir = path
            self.source_label.setText(f"源文件目录: {path}")
            self.log_message(f"源文件目录设置为: {path}")

            # 检查必要文件
            self.check_source_files()
            self.check_ready_to_build()

    def select_output_dir(self):
        """选择输出目录"""
        dialog = QFileDialog()
        path = dialog.getExistingDirectory(self, "选择刷机包输出目录")

        if path:
            self.output_dir = path
            self.output_label.setText(f"输出目录: {path}")
            self.log_message(f"输出目录设置为: {path}")
            self.check_ready_to_build()

    def check_source_files(self):
        """检查源文件"""
        if not self.source_dir:
            return

        required_files = [
            'bootloader.PARTITION',
            'meson1.dtb'
        ]

        optional_files = [
            'boot.PARTITION',
            'recovery.PARTITION',
            'system.PARTITION',
            'vendor.PARTITION',
            'product.PARTITION',
            'logo.PARTITION',
            'param.PARTITION',
            'vbmeta.PARTITION',
            'metadata.PARTITION'
        ]

        found_required = 0
        found_optional = 0

        for file in required_files:
            if os.path.exists(os.path.join(self.source_dir, file)):
                found_required += 1
                self.log_message(f"✅ 找到必需文件: {file}")
            else:
                self.log_message(f"❌ 缺少必需文件: {file}")

        for file in optional_files:
            if os.path.exists(os.path.join(self.source_dir, file)):
                found_optional += 1
                self.log_message(f"✅ 找到可选文件: {file}")

        self.log_message(f"📊 文件检查完成: 必需文件 {found_required}/{len(required_files)}, 可选文件 {found_optional}/{len(optional_files)}")

        if found_required < len(required_files):
            self.log_message("⚠️  缺少必需文件，可能影响刷机包制作")

    def check_ready_to_build(self):
        """检查是否可以开始制作"""
        if self.source_dir and self.output_dir:
            # 检查是否有bootloader.PARTITION
            bootloader_path = os.path.join(self.source_dir, "bootloader.PARTITION")
            if os.path.exists(bootloader_path):
                self.build_btn.setEnabled(True)
            else:
                self.build_btn.setEnabled(False)
                self.log_message("❌ 缺少bootloader.PARTITION文件，无法制作")

    def update_device_info(self):
        """更新设备信息"""
        self.device_info = {
            'model': self.model_edit.text().strip() or 'CM311-1e',
            'brand': self.brand_edit.text().strip() or 'CM311-1e',
            'chip': self.chip_edit.text().strip() or 'amlogic',
            'platform': self.platform_edit.text().strip() or 'amlogic'
        }

    def build_firmware(self):
        """制作刷机包"""
        if not self.source_dir or not self.output_dir:
            self.log_message("请先选择源文件目录和输出目录")
            return

        # 更新设备信息
        self.update_device_info()

        # 禁用按钮
        self.build_btn.setEnabled(False)
        self.build_btn.setText("制作中...")

        # 启动构建线程
        self.build_worker = BuildWorker(self.source_dir, self.output_dir, self.device_info)
        self.build_worker.progress_updated.connect(self.update_progress)
        self.build_worker.finished.connect(self.on_build_finished)
        self.build_worker.start()

        self.log_message("开始制作刷机包...")

    def update_progress(self, value, message):
        """更新进度"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(message)
        self.log_message(message)

    def on_build_finished(self, success, message):
        """构建完成回调"""
        self.build_btn.setEnabled(True)
        self.build_btn.setText("🚀 制作刷机包")

        if success:
            self.log_message("✅ 刷机包制作完成！")

            # 显示完成对话框
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("制作完成")
            msg_box.setText("刷机包制作完成！")
            msg_box.setDetailedText(f"输出路径: {message}")
            msg_box.setStandardButtons(QMessageBox.Ok | QMessageBox.Open)
            msg_box.setDefaultButton(QMessageBox.Open)

            result = msg_box.exec()
            if result == QMessageBox.Open:
                # 打开输出目录
                if os.name == 'nt':  # Windows
                    os.startfile(message)
                else:  # Linux/Mac
                    import subprocess
                    subprocess.run(['xdg-open', message])
        else:
            self.log_message(f"❌ 刷机包制作失败: {message}")
            QMessageBox.warning(self, "制作失败", message)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("晶晨本地刷机包制作工具")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("By.举个🌰")

    window = MainWindow()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
