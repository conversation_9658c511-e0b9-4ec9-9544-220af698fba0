# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtWebEngineCore, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtWebEngineCore`

import PySide6.QtWebEngineCore
import PySide6.QtCore
import PySide6.QtGui
import PySide6.QtNetwork
import PySide6.QtWebChannel

import enum
import typing
import collections.abc
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QIntList: ...


class QWebEngineCertificateError(Shiboken.Object):

    class Type(enum.Enum):

        SslObsoleteVersion        = ...  # -218
        CertificateKnownInterceptionBlocked = ...  # -217
        CertificateSymantecLegacy = ...  # -215
        CertificateTransparencyRequired = ...  # -214
        CertificateValidityTooLong = ...  # -213
        CertificateNameConstraintViolation = ...  # -212
        CertificateWeakKey        = ...  # -211
        CertificateNonUniqueName  = ...  # -210
        CertificateWeakSignatureAlgorithm = ...  # -208
        CertificateInvalid        = ...  # -207
        CertificateRevoked        = ...  # -206
        CertificateUnableToCheckRevocation = ...  # -205
        CertificateNoRevocationMechanism = ...  # -204
        CertificateContainsErrors = ...  # -203
        CertificateAuthorityInvalid = ...  # -202
        CertificateDateInvalid    = ...  # -201
        CertificateCommonNameInvalid = ...  # -200
        SslPinnedKeyNotInCertificateChain = ...  # -150
        Ok                        = ...  # 0x0


    @typing.overload
    def __init__(self, other: PySide6.QtWebEngineCore.QWebEngineCertificateError, /, *, url: PySide6.QtCore.QUrl | None = ..., type: PySide6.QtWebEngineCore.QWebEngineCertificateError.Type | None = ..., description: str | None = ..., overridable: bool | None = ..., isMainFrame: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, url: PySide6.QtCore.QUrl | None = ..., type: PySide6.QtWebEngineCore.QWebEngineCertificateError.Type | None = ..., description: str | None = ..., overridable: bool | None = ..., isMainFrame: bool | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def acceptCertificate(self, /) -> None: ...
    def certificateChain(self, /) -> typing.List[PySide6.QtNetwork.QSslCertificate]: ...
    def defer(self, /) -> None: ...
    def description(self, /) -> str: ...
    def isMainFrame(self, /) -> bool: ...
    def isOverridable(self, /) -> bool: ...
    def rejectCertificate(self, /) -> None: ...
    def type(self, /) -> PySide6.QtWebEngineCore.QWebEngineCertificateError.Type: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...


class QWebEngineClientCertificateSelection(Shiboken.Object):

    def __init__(self, arg__1: PySide6.QtWebEngineCore.QWebEngineClientCertificateSelection, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def certificates(self, /) -> typing.List[PySide6.QtNetwork.QSslCertificate]: ...
    def host(self, /) -> PySide6.QtCore.QUrl: ...
    def select(self, certificate: PySide6.QtNetwork.QSslCertificate | PySide6.QtCore.QIODevice, /) -> None: ...
    def selectNone(self, /) -> None: ...


class QWebEngineClientCertificateStore(Shiboken.Object):
    def add(self, certificate: PySide6.QtNetwork.QSslCertificate | PySide6.QtCore.QIODevice, privateKey: PySide6.QtNetwork.QSslKey | int, /) -> None: ...
    def certificates(self, /) -> typing.List[PySide6.QtNetwork.QSslCertificate]: ...
    def clear(self, /) -> None: ...
    def remove(self, certificate: PySide6.QtNetwork.QSslCertificate | PySide6.QtCore.QIODevice, /) -> None: ...


class QWebEngineClientHints(PySide6.QtCore.QObject):
    def arch(self, /) -> str: ...
    def bitness(self, /) -> str: ...
    def fullVersion(self, /) -> str: ...
    def fullVersionList(self, /) -> typing.Dict[str, typing.Any]: ...
    def isAllClientHintsEnabled(self, /) -> bool: ...
    def isMobile(self, /) -> bool: ...
    def isWow64(self, /) -> bool: ...
    def model(self, /) -> str: ...
    def platform(self, /) -> str: ...
    def platformVersion(self, /) -> str: ...
    def qt_qmlMarker_uncreatable(self, /) -> None: ...
    def resetAll(self, /) -> None: ...
    def setAllClientHintsEnabled(self, enabled: bool, /) -> None: ...
    def setArch(self, arg__1: str, /) -> None: ...
    def setBitness(self, arg__1: str, /) -> None: ...
    def setFullVersion(self, arg__1: str, /) -> None: ...
    def setFullVersionList(self, arg__1: typing.Dict[str, typing.Any], /) -> None: ...
    def setIsMobile(self, arg__1: bool, /) -> None: ...
    def setIsWow64(self, arg__1: bool, /) -> None: ...
    def setModel(self, arg__1: str, /) -> None: ...
    def setPlatform(self, arg__1: str, /) -> None: ...
    def setPlatformVersion(self, arg__1: str, /) -> None: ...


class QWebEngineContextMenuRequest(PySide6.QtCore.QObject):

    class EditFlag(enum.Flag):

        CanUndo                   = ...  # 0x1
        CanRedo                   = ...  # 0x2
        CanCut                    = ...  # 0x4
        CanCopy                   = ...  # 0x8
        CanPaste                  = ...  # 0x10
        CanDelete                 = ...  # 0x20
        CanSelectAll              = ...  # 0x40
        CanTranslate              = ...  # 0x80
        CanEditRichly             = ...  # 0x100

    class MediaFlag(enum.Flag):

        MediaInError              = ...  # 0x1
        MediaPaused               = ...  # 0x2
        MediaMuted                = ...  # 0x4
        MediaLoop                 = ...  # 0x8
        MediaCanSave              = ...  # 0x10
        MediaHasAudio             = ...  # 0x20
        MediaCanToggleControls    = ...  # 0x40
        MediaControls             = ...  # 0x80
        MediaCanPrint             = ...  # 0x100
        MediaCanRotate            = ...  # 0x200

    class MediaType(enum.Enum):

        MediaTypeNone             = ...  # 0x0
        MediaTypeImage            = ...  # 0x1
        MediaTypeVideo            = ...  # 0x2
        MediaTypeAudio            = ...  # 0x3
        MediaTypeCanvas           = ...  # 0x4
        MediaTypeFile             = ...  # 0x5
        MediaTypePlugin           = ...  # 0x6


    def editFlags(self, /) -> PySide6.QtWebEngineCore.QWebEngineContextMenuRequest.EditFlag: ...
    def isAccepted(self, /) -> bool: ...
    def isContentEditable(self, /) -> bool: ...
    def linkText(self, /) -> str: ...
    def linkUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def mediaFlags(self, /) -> PySide6.QtWebEngineCore.QWebEngineContextMenuRequest.MediaFlag: ...
    def mediaType(self, /) -> PySide6.QtWebEngineCore.QWebEngineContextMenuRequest.MediaType: ...
    def mediaUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def misspelledWord(self, /) -> str: ...
    def position(self, /) -> PySide6.QtCore.QPoint: ...
    def selectedText(self, /) -> str: ...
    def setAccepted(self, accepted: bool, /) -> None: ...
    def spellCheckerSuggestions(self, /) -> typing.List[str]: ...


class QWebEngineCookieStore(PySide6.QtCore.QObject):

    cookieAdded              : typing.ClassVar[Signal] = ... # cookieAdded(QNetworkCookie)
    cookieRemoved            : typing.ClassVar[Signal] = ... # cookieRemoved(QNetworkCookie)

    class FilterRequest(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, FilterRequest: PySide6.QtWebEngineCore.QWebEngineCookieStore.FilterRequest, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...


    def deleteAllCookies(self, /) -> None: ...
    def deleteCookie(self, cookie: PySide6.QtNetwork.QNetworkCookie, /, origin: PySide6.QtCore.QUrl | str = ...) -> None: ...
    def deleteSessionCookies(self, /) -> None: ...
    def loadAllCookies(self, /) -> None: ...
    def setCookie(self, cookie: PySide6.QtNetwork.QNetworkCookie, /, origin: PySide6.QtCore.QUrl | str = ...) -> None: ...
    def setCookieFilter(self, filterCallback: collections.abc.Callable[..., typing.Any], /) -> None: ...


class QWebEngineDesktopMediaRequest(Shiboken.Object):

    def __init__(self, other: PySide6.QtWebEngineCore.QWebEngineDesktopMediaRequest, /, *, screensModel: PySide6.QtCore.QAbstractListModel | None = ..., windowsModel: PySide6.QtCore.QAbstractListModel | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def cancel(self, /) -> None: ...
    def screensModel(self, /) -> PySide6.QtCore.QAbstractListModel: ...
    def selectScreen(self, index: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex, /) -> None: ...
    def selectWindow(self, index: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex, /) -> None: ...
    def windowsModel(self, /) -> PySide6.QtCore.QAbstractListModel: ...


class QWebEngineDownloadRequest(PySide6.QtCore.QObject):

    downloadDirectoryChanged : typing.ClassVar[Signal] = ... # downloadDirectoryChanged()
    downloadFileNameChanged  : typing.ClassVar[Signal] = ... # downloadFileNameChanged()
    interruptReasonChanged   : typing.ClassVar[Signal] = ... # interruptReasonChanged()
    isFinishedChanged        : typing.ClassVar[Signal] = ... # isFinishedChanged()
    isPausedChanged          : typing.ClassVar[Signal] = ... # isPausedChanged()
    receivedBytesChanged     : typing.ClassVar[Signal] = ... # receivedBytesChanged()
    savePageFormatChanged    : typing.ClassVar[Signal] = ... # savePageFormatChanged()
    stateChanged             : typing.ClassVar[Signal] = ... # stateChanged(QWebEngineDownloadRequest::DownloadState)
    totalBytesChanged        : typing.ClassVar[Signal] = ... # totalBytesChanged()

    class DownloadInterruptReason(enum.Enum):

        NoReason                  = ...  # 0x0
        FileFailed                = ...  # 0x1
        FileAccessDenied          = ...  # 0x2
        FileNoSpace               = ...  # 0x3
        FileNameTooLong           = ...  # 0x5
        FileTooLarge              = ...  # 0x6
        FileVirusInfected         = ...  # 0x7
        FileTransientError        = ...  # 0xa
        FileBlocked               = ...  # 0xb
        FileSecurityCheckFailed   = ...  # 0xc
        FileTooShort              = ...  # 0xd
        FileHashMismatch          = ...  # 0xe
        NetworkFailed             = ...  # 0x14
        NetworkTimeout            = ...  # 0x15
        NetworkDisconnected       = ...  # 0x16
        NetworkServerDown         = ...  # 0x17
        NetworkInvalidRequest     = ...  # 0x18
        ServerFailed              = ...  # 0x1e
        ServerBadContent          = ...  # 0x21
        ServerUnauthorized        = ...  # 0x22
        ServerCertProblem         = ...  # 0x23
        ServerForbidden           = ...  # 0x24
        ServerUnreachable         = ...  # 0x25
        UserCanceled              = ...  # 0x28

    class DownloadState(enum.Enum):

        DownloadRequested         = ...  # 0x0
        DownloadInProgress        = ...  # 0x1
        DownloadCompleted         = ...  # 0x2
        DownloadCancelled         = ...  # 0x3
        DownloadInterrupted       = ...  # 0x4

    class SavePageFormat(enum.Enum):

        UnknownSaveFormat         = ...  # -1
        SingleHtmlSaveFormat      = ...  # 0x0
        CompleteHtmlSaveFormat    = ...  # 0x1
        MimeHtmlSaveFormat        = ...  # 0x2


    def accept(self, /) -> None: ...
    def cancel(self, /) -> None: ...
    def downloadDirectory(self, /) -> str: ...
    def downloadFileName(self, /) -> str: ...
    def id(self, /) -> int: ...
    def interruptReason(self, /) -> PySide6.QtWebEngineCore.QWebEngineDownloadRequest.DownloadInterruptReason: ...
    def interruptReasonString(self, /) -> str: ...
    def isFinished(self, /) -> bool: ...
    def isPaused(self, /) -> bool: ...
    def isSavePageDownload(self, /) -> bool: ...
    def mimeType(self, /) -> str: ...
    def page(self, /) -> PySide6.QtWebEngineCore.QWebEnginePage: ...
    def pause(self, /) -> None: ...
    def receivedBytes(self, /) -> int: ...
    def resume(self, /) -> None: ...
    def savePageFormat(self, /) -> PySide6.QtWebEngineCore.QWebEngineDownloadRequest.SavePageFormat: ...
    def setDownloadDirectory(self, directory: str, /) -> None: ...
    def setDownloadFileName(self, fileName: str, /) -> None: ...
    def setSavePageFormat(self, format: PySide6.QtWebEngineCore.QWebEngineDownloadRequest.SavePageFormat, /) -> None: ...
    def state(self, /) -> PySide6.QtWebEngineCore.QWebEngineDownloadRequest.DownloadState: ...
    def suggestedFileName(self, /) -> str: ...
    def totalBytes(self, /) -> int: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...


class QWebEngineFileSystemAccessRequest(Shiboken.Object):

    class AccessFlag(enum.Flag):

        Read                      = ...  # 0x1
        Write                     = ...  # 0x2

    class HandleType(enum.Enum):

        File                      = ...  # 0x0
        Directory                 = ...  # 0x1


    @typing.overload
    def __init__(self, other: PySide6.QtWebEngineCore.QWebEngineFileSystemAccessRequest, /, *, origin: PySide6.QtCore.QUrl | None = ..., filePath: PySide6.QtCore.QUrl | None = ..., handleType: PySide6.QtWebEngineCore.QWebEngineFileSystemAccessRequest.HandleType | None = ..., accessFlags: PySide6.QtWebEngineCore.QWebEngineFileSystemAccessRequest.AccessFlag | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, origin: PySide6.QtCore.QUrl | None = ..., filePath: PySide6.QtCore.QUrl | None = ..., handleType: PySide6.QtWebEngineCore.QWebEngineFileSystemAccessRequest.HandleType | None = ..., accessFlags: PySide6.QtWebEngineCore.QWebEngineFileSystemAccessRequest.AccessFlag | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtWebEngineCore.QWebEngineFileSystemAccessRequest, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtWebEngineCore.QWebEngineFileSystemAccessRequest, /) -> bool: ...
    def accept(self, /) -> None: ...
    def accessFlags(self, /) -> PySide6.QtWebEngineCore.QWebEngineFileSystemAccessRequest.AccessFlag: ...
    def filePath(self, /) -> PySide6.QtCore.QUrl: ...
    def handleType(self, /) -> PySide6.QtWebEngineCore.QWebEngineFileSystemAccessRequest.HandleType: ...
    def origin(self, /) -> PySide6.QtCore.QUrl: ...
    def reject(self, /) -> None: ...


class QWebEngineFindTextResult(Shiboken.Object):

    @typing.overload
    def __init__(self, other: PySide6.QtWebEngineCore.QWebEngineFindTextResult, /, *, numberOfMatches: int | None = ..., activeMatch: int | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, numberOfMatches: int | None = ..., activeMatch: int | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def activeMatch(self, /) -> int: ...
    def numberOfMatches(self, /) -> int: ...


class QWebEngineFrame(Shiboken.Object):

    def __init__(self, QWebEngineFrame: PySide6.QtWebEngineCore.QWebEngineFrame, /, *, isValid: bool | None = ..., name: str | None = ..., htmlName: str | None = ..., url: PySide6.QtCore.QUrl | None = ..., size: PySide6.QtCore.QSizeF | None = ..., isMainFrame: bool | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtWebEngineCore.QWebEngineFrame, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtWebEngineCore.QWebEngineFrame, /) -> bool: ...
    def children(self, /) -> typing.List[PySide6.QtWebEngineCore.QWebEngineFrame]: ...
    def htmlName(self, /) -> str: ...
    def isMainFrame(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def name(self, /) -> str: ...
    @typing.overload
    def printToPdf(self, filePath: str, /) -> None: ...
    @typing.overload
    def printToPdf(self, resultCallback: collections.abc.Callable[..., typing.Any], /) -> None: ...
    @typing.overload
    def runJavaScript(self, scriptSource: str, resultCallback: collections.abc.Callable[..., typing.Any], /) -> None: ...
    @typing.overload
    def runJavaScript(self, scriptSource: str, worldId: int, /, resultCallback: collections.abc.Callable[..., typing.Any] = ...) -> None: ...
    def size(self, /) -> PySide6.QtCore.QSizeF: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...


class QWebEngineFullScreenRequest(Shiboken.Object):

    @typing.overload
    def __init__(self, other: PySide6.QtWebEngineCore.QWebEngineFullScreenRequest, /, *, toggleOn: bool | None = ..., origin: PySide6.QtCore.QUrl | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, toggleOn: bool | None = ..., origin: PySide6.QtCore.QUrl | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def accept(self, /) -> None: ...
    def origin(self, /) -> PySide6.QtCore.QUrl: ...
    def reject(self, /) -> None: ...
    def toggleOn(self, /) -> bool: ...


class QWebEngineGlobalSettings(Shiboken.Object):

    class DnsMode(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, DnsMode: PySide6.QtWebEngineCore.QWebEngineGlobalSettings.DnsMode, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...

    class SecureDnsMode(enum.Enum):

        SystemOnly                = ...  # 0x0
        SecureWithFallback        = ...  # 0x1
        SecureOnly                = ...  # 0x2


    @staticmethod
    def setDnsMode(dnsMode: PySide6.QtWebEngineCore.QWebEngineGlobalSettings.DnsMode, /) -> bool: ...


class QWebEngineHistory(PySide6.QtCore.QObject):
    def __lshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __rshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def back(self, /) -> None: ...
    def backItem(self, /) -> PySide6.QtWebEngineCore.QWebEngineHistoryItem: ...
    def backItems(self, maxItems: int, /) -> typing.List[PySide6.QtWebEngineCore.QWebEngineHistoryItem]: ...
    def backItemsModel(self, /) -> PySide6.QtWebEngineCore.QWebEngineHistoryModel: ...
    def canGoBack(self, /) -> bool: ...
    def canGoForward(self, /) -> bool: ...
    def clear(self, /) -> None: ...
    def count(self, /) -> int: ...
    def currentItem(self, /) -> PySide6.QtWebEngineCore.QWebEngineHistoryItem: ...
    def currentItemIndex(self, /) -> int: ...
    def forward(self, /) -> None: ...
    def forwardItem(self, /) -> PySide6.QtWebEngineCore.QWebEngineHistoryItem: ...
    def forwardItems(self, maxItems: int, /) -> typing.List[PySide6.QtWebEngineCore.QWebEngineHistoryItem]: ...
    def forwardItemsModel(self, /) -> PySide6.QtWebEngineCore.QWebEngineHistoryModel: ...
    def goToItem(self, item: PySide6.QtWebEngineCore.QWebEngineHistoryItem, /) -> None: ...
    def itemAt(self, i: int, /) -> PySide6.QtWebEngineCore.QWebEngineHistoryItem: ...
    def items(self, /) -> typing.List[PySide6.QtWebEngineCore.QWebEngineHistoryItem]: ...
    def itemsModel(self, /) -> PySide6.QtWebEngineCore.QWebEngineHistoryModel: ...


class QWebEngineHistoryItem(Shiboken.Object):

    def __init__(self, other: PySide6.QtWebEngineCore.QWebEngineHistoryItem, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def iconUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def isValid(self, /) -> bool: ...
    def lastVisited(self, /) -> PySide6.QtCore.QDateTime: ...
    def originalUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def title(self, /) -> str: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...


class QWebEngineHistoryModel(PySide6.QtCore.QAbstractListModel):

    class Roles(enum.Enum):

        UrlRole                   = ...  # 0x100
        TitleRole                 = ...  # 0x101
        OffsetRole                = ...  # 0x102
        IconUrlRole               = ...  # 0x103


    def data(self, index: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex, /, role: int = ...) -> typing.Any: ...
    def reset(self, /) -> None: ...
    def roleNames(self, /) -> typing.Dict[int, PySide6.QtCore.QByteArray]: ...
    def rowCount(self, /, parent: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex = ...) -> int: ...


class QWebEngineHttpRequest(Shiboken.Object):

    class Method(enum.Enum):

        Get                       = ...  # 0x0
        Post                      = ...  # 0x1


    @typing.overload
    def __init__(self, other: PySide6.QtWebEngineCore.QWebEngineHttpRequest, /) -> None: ...
    @typing.overload
    def __init__(self, /, url: PySide6.QtCore.QUrl | str = ..., method: PySide6.QtWebEngineCore.QWebEngineHttpRequest.Method = ...) -> None: ...

    def __eq__(self, other: PySide6.QtWebEngineCore.QWebEngineHttpRequest, /) -> bool: ...
    def __ne__(self, other: PySide6.QtWebEngineCore.QWebEngineHttpRequest, /) -> bool: ...
    def hasHeader(self, headerName: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> bool: ...
    def header(self, headerName: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtCore.QByteArray: ...
    def headers(self, /) -> typing.List[PySide6.QtCore.QByteArray]: ...
    def method(self, /) -> PySide6.QtWebEngineCore.QWebEngineHttpRequest.Method: ...
    def postData(self, /) -> PySide6.QtCore.QByteArray: ...
    @staticmethod
    def postRequest(url: PySide6.QtCore.QUrl | str, postData: typing.Dict[str, str], /) -> PySide6.QtWebEngineCore.QWebEngineHttpRequest: ...
    def setHeader(self, headerName: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, value: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setMethod(self, method: PySide6.QtWebEngineCore.QWebEngineHttpRequest.Method, /) -> None: ...
    def setPostData(self, postData: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setUrl(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    def swap(self, other: PySide6.QtWebEngineCore.QWebEngineHttpRequest, /) -> None: ...
    def unsetHeader(self, headerName: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...


class QWebEngineLoadingInfo(Shiboken.Object):

    class ErrorDomain(enum.Enum):

        NoErrorDomain             = ...  # 0x0
        InternalErrorDomain       = ...  # 0x1
        ConnectionErrorDomain     = ...  # 0x2
        CertificateErrorDomain    = ...  # 0x3
        HttpErrorDomain           = ...  # 0x4
        FtpErrorDomain            = ...  # 0x5
        DnsErrorDomain            = ...  # 0x6
        HttpStatusCodeDomain      = ...  # 0x7

    class LoadStatus(enum.Enum):

        LoadStartedStatus         = ...  # 0x0
        LoadStoppedStatus         = ...  # 0x1
        LoadSucceededStatus       = ...  # 0x2
        LoadFailedStatus          = ...  # 0x3


    @typing.overload
    def __init__(self, other: PySide6.QtWebEngineCore.QWebEngineLoadingInfo, /, *, url: PySide6.QtCore.QUrl | None = ..., isErrorPage: bool | None = ..., status: PySide6.QtWebEngineCore.QWebEngineLoadingInfo.LoadStatus | None = ..., errorString: str | None = ..., errorDomain: PySide6.QtWebEngineCore.QWebEngineLoadingInfo.ErrorDomain | None = ..., errorCode: int | None = ..., responseHeaders: typing.Optional[typing.Dict[PySide6.QtCore.QByteArray, PySide6.QtCore.QByteArray]] = ..., isDownload: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, url: PySide6.QtCore.QUrl | None = ..., isErrorPage: bool | None = ..., status: PySide6.QtWebEngineCore.QWebEngineLoadingInfo.LoadStatus | None = ..., errorString: str | None = ..., errorDomain: PySide6.QtWebEngineCore.QWebEngineLoadingInfo.ErrorDomain | None = ..., errorCode: int | None = ..., responseHeaders: typing.Optional[typing.Dict[PySide6.QtCore.QByteArray, PySide6.QtCore.QByteArray]] = ..., isDownload: bool | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def errorCode(self, /) -> int: ...
    def errorDomain(self, /) -> PySide6.QtWebEngineCore.QWebEngineLoadingInfo.ErrorDomain: ...
    def errorString(self, /) -> str: ...
    def isDownload(self, /) -> bool: ...
    def isErrorPage(self, /) -> bool: ...
    def responseHeaders(self, /) -> typing.Dict[PySide6.QtCore.QByteArray, PySide6.QtCore.QByteArray]: ...
    def status(self, /) -> PySide6.QtWebEngineCore.QWebEngineLoadingInfo.LoadStatus: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...


class QWebEngineNavigationRequest(PySide6.QtCore.QObject):

    actionChanged            : typing.ClassVar[Signal] = ... # actionChanged()

    class NavigationRequestAction(enum.Enum):

        AcceptRequest             = ...  # 0x0
        IgnoreRequest             = ...  # 0xff

    class NavigationType(enum.Enum):

        LinkClickedNavigation     = ...  # 0x0
        TypedNavigation           = ...  # 0x1
        FormSubmittedNavigation   = ...  # 0x2
        BackForwardNavigation     = ...  # 0x3
        ReloadNavigation          = ...  # 0x4
        OtherNavigation           = ...  # 0x5
        RedirectNavigation        = ...  # 0x6


    def accept(self, /) -> None: ...
    def hasFormData(self, /) -> bool: ...
    def isMainFrame(self, /) -> bool: ...
    def navigationType(self, /) -> PySide6.QtWebEngineCore.QWebEngineNavigationRequest.NavigationType: ...
    def reject(self, /) -> None: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...


class QWebEngineNewWindowRequest(PySide6.QtCore.QObject):

    class DestinationType(enum.Enum):

        InNewWindow               = ...  # 0x0
        InNewTab                  = ...  # 0x1
        InNewDialog               = ...  # 0x2
        InNewBackgroundTab        = ...  # 0x3


    def destination(self, /) -> PySide6.QtWebEngineCore.QWebEngineNewWindowRequest.DestinationType: ...
    def isUserInitiated(self, /) -> bool: ...
    def openIn(self, arg__1: PySide6.QtWebEngineCore.QWebEnginePage, /) -> None: ...
    def requestedGeometry(self, /) -> PySide6.QtCore.QRect: ...
    def requestedUrl(self, /) -> PySide6.QtCore.QUrl: ...


class QWebEngineNotification(PySide6.QtCore.QObject):

    closed                   : typing.ClassVar[Signal] = ... # closed()
    def click(self, /) -> None: ...
    def close(self, /) -> None: ...
    def direction(self, /) -> PySide6.QtCore.Qt.LayoutDirection: ...
    def icon(self, /) -> PySide6.QtGui.QImage: ...
    def language(self, /) -> str: ...
    def matches(self, other: PySide6.QtWebEngineCore.QWebEngineNotification, /) -> bool: ...
    def message(self, /) -> str: ...
    def origin(self, /) -> PySide6.QtCore.QUrl: ...
    def show(self, /) -> None: ...
    def tag(self, /) -> str: ...
    def title(self, /) -> str: ...


class QWebEnginePage(PySide6.QtCore.QObject):

    _q_aboutToDelete         : typing.ClassVar[Signal] = ... # _q_aboutToDelete()
    audioMutedChanged        : typing.ClassVar[Signal] = ... # audioMutedChanged(bool)
    authenticationRequired   : typing.ClassVar[Signal] = ... # authenticationRequired(QUrl,QAuthenticator*)
    certificateError         : typing.ClassVar[Signal] = ... # certificateError(QWebEngineCertificateError)
    contentsSizeChanged      : typing.ClassVar[Signal] = ... # contentsSizeChanged(QSizeF)
    desktopMediaRequested    : typing.ClassVar[Signal] = ... # desktopMediaRequested(QWebEngineDesktopMediaRequest)
    featurePermissionRequestCanceled: typing.ClassVar[Signal] = ... # featurePermissionRequestCanceled(QUrl,QWebEnginePage::Feature)
    featurePermissionRequested: typing.ClassVar[Signal] = ... # featurePermissionRequested(QUrl,QWebEnginePage::Feature)
    fileSystemAccessRequested: typing.ClassVar[Signal] = ... # fileSystemAccessRequested(QWebEngineFileSystemAccessRequest)
    findTextFinished         : typing.ClassVar[Signal] = ... # findTextFinished(QWebEngineFindTextResult)
    fullScreenRequested      : typing.ClassVar[Signal] = ... # fullScreenRequested(QWebEngineFullScreenRequest)
    geometryChangeRequested  : typing.ClassVar[Signal] = ... # geometryChangeRequested(QRect)
    iconChanged              : typing.ClassVar[Signal] = ... # iconChanged(QIcon)
    iconUrlChanged           : typing.ClassVar[Signal] = ... # iconUrlChanged(QUrl)
    lifecycleStateChanged    : typing.ClassVar[Signal] = ... # lifecycleStateChanged(LifecycleState)
    linkHovered              : typing.ClassVar[Signal] = ... # linkHovered(QString)
    loadFinished             : typing.ClassVar[Signal] = ... # loadFinished(bool)
    loadProgress             : typing.ClassVar[Signal] = ... # loadProgress(int)
    loadStarted              : typing.ClassVar[Signal] = ... # loadStarted()
    loadingChanged           : typing.ClassVar[Signal] = ... # loadingChanged(QWebEngineLoadingInfo)
    navigationRequested      : typing.ClassVar[Signal] = ... # navigationRequested(QWebEngineNavigationRequest&)
    newWindowRequested       : typing.ClassVar[Signal] = ... # newWindowRequested(QWebEngineNewWindowRequest&)
    pdfPrintingFinished      : typing.ClassVar[Signal] = ... # pdfPrintingFinished(QString,bool)
    permissionRequested      : typing.ClassVar[Signal] = ... # permissionRequested(QWebEnginePermission)
    printRequested           : typing.ClassVar[Signal] = ... # printRequested()
    printRequestedByFrame    : typing.ClassVar[Signal] = ... # printRequestedByFrame(QWebEngineFrame)
    proxyAuthenticationRequired: typing.ClassVar[Signal] = ... # proxyAuthenticationRequired(QUrl,QAuthenticator*,QString)
    quotaRequested           : typing.ClassVar[Signal] = ... # quotaRequested(QWebEngineQuotaRequest)
    recentlyAudibleChanged   : typing.ClassVar[Signal] = ... # recentlyAudibleChanged(bool)
    recommendedStateChanged  : typing.ClassVar[Signal] = ... # recommendedStateChanged(LifecycleState)
    registerProtocolHandlerRequested: typing.ClassVar[Signal] = ... # registerProtocolHandlerRequested(QWebEngineRegisterProtocolHandlerRequest)
    renderProcessPidChanged  : typing.ClassVar[Signal] = ... # renderProcessPidChanged(qlonglong)
    renderProcessTerminated  : typing.ClassVar[Signal] = ... # renderProcessTerminated(RenderProcessTerminationStatus,int)
    scrollPositionChanged    : typing.ClassVar[Signal] = ... # scrollPositionChanged(QPointF)
    selectClientCertificate  : typing.ClassVar[Signal] = ... # selectClientCertificate(QWebEngineClientCertificateSelection)
    selectionChanged         : typing.ClassVar[Signal] = ... # selectionChanged()
    titleChanged             : typing.ClassVar[Signal] = ... # titleChanged(QString)
    urlChanged               : typing.ClassVar[Signal] = ... # urlChanged(QUrl)
    visibleChanged           : typing.ClassVar[Signal] = ... # visibleChanged(bool)
    webAuthUxRequested       : typing.ClassVar[Signal] = ... # webAuthUxRequested(QWebEngineWebAuthUxRequest*)
    windowCloseRequested     : typing.ClassVar[Signal] = ... # windowCloseRequested()
    zoomFactorChanged        : typing.ClassVar[Signal] = ... # zoomFactorChanged(double)

    class Feature(enum.Enum):

        Notifications             = ...  # 0x0
        Geolocation               = ...  # 0x1
        MediaAudioCapture         = ...  # 0x2
        MediaVideoCapture         = ...  # 0x3
        MediaAudioVideoCapture    = ...  # 0x4
        MouseLock                 = ...  # 0x5
        DesktopVideoCapture       = ...  # 0x6
        DesktopAudioVideoCapture  = ...  # 0x7
        ClipboardReadWrite        = ...  # 0x8
        LocalFontsAccess          = ...  # 0x9

    class FileSelectionMode(enum.Enum):

        FileSelectOpen            = ...  # 0x0
        FileSelectOpenMultiple    = ...  # 0x1
        FileSelectUploadFolder    = ...  # 0x2
        FileSelectSave            = ...  # 0x3

    class FindFlag(enum.Flag):

        FindBackward              = ...  # 0x1
        FindCaseSensitively       = ...  # 0x2

    class JavaScriptConsoleMessageLevel(enum.Enum):

        InfoMessageLevel          = ...  # 0x0
        WarningMessageLevel       = ...  # 0x1
        ErrorMessageLevel         = ...  # 0x2

    class LifecycleState(enum.Enum):

        Active                    = ...  # 0x0
        Frozen                    = ...  # 0x1
        Discarded                 = ...  # 0x2

    class NavigationType(enum.Enum):

        NavigationTypeLinkClicked = ...  # 0x0
        NavigationTypeTyped       = ...  # 0x1
        NavigationTypeFormSubmitted = ...  # 0x2
        NavigationTypeBackForward = ...  # 0x3
        NavigationTypeReload      = ...  # 0x4
        NavigationTypeOther       = ...  # 0x5
        NavigationTypeRedirect    = ...  # 0x6

    class PermissionPolicy(enum.Enum):

        PermissionUnknown         = ...  # 0x0
        PermissionGrantedByUser   = ...  # 0x1
        PermissionDeniedByUser    = ...  # 0x2

    class RenderProcessTerminationStatus(enum.Enum):

        NormalTerminationStatus   = ...  # 0x0
        AbnormalTerminationStatus = ...  # 0x1
        CrashedTerminationStatus  = ...  # 0x2
        KilledTerminationStatus   = ...  # 0x3

    class WebAction(enum.Enum):

        NoWebAction               = ...  # -1
        Back                      = ...  # 0x0
        Forward                   = ...  # 0x1
        Stop                      = ...  # 0x2
        Reload                    = ...  # 0x3
        Cut                       = ...  # 0x4
        Copy                      = ...  # 0x5
        Paste                     = ...  # 0x6
        Undo                      = ...  # 0x7
        Redo                      = ...  # 0x8
        SelectAll                 = ...  # 0x9
        ReloadAndBypassCache      = ...  # 0xa
        PasteAndMatchStyle        = ...  # 0xb
        OpenLinkInThisWindow      = ...  # 0xc
        OpenLinkInNewWindow       = ...  # 0xd
        OpenLinkInNewTab          = ...  # 0xe
        CopyLinkToClipboard       = ...  # 0xf
        DownloadLinkToDisk        = ...  # 0x10
        CopyImageToClipboard      = ...  # 0x11
        CopyImageUrlToClipboard   = ...  # 0x12
        DownloadImageToDisk       = ...  # 0x13
        CopyMediaUrlToClipboard   = ...  # 0x14
        ToggleMediaControls       = ...  # 0x15
        ToggleMediaLoop           = ...  # 0x16
        ToggleMediaPlayPause      = ...  # 0x17
        ToggleMediaMute           = ...  # 0x18
        DownloadMediaToDisk       = ...  # 0x19
        InspectElement            = ...  # 0x1a
        ExitFullScreen            = ...  # 0x1b
        RequestClose              = ...  # 0x1c
        Unselect                  = ...  # 0x1d
        SavePage                  = ...  # 0x1e
        OpenLinkInNewBackgroundTab = ...  # 0x1f
        ViewSource                = ...  # 0x20
        ToggleBold                = ...  # 0x21
        ToggleItalic              = ...  # 0x22
        ToggleUnderline           = ...  # 0x23
        ToggleStrikethrough       = ...  # 0x24
        AlignLeft                 = ...  # 0x25
        AlignCenter               = ...  # 0x26
        AlignRight                = ...  # 0x27
        AlignJustified            = ...  # 0x28
        Indent                    = ...  # 0x29
        Outdent                   = ...  # 0x2a
        InsertOrderedList         = ...  # 0x2b
        InsertUnorderedList       = ...  # 0x2c
        ChangeTextDirectionLTR    = ...  # 0x2d
        ChangeTextDirectionRTL    = ...  # 0x2e
        WebActionCount            = ...  # 0x2f

    class WebWindowType(enum.Enum):

        WebBrowserWindow          = ...  # 0x0
        WebBrowserTab             = ...  # 0x1
        WebDialog                 = ...  # 0x2
        WebBrowserBackgroundTab   = ...  # 0x3


    @typing.overload
    def __init__(self, profile: PySide6.QtWebEngineCore.QWebEngineProfile, /, parent: PySide6.QtCore.QObject | None = ..., *, selectedText: str | None = ..., hasSelection: bool | None = ..., requestedUrl: PySide6.QtCore.QUrl | None = ..., zoomFactor: float | None = ..., title: str | None = ..., url: PySide6.QtCore.QUrl | None = ..., iconUrl: PySide6.QtCore.QUrl | None = ..., icon: PySide6.QtGui.QIcon | None = ..., backgroundColor: PySide6.QtGui.QColor | None = ..., contentsSize: PySide6.QtCore.QSizeF | None = ..., scrollPosition: PySide6.QtCore.QPointF | None = ..., audioMuted: bool | None = ..., recentlyAudible: bool | None = ..., visible: bool | None = ..., lifecycleState: PySide6.QtWebEngineCore.QWebEnginePage.LifecycleState | None = ..., recommendedState: PySide6.QtWebEngineCore.QWebEnginePage.LifecycleState | None = ..., renderProcessPid: int | None = ..., loading: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, selectedText: str | None = ..., hasSelection: bool | None = ..., requestedUrl: PySide6.QtCore.QUrl | None = ..., zoomFactor: float | None = ..., title: str | None = ..., url: PySide6.QtCore.QUrl | None = ..., iconUrl: PySide6.QtCore.QUrl | None = ..., icon: PySide6.QtGui.QIcon | None = ..., backgroundColor: PySide6.QtGui.QColor | None = ..., contentsSize: PySide6.QtCore.QSizeF | None = ..., scrollPosition: PySide6.QtCore.QPointF | None = ..., audioMuted: bool | None = ..., recentlyAudible: bool | None = ..., visible: bool | None = ..., lifecycleState: PySide6.QtWebEngineCore.QWebEnginePage.LifecycleState | None = ..., recommendedState: PySide6.QtWebEngineCore.QWebEnginePage.LifecycleState | None = ..., renderProcessPid: int | None = ..., loading: bool | None = ...) -> None: ...

    def acceptAsNewWindow(self, request: PySide6.QtWebEngineCore.QWebEngineNewWindowRequest, /) -> None: ...
    def acceptNavigationRequest(self, url: PySide6.QtCore.QUrl | str, type: PySide6.QtWebEngineCore.QWebEnginePage.NavigationType, isMainFrame: bool, /) -> bool: ...
    def action(self, action: PySide6.QtWebEngineCore.QWebEnginePage.WebAction, /) -> PySide6.QtGui.QAction: ...
    def backgroundColor(self, /) -> PySide6.QtGui.QColor: ...
    def chooseFiles(self, mode: PySide6.QtWebEngineCore.QWebEnginePage.FileSelectionMode, oldFiles: collections.abc.Sequence[str], acceptedMimeTypes: collections.abc.Sequence[str], /) -> typing.List[str]: ...
    def contentsSize(self, /) -> PySide6.QtCore.QSizeF: ...
    def createWindow(self, type: PySide6.QtWebEngineCore.QWebEnginePage.WebWindowType, /) -> PySide6.QtWebEngineCore.QWebEnginePage: ...
    def devToolsId(self, /) -> str: ...
    def devToolsPage(self, /) -> PySide6.QtWebEngineCore.QWebEnginePage: ...
    def download(self, url: PySide6.QtCore.QUrl | str, /, filename: str = ...) -> None: ...
    def event(self, arg__1: PySide6.QtCore.QEvent, /) -> bool: ...
    def findFrameByName(self, name: str, /) -> PySide6.QtWebEngineCore.QWebEngineFrame: ...
    @typing.overload
    def findText(self, subString: str, /, options: PySide6.QtWebEngineCore.QWebEnginePage.FindFlag = ...) -> None: ...
    @typing.overload
    def findText(self, subString: str, options: PySide6.QtWebEngineCore.QWebEnginePage.FindFlag, resultCallback: collections.abc.Callable[..., typing.Any], /) -> None: ...
    def hasSelection(self, /) -> bool: ...
    def history(self, /) -> PySide6.QtWebEngineCore.QWebEngineHistory: ...
    def icon(self, /) -> PySide6.QtGui.QIcon: ...
    def iconUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def inspectedPage(self, /) -> PySide6.QtWebEngineCore.QWebEnginePage: ...
    def isAudioMuted(self, /) -> bool: ...
    def isLoading(self, /) -> bool: ...
    def isVisible(self, /) -> bool: ...
    def javaScriptAlert(self, securityOrigin: PySide6.QtCore.QUrl | str, msg: str, /) -> None: ...
    def javaScriptConfirm(self, securityOrigin: PySide6.QtCore.QUrl | str, msg: str, /) -> bool: ...
    def javaScriptConsoleMessage(self, level: PySide6.QtWebEngineCore.QWebEnginePage.JavaScriptConsoleMessageLevel, message: str, lineNumber: int, sourceID: str, /) -> None: ...
    def javaScriptPrompt(self, securityOrigin: PySide6.QtCore.QUrl | str, msg: str, defaultValue: str, /) -> typing.Tuple[bool, str]: ...
    def lifecycleState(self, /) -> PySide6.QtWebEngineCore.QWebEnginePage.LifecycleState: ...
    @typing.overload
    def load(self, request: PySide6.QtWebEngineCore.QWebEngineHttpRequest, /) -> None: ...
    @typing.overload
    def load(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    def mainFrame(self, /) -> PySide6.QtWebEngineCore.QWebEngineFrame: ...
    @typing.overload
    def printToPdf(self, filePath: str, /, layout: PySide6.QtGui.QPageLayout = ..., ranges: PySide6.QtGui.QPageRanges = ...) -> None: ...
    @typing.overload
    def printToPdf(self, resultCallback: collections.abc.Callable[..., typing.Any], /, pageLayout: PySide6.QtGui.QPageLayout = ..., ranges: PySide6.QtGui.QPageRanges = ...) -> None: ...
    def profile(self, /) -> PySide6.QtWebEngineCore.QWebEngineProfile: ...
    def recentlyAudible(self, /) -> bool: ...
    def recommendedState(self, /) -> PySide6.QtWebEngineCore.QWebEnginePage.LifecycleState: ...
    def renderProcessPid(self, /) -> int: ...
    def replaceMisspelledWord(self, replacement: str, /) -> None: ...
    def requestedUrl(self, /) -> PySide6.QtCore.QUrl: ...
    @typing.overload
    def runJavaScript(self, scriptSource: str, resultCallback: collections.abc.Callable[..., typing.Any], /) -> None: ...
    @typing.overload
    def runJavaScript(self, scriptSource: str, /, worldId: int | None = ..., resultCallback: collections.abc.Callable[..., typing.Any] = ...) -> None: ...
    def save(self, filePath: str, /, format: PySide6.QtWebEngineCore.QWebEngineDownloadRequest.SavePageFormat = ...) -> None: ...
    def scripts(self, /) -> PySide6.QtWebEngineCore.QWebEngineScriptCollection: ...
    def scrollPosition(self, /) -> PySide6.QtCore.QPointF: ...
    def selectedText(self, /) -> str: ...
    def setAudioMuted(self, muted: bool, /) -> None: ...
    def setBackgroundColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setContent(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /, mimeType: str = ..., baseUrl: PySide6.QtCore.QUrl | str = ...) -> None: ...
    def setDevToolsPage(self, page: PySide6.QtWebEngineCore.QWebEnginePage, /) -> None: ...
    def setFeaturePermission(self, securityOrigin: PySide6.QtCore.QUrl | str, feature: PySide6.QtWebEngineCore.QWebEnginePage.Feature, policy: PySide6.QtWebEngineCore.QWebEnginePage.PermissionPolicy, /) -> None: ...
    def setHtml(self, html: str, /, baseUrl: PySide6.QtCore.QUrl | str = ...) -> None: ...
    def setInspectedPage(self, page: PySide6.QtWebEngineCore.QWebEnginePage, /) -> None: ...
    def setLifecycleState(self, state: PySide6.QtWebEngineCore.QWebEnginePage.LifecycleState, /) -> None: ...
    def setUrl(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    def setUrlRequestInterceptor(self, interceptor: PySide6.QtWebEngineCore.QWebEngineUrlRequestInterceptor, /) -> None: ...
    def setVisible(self, visible: bool, /) -> None: ...
    def setWebChannel(self, arg__1: PySide6.QtWebChannel.QWebChannel, /, worldId: int | None = ...) -> None: ...
    def setZoomFactor(self, factor: float, /) -> None: ...
    def settings(self, /) -> PySide6.QtWebEngineCore.QWebEngineSettings: ...
    def title(self, /) -> str: ...
    def toHtml(self, resultCallback: collections.abc.Callable[..., typing.Any], /) -> None: ...
    def toPlainText(self, resultCallback: collections.abc.Callable[..., typing.Any], /) -> None: ...
    def triggerAction(self, action: PySide6.QtWebEngineCore.QWebEnginePage.WebAction, /, checked: bool = ...) -> None: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...
    def webChannel(self, /) -> PySide6.QtWebChannel.QWebChannel: ...
    def zoomFactor(self, /) -> float: ...


class QWebEnginePermission(Shiboken.Object):

    class PermissionType(enum.Enum):

        Unsupported               = ...  # 0x0
        MediaAudioCapture         = ...  # 0x1
        MediaVideoCapture         = ...  # 0x2
        MediaAudioVideoCapture    = ...  # 0x3
        DesktopVideoCapture       = ...  # 0x4
        DesktopAudioVideoCapture  = ...  # 0x5
        MouseLock                 = ...  # 0x6
        Notifications             = ...  # 0x7
        Geolocation               = ...  # 0x8
        ClipboardReadWrite        = ...  # 0x9
        LocalFontsAccess          = ...  # 0xa

    class State(enum.Enum):

        Invalid                   = ...  # 0x0
        Ask                       = ...  # 0x1
        Granted                   = ...  # 0x2
        Denied                    = ...  # 0x3


    @typing.overload
    def __init__(self, other: PySide6.QtWebEngineCore.QWebEnginePermission, /, *, origin: PySide6.QtCore.QUrl | None = ..., permissionType: PySide6.QtWebEngineCore.QWebEnginePermission.PermissionType | None = ..., state: PySide6.QtWebEngineCore.QWebEnginePermission.State | None = ..., isValid: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, origin: PySide6.QtCore.QUrl | None = ..., permissionType: PySide6.QtWebEngineCore.QWebEnginePermission.PermissionType | None = ..., state: PySide6.QtWebEngineCore.QWebEnginePermission.State | None = ..., isValid: bool | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtWebEngineCore.QWebEnginePermission, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtWebEngineCore.QWebEnginePermission, /) -> bool: ...
    def deny(self, /) -> None: ...
    def grant(self, /) -> None: ...
    @staticmethod
    def isPersistent(permissionType: PySide6.QtWebEngineCore.QWebEnginePermission.PermissionType, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def origin(self, /) -> PySide6.QtCore.QUrl: ...
    def permissionType(self, /) -> PySide6.QtWebEngineCore.QWebEnginePermission.PermissionType: ...
    def reset(self, /) -> None: ...
    def state(self, /) -> PySide6.QtWebEngineCore.QWebEnginePermission.State: ...
    def swap(self, other: PySide6.QtWebEngineCore.QWebEnginePermission, /) -> None: ...


class QWebEngineProfile(PySide6.QtCore.QObject):

    clearHttpCacheCompleted  : typing.ClassVar[Signal] = ... # clearHttpCacheCompleted()
    downloadRequested        : typing.ClassVar[Signal] = ... # downloadRequested(QWebEngineDownloadRequest*)

    class HttpCacheType(enum.Enum):

        MemoryHttpCache           = ...  # 0x0
        DiskHttpCache             = ...  # 0x1
        NoCache                   = ...  # 0x2

    class PersistentCookiesPolicy(enum.Enum):

        NoPersistentCookies       = ...  # 0x0
        AllowPersistentCookies    = ...  # 0x1
        ForcePersistentCookies    = ...  # 0x2

    class PersistentPermissionsPolicy(enum.Enum):

        AskEveryTime              = ...  # 0x0
        StoreInMemory             = ...  # 0x1
        StoreOnDisk               = ...  # 0x2


    @typing.overload
    def __init__(self, name: str, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def cachePath(self, /) -> str: ...
    def clearAllVisitedLinks(self, /) -> None: ...
    def clearHttpCache(self, /) -> None: ...
    def clearVisitedLinks(self, urls: collections.abc.Sequence[PySide6.QtCore.QUrl], /) -> None: ...
    def clientCertificateStore(self, /) -> PySide6.QtWebEngineCore.QWebEngineClientCertificateStore: ...
    def clientHints(self, /) -> PySide6.QtWebEngineCore.QWebEngineClientHints: ...
    def cookieStore(self, /) -> PySide6.QtWebEngineCore.QWebEngineCookieStore: ...
    @staticmethod
    def defaultProfile() -> PySide6.QtWebEngineCore.QWebEngineProfile: ...
    def downloadPath(self, /) -> str: ...
    def httpAcceptLanguage(self, /) -> str: ...
    def httpCacheMaximumSize(self, /) -> int: ...
    def httpCacheType(self, /) -> PySide6.QtWebEngineCore.QWebEngineProfile.HttpCacheType: ...
    def httpUserAgent(self, /) -> str: ...
    def installUrlSchemeHandler(self, scheme: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, arg__2: PySide6.QtWebEngineCore.QWebEngineUrlSchemeHandler, /) -> None: ...
    def isOffTheRecord(self, /) -> bool: ...
    def isPushServiceEnabled(self, /) -> bool: ...
    def isSpellCheckEnabled(self, /) -> bool: ...
    def listAllPermissions(self, /) -> typing.List[PySide6.QtWebEngineCore.QWebEnginePermission]: ...
    def listPermissionsForOrigin(self, securityOrigin: PySide6.QtCore.QUrl | str, /) -> typing.List[PySide6.QtWebEngineCore.QWebEnginePermission]: ...
    def listPermissionsForPermissionType(self, permissionType: PySide6.QtWebEngineCore.QWebEnginePermission.PermissionType, /) -> typing.List[PySide6.QtWebEngineCore.QWebEnginePermission]: ...
    def persistentCookiesPolicy(self, /) -> PySide6.QtWebEngineCore.QWebEngineProfile.PersistentCookiesPolicy: ...
    def persistentPermissionsPolicy(self, /) -> PySide6.QtWebEngineCore.QWebEngineProfile.PersistentPermissionsPolicy: ...
    def persistentStoragePath(self, /) -> str: ...
    def queryPermission(self, securityOrigin: PySide6.QtCore.QUrl | str, permissionType: PySide6.QtWebEngineCore.QWebEnginePermission.PermissionType, /) -> PySide6.QtWebEngineCore.QWebEnginePermission: ...
    def removeAllUrlSchemeHandlers(self, /) -> None: ...
    def removeUrlScheme(self, scheme: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def removeUrlSchemeHandler(self, arg__1: PySide6.QtWebEngineCore.QWebEngineUrlSchemeHandler, /) -> None: ...
    def scripts(self, /) -> PySide6.QtWebEngineCore.QWebEngineScriptCollection: ...
    def setCachePath(self, path: str, /) -> None: ...
    def setDownloadPath(self, path: str, /) -> None: ...
    def setHttpAcceptLanguage(self, httpAcceptLanguage: str, /) -> None: ...
    def setHttpCacheMaximumSize(self, maxSize: int, /) -> None: ...
    def setHttpCacheType(self, arg__1: PySide6.QtWebEngineCore.QWebEngineProfile.HttpCacheType, /) -> None: ...
    def setHttpUserAgent(self, userAgent: str, /) -> None: ...
    def setNotificationPresenter(self, notificationPresenter: collections.abc.Callable[..., typing.Any], /) -> None: ...
    def setPersistentCookiesPolicy(self, arg__1: PySide6.QtWebEngineCore.QWebEngineProfile.PersistentCookiesPolicy, /) -> None: ...
    def setPersistentPermissionsPolicy(self, arg__1: PySide6.QtWebEngineCore.QWebEngineProfile.PersistentPermissionsPolicy, /) -> None: ...
    def setPersistentStoragePath(self, path: str, /) -> None: ...
    def setPushServiceEnabled(self, enabled: bool, /) -> None: ...
    def setSpellCheckEnabled(self, enabled: bool, /) -> None: ...
    def setSpellCheckLanguages(self, languages: collections.abc.Sequence[str], /) -> None: ...
    def setUrlRequestInterceptor(self, interceptor: PySide6.QtWebEngineCore.QWebEngineUrlRequestInterceptor, /) -> None: ...
    def settings(self, /) -> PySide6.QtWebEngineCore.QWebEngineSettings: ...
    def spellCheckLanguages(self, /) -> typing.List[str]: ...
    def storageName(self, /) -> str: ...
    def urlSchemeHandler(self, arg__1: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtWebEngineCore.QWebEngineUrlSchemeHandler: ...
    def visitedLinksContainsUrl(self, url: PySide6.QtCore.QUrl | str, /) -> bool: ...


class QWebEngineProfileBuilder(Shiboken.Object):

    def __init__(self, /) -> None: ...

    @staticmethod
    def createOffTheRecordProfile(parent: PySide6.QtCore.QObject | None = ...) -> PySide6.QtWebEngineCore.QWebEngineProfile: ...
    def createProfile(self, storageName: str, /, parent: PySide6.QtCore.QObject | None = ...) -> PySide6.QtWebEngineCore.QWebEngineProfile: ...
    def setCachePath(self, path: str, /) -> PySide6.QtWebEngineCore.QWebEngineProfileBuilder: ...
    def setHttpCacheMaximumSize(self, maxSizeInBytes: int, /) -> PySide6.QtWebEngineCore.QWebEngineProfileBuilder: ...
    def setHttpCacheType(self, httpCacheType: PySide6.QtWebEngineCore.QWebEngineProfile.HttpCacheType, /) -> PySide6.QtWebEngineCore.QWebEngineProfileBuilder: ...
    def setPersistentCookiesPolicy(self, persistentCookiesPolicy: PySide6.QtWebEngineCore.QWebEngineProfile.PersistentCookiesPolicy, /) -> PySide6.QtWebEngineCore.QWebEngineProfileBuilder: ...
    def setPersistentPermissionsPolicy(self, persistentPermissionPolicy: PySide6.QtWebEngineCore.QWebEngineProfile.PersistentPermissionsPolicy, /) -> PySide6.QtWebEngineCore.QWebEngineProfileBuilder: ...
    def setPersistentStoragePath(self, path: str, /) -> PySide6.QtWebEngineCore.QWebEngineProfileBuilder: ...


class QWebEngineQuotaRequest(Shiboken.Object):

    def __init__(self, /, *, origin: PySide6.QtCore.QUrl | None = ..., requestedSize: int | None = ...) -> None: ...

    def __eq__(self, arg__1: PySide6.QtWebEngineCore.QWebEngineQuotaRequest, /) -> bool: ...
    def __ne__(self, arg__1: PySide6.QtWebEngineCore.QWebEngineQuotaRequest, /) -> bool: ...
    def accept(self, /) -> None: ...
    def origin(self, /) -> PySide6.QtCore.QUrl: ...
    def reject(self, /) -> None: ...
    def requestedSize(self, /) -> int: ...


class QWebEngineRegisterProtocolHandlerRequest(Shiboken.Object):

    @typing.overload
    def __init__(self, QWebEngineRegisterProtocolHandlerRequest: PySide6.QtWebEngineCore.QWebEngineRegisterProtocolHandlerRequest, /, *, origin: PySide6.QtCore.QUrl | None = ..., scheme: str | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, origin: PySide6.QtCore.QUrl | None = ..., scheme: str | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, that: PySide6.QtWebEngineCore.QWebEngineRegisterProtocolHandlerRequest, /) -> bool: ...
    def __ne__(self, that: PySide6.QtWebEngineCore.QWebEngineRegisterProtocolHandlerRequest, /) -> bool: ...
    def accept(self, /) -> None: ...
    def origin(self, /) -> PySide6.QtCore.QUrl: ...
    def reject(self, /) -> None: ...
    def scheme(self, /) -> str: ...


class QWebEngineScript(Shiboken.Object):

    class InjectionPoint(enum.Enum):

        Deferred                  = ...  # 0x0
        DocumentReady             = ...  # 0x1
        DocumentCreation          = ...  # 0x2

    class ScriptWorldId(enum.IntEnum):

        MainWorld                 = ...  # 0x0
        ApplicationWorld          = ...  # 0x1
        UserWorld                 = ...  # 0x2


    @typing.overload
    def __init__(self, other: PySide6.QtWebEngineCore.QWebEngineScript, /, *, name: str | None = ..., sourceUrl: PySide6.QtCore.QUrl | None = ..., sourceCode: str | None = ..., injectionPoint: PySide6.QtWebEngineCore.QWebEngineScript.InjectionPoint | None = ..., worldId: int | None = ..., runsOnSubFrames: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, name: str | None = ..., sourceUrl: PySide6.QtCore.QUrl | None = ..., sourceCode: str | None = ..., injectionPoint: PySide6.QtWebEngineCore.QWebEngineScript.InjectionPoint | None = ..., worldId: int | None = ..., runsOnSubFrames: bool | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtWebEngineCore.QWebEngineScript, /) -> bool: ...
    def __ne__(self, other: PySide6.QtWebEngineCore.QWebEngineScript, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def injectionPoint(self, /) -> PySide6.QtWebEngineCore.QWebEngineScript.InjectionPoint: ...
    def name(self, /) -> str: ...
    def runsOnSubFrames(self, /) -> bool: ...
    def setInjectionPoint(self, arg__1: PySide6.QtWebEngineCore.QWebEngineScript.InjectionPoint, /) -> None: ...
    def setName(self, arg__1: str, /) -> None: ...
    def setRunsOnSubFrames(self, on: bool, /) -> None: ...
    def setSourceCode(self, arg__1: str, /) -> None: ...
    def setSourceUrl(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    def setWorldId(self, arg__1: int, /) -> None: ...
    def sourceCode(self, /) -> str: ...
    def sourceUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def swap(self, other: PySide6.QtWebEngineCore.QWebEngineScript, /) -> None: ...
    def worldId(self, /) -> int: ...


class QWebEngineScriptCollection(Shiboken.Object):
    def clear(self, /) -> None: ...
    def contains(self, value: PySide6.QtWebEngineCore.QWebEngineScript, /) -> bool: ...
    def count(self, /) -> int: ...
    def find(self, name: str, /) -> typing.List[PySide6.QtWebEngineCore.QWebEngineScript]: ...
    @typing.overload
    def insert(self, arg__1: PySide6.QtWebEngineCore.QWebEngineScript, /) -> None: ...
    @typing.overload
    def insert(self, list: collections.abc.Sequence[PySide6.QtWebEngineCore.QWebEngineScript], /) -> None: ...
    def isEmpty(self, /) -> bool: ...
    def remove(self, arg__1: PySide6.QtWebEngineCore.QWebEngineScript, /) -> bool: ...
    def toList(self, /) -> typing.List[PySide6.QtWebEngineCore.QWebEngineScript]: ...


class QWebEngineSettings(Shiboken.Object):

    class FontFamily(enum.Enum):

        StandardFont              = ...  # 0x0
        FixedFont                 = ...  # 0x1
        SerifFont                 = ...  # 0x2
        SansSerifFont             = ...  # 0x3
        CursiveFont               = ...  # 0x4
        FantasyFont               = ...  # 0x5
        PictographFont            = ...  # 0x6

    class FontSize(enum.Enum):

        MinimumFontSize           = ...  # 0x0
        MinimumLogicalFontSize    = ...  # 0x1
        DefaultFontSize           = ...  # 0x2
        DefaultFixedFontSize      = ...  # 0x3

    class ImageAnimationPolicy(enum.Enum):

        Inherited                 = ...  # 0x0
        Allow                     = ...  # 0x1
        AnimateOnce               = ...  # 0x2
        Disallow                  = ...  # 0x3

    class UnknownUrlSchemePolicy(enum.Enum):

        InheritedUnknownUrlSchemePolicy = ...  # 0x0
        DisallowUnknownUrlSchemes = ...  # 0x1
        AllowUnknownUrlSchemesFromUserInteraction = ...  # 0x2
        AllowAllUnknownUrlSchemes = ...  # 0x3

    class WebAttribute(enum.Enum):

        AutoLoadImages            = ...  # 0x0
        JavascriptEnabled         = ...  # 0x1
        JavascriptCanOpenWindows  = ...  # 0x2
        JavascriptCanAccessClipboard = ...  # 0x3
        LinksIncludedInFocusChain = ...  # 0x4
        LocalStorageEnabled       = ...  # 0x5
        LocalContentCanAccessRemoteUrls = ...  # 0x6
        XSSAuditingEnabled        = ...  # 0x7
        SpatialNavigationEnabled  = ...  # 0x8
        LocalContentCanAccessFileUrls = ...  # 0x9
        HyperlinkAuditingEnabled  = ...  # 0xa
        ScrollAnimatorEnabled     = ...  # 0xb
        ErrorPageEnabled          = ...  # 0xc
        PluginsEnabled            = ...  # 0xd
        FullScreenSupportEnabled  = ...  # 0xe
        ScreenCaptureEnabled      = ...  # 0xf
        WebGLEnabled              = ...  # 0x10
        Accelerated2dCanvasEnabled = ...  # 0x11
        AutoLoadIconsForPage      = ...  # 0x12
        TouchIconsEnabled         = ...  # 0x13
        FocusOnNavigationEnabled  = ...  # 0x14
        PrintElementBackgrounds   = ...  # 0x15
        AllowRunningInsecureContent = ...  # 0x16
        AllowGeolocationOnInsecureOrigins = ...  # 0x17
        AllowWindowActivationFromJavaScript = ...  # 0x18
        ShowScrollBars            = ...  # 0x19
        PlaybackRequiresUserGesture = ...  # 0x1a
        WebRTCPublicInterfacesOnly = ...  # 0x1b
        JavascriptCanPaste        = ...  # 0x1c
        DnsPrefetchEnabled        = ...  # 0x1d
        PdfViewerEnabled          = ...  # 0x1e
        NavigateOnDropEnabled     = ...  # 0x1f
        ReadingFromCanvasEnabled  = ...  # 0x20
        ForceDarkMode             = ...  # 0x21
        PrintHeaderAndFooter      = ...  # 0x22
        PreferCSSMarginsForPrinting = ...  # 0x23
        TouchEventsApiEnabled     = ...  # 0x24


    def defaultTextEncoding(self, /) -> str: ...
    def fontFamily(self, which: PySide6.QtWebEngineCore.QWebEngineSettings.FontFamily, /) -> str: ...
    def fontSize(self, type: PySide6.QtWebEngineCore.QWebEngineSettings.FontSize, /) -> int: ...
    def imageAnimationPolicy(self, /) -> PySide6.QtWebEngineCore.QWebEngineSettings.ImageAnimationPolicy: ...
    def resetAttribute(self, attr: PySide6.QtWebEngineCore.QWebEngineSettings.WebAttribute, /) -> None: ...
    def resetFontFamily(self, which: PySide6.QtWebEngineCore.QWebEngineSettings.FontFamily, /) -> None: ...
    def resetFontSize(self, type: PySide6.QtWebEngineCore.QWebEngineSettings.FontSize, /) -> None: ...
    def resetImageAnimationPolicy(self, /) -> None: ...
    def resetUnknownUrlSchemePolicy(self, /) -> None: ...
    def setAttribute(self, attr: PySide6.QtWebEngineCore.QWebEngineSettings.WebAttribute, on: bool, /) -> None: ...
    def setDefaultTextEncoding(self, encoding: str, /) -> None: ...
    def setFontFamily(self, which: PySide6.QtWebEngineCore.QWebEngineSettings.FontFamily, family: str, /) -> None: ...
    def setFontSize(self, type: PySide6.QtWebEngineCore.QWebEngineSettings.FontSize, size: int, /) -> None: ...
    def setImageAnimationPolicy(self, policy: PySide6.QtWebEngineCore.QWebEngineSettings.ImageAnimationPolicy, /) -> None: ...
    def setUnknownUrlSchemePolicy(self, policy: PySide6.QtWebEngineCore.QWebEngineSettings.UnknownUrlSchemePolicy, /) -> None: ...
    def testAttribute(self, attr: PySide6.QtWebEngineCore.QWebEngineSettings.WebAttribute, /) -> bool: ...
    def unknownUrlSchemePolicy(self, /) -> PySide6.QtWebEngineCore.QWebEngineSettings.UnknownUrlSchemePolicy: ...


class QWebEngineUrlRequestInfo(Shiboken.Object):

    class NavigationType(enum.Enum):

        NavigationTypeLink        = ...  # 0x0
        NavigationTypeTyped       = ...  # 0x1
        NavigationTypeFormSubmitted = ...  # 0x2
        NavigationTypeBackForward = ...  # 0x3
        NavigationTypeReload      = ...  # 0x4
        NavigationTypeOther       = ...  # 0x5
        NavigationTypeRedirect    = ...  # 0x6

    class ResourceType(enum.Enum):

        ResourceTypeMainFrame     = ...  # 0x0
        ResourceTypeSubFrame      = ...  # 0x1
        ResourceTypeStylesheet    = ...  # 0x2
        ResourceTypeScript        = ...  # 0x3
        ResourceTypeImage         = ...  # 0x4
        ResourceTypeFontResource  = ...  # 0x5
        ResourceTypeSubResource   = ...  # 0x6
        ResourceTypeObject        = ...  # 0x7
        ResourceTypeMedia         = ...  # 0x8
        ResourceTypeWorker        = ...  # 0x9
        ResourceTypeSharedWorker  = ...  # 0xa
        ResourceTypePrefetch      = ...  # 0xb
        ResourceTypeFavicon       = ...  # 0xc
        ResourceTypeXhr           = ...  # 0xd
        ResourceTypePing          = ...  # 0xe
        ResourceTypeServiceWorker = ...  # 0xf
        ResourceTypeCspReport     = ...  # 0x10
        ResourceTypePluginResource = ...  # 0x11
        ResourceTypeNavigationPreloadMainFrame = ...  # 0x13
        ResourceTypeNavigationPreloadSubFrame = ...  # 0x14
        ResourceTypeJson          = ...  # 0x15
        ResourceTypeLast          = ...  # 0x15
        ResourceTypeWebSocket     = ...  # 0xfe
        ResourceTypeUnknown       = ...  # 0xff


    def block(self, shouldBlock: bool, /) -> None: ...
    def changed(self, /) -> bool: ...
    def firstPartyUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def httpHeaders(self, /) -> typing.Dict[PySide6.QtCore.QByteArray, PySide6.QtCore.QByteArray]: ...
    def initiator(self, /) -> PySide6.QtCore.QUrl: ...
    def isDownload(self, /) -> bool: ...
    def navigationType(self, /) -> PySide6.QtWebEngineCore.QWebEngineUrlRequestInfo.NavigationType: ...
    def redirect(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    def requestBody(self, /) -> PySide6.QtCore.QIODevice: ...
    def requestMethod(self, /) -> PySide6.QtCore.QByteArray: ...
    def requestUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def resourceType(self, /) -> PySide6.QtWebEngineCore.QWebEngineUrlRequestInfo.ResourceType: ...
    def setHttpHeader(self, name: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, value: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...


class QWebEngineUrlRequestInterceptor(PySide6.QtCore.QObject):

    def __init__(self, /, p: PySide6.QtCore.QObject | None = ...) -> None: ...

    def interceptRequest(self, info: PySide6.QtWebEngineCore.QWebEngineUrlRequestInfo, /) -> None: ...


class QWebEngineUrlRequestJob(PySide6.QtCore.QObject):

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        UrlNotFound               = ...  # 0x1
        UrlInvalid                = ...  # 0x2
        RequestAborted            = ...  # 0x3
        RequestDenied             = ...  # 0x4
        RequestFailed             = ...  # 0x5


    def fail(self, error: PySide6.QtWebEngineCore.QWebEngineUrlRequestJob.Error, /) -> None: ...
    def initiator(self, /) -> PySide6.QtCore.QUrl: ...
    def redirect(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    def reply(self, contentType: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, device: PySide6.QtCore.QIODevice, /) -> None: ...
    def requestBody(self, /) -> PySide6.QtCore.QIODevice: ...
    def requestHeaders(self, /) -> typing.Dict[PySide6.QtCore.QByteArray, PySide6.QtCore.QByteArray]: ...
    def requestMethod(self, /) -> PySide6.QtCore.QByteArray: ...
    def requestUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def setAdditionalResponseHeaders(self, additionalResponseHeaders: typing.Dict[PySide6.QtCore.QByteArray, PySide6.QtCore.QByteArray], /) -> None: ...


class QWebEngineUrlScheme(Shiboken.Object):

    class Flag(enum.Flag):

        SecureScheme              = ...  # 0x1
        LocalScheme               = ...  # 0x2
        LocalAccessAllowed        = ...  # 0x4
        NoAccessAllowed           = ...  # 0x8
        ServiceWorkersAllowed     = ...  # 0x10
        ViewSourceAllowed         = ...  # 0x20
        ContentSecurityPolicyIgnored = ...  # 0x40
        CorsEnabled               = ...  # 0x80
        FetchApiAllowed           = ...  # 0x100

    class SpecialPort(enum.Enum):

        PortUnspecified           = ...  # -1

    class Syntax(enum.Enum):

        HostPortAndUserInformation = ...  # 0x0
        HostAndPort               = ...  # 0x1
        Host                      = ...  # 0x2
        Path                      = ...  # 0x3


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, that: PySide6.QtWebEngineCore.QWebEngineUrlScheme, /) -> None: ...
    @typing.overload
    def __init__(self, name: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, that: PySide6.QtWebEngineCore.QWebEngineUrlScheme, /) -> bool: ...
    def __ne__(self, that: PySide6.QtWebEngineCore.QWebEngineUrlScheme, /) -> bool: ...
    def defaultPort(self, /) -> int: ...
    def flags(self, /) -> PySide6.QtWebEngineCore.QWebEngineUrlScheme.Flag: ...
    def name(self, /) -> PySide6.QtCore.QByteArray: ...
    @staticmethod
    def registerScheme(scheme: PySide6.QtWebEngineCore.QWebEngineUrlScheme, /) -> None: ...
    @staticmethod
    def schemeByName(name: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtWebEngineCore.QWebEngineUrlScheme: ...
    def setDefaultPort(self, newValue: int, /) -> None: ...
    def setFlags(self, newValue: PySide6.QtWebEngineCore.QWebEngineUrlScheme.Flag, /) -> None: ...
    def setName(self, newValue: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setSyntax(self, newValue: PySide6.QtWebEngineCore.QWebEngineUrlScheme.Syntax, /) -> None: ...
    def syntax(self, /) -> PySide6.QtWebEngineCore.QWebEngineUrlScheme.Syntax: ...


class QWebEngineUrlSchemeHandler(PySide6.QtCore.QObject):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def requestStarted(self, arg__1: PySide6.QtWebEngineCore.QWebEngineUrlRequestJob, /) -> None: ...


class QWebEngineWebAuthPinRequest(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QWebEngineWebAuthPinRequest: PySide6.QtWebEngineCore.QWebEngineWebAuthPinRequest, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...


class QWebEngineWebAuthUxRequest(PySide6.QtCore.QObject):

    stateChanged             : typing.ClassVar[Signal] = ... # stateChanged(QWebEngineWebAuthUxRequest::WebAuthUxState)

    class PinEntryError(enum.Enum):

        NoError                   = ...  # 0x0
        InternalUvLocked          = ...  # 0x1
        WrongPin                  = ...  # 0x2
        TooShort                  = ...  # 0x3
        InvalidCharacters         = ...  # 0x4
        SameAsCurrentPin          = ...  # 0x5

    class PinEntryReason(enum.Enum):

        Set                       = ...  # 0x0
        Change                    = ...  # 0x1
        Challenge                 = ...  # 0x2

    class RequestFailureReason(enum.Enum):

        Timeout                   = ...  # 0x0
        KeyNotRegistered          = ...  # 0x1
        KeyAlreadyRegistered      = ...  # 0x2
        SoftPinBlock              = ...  # 0x3
        HardPinBlock              = ...  # 0x4
        AuthenticatorRemovedDuringPinEntry = ...  # 0x5
        AuthenticatorMissingResidentKeys = ...  # 0x6
        AuthenticatorMissingUserVerification = ...  # 0x7
        AuthenticatorMissingLargeBlob = ...  # 0x8
        NoCommonAlgorithms        = ...  # 0x9
        StorageFull               = ...  # 0xa
        UserConsentDenied         = ...  # 0xb
        WinUserCancelled          = ...  # 0xc

    class WebAuthUxState(enum.Enum):

        NotStarted                = ...  # 0x0
        SelectAccount             = ...  # 0x1
        CollectPin                = ...  # 0x2
        FinishTokenCollection     = ...  # 0x3
        RequestFailed             = ...  # 0x4
        Cancelled                 = ...  # 0x5
        Completed                 = ...  # 0x6


    def cancel(self, /) -> None: ...
    def pinRequest(self, /) -> PySide6.QtWebEngineCore.QWebEngineWebAuthPinRequest: ...
    def relyingPartyId(self, /) -> str: ...
    def requestFailureReason(self, /) -> PySide6.QtWebEngineCore.QWebEngineWebAuthUxRequest.RequestFailureReason: ...
    def retry(self, /) -> None: ...
    def setPin(self, pin: str, /) -> None: ...
    def setSelectedAccount(self, selectedAccount: str, /) -> None: ...
    def state(self, /) -> PySide6.QtWebEngineCore.QWebEngineWebAuthUxRequest.WebAuthUxState: ...
    def userNames(self, /) -> typing.List[str]: ...


def qWebEngineChromiumSecurityPatchVersion() -> bytes | bytearray | memoryview: ...
def qWebEngineChromiumVersion() -> bytes | bytearray | memoryview: ...
def qWebEngineVersion() -> bytes | bytearray | memoryview: ...


# eof
