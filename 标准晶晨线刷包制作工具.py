#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标准晶晨线刷包制作工具
完整实现USB Burning Tool兼容的线刷包格式
Copyright (c) 2025 By.举个🌰
"""

import sys
import os
import subprocess
import threading
import time
import json
import struct
import shutil
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

class AmlogicBootloaderParser:
    """晶晨Bootloader解析器"""
    
    def __init__(self, bootloader_file):
        self.bootloader_file = bootloader_file
        self.ddr_data = None
        self.uboot_data = None
        
    def parse_bootloader(self):
        """解析bootloader分区，提取DDR和U-Boot"""
        try:
            with open(self.bootloader_file, 'rb') as f:
                data = f.read()

            print(f"📁 Bootloader文件大小: {len(data)} bytes")

            # 分析现有的DDR.USB和UBOOT.USB文件来确定正确的提取方法
            ddr_reference_size = 0
            uboot_reference_size = 0

            if os.path.exists("DDR.USB"):
                ddr_reference_size = os.path.getsize("DDR.USB")
                print(f"📋 参考DDR.USB大小: {ddr_reference_size} bytes")

            if os.path.exists("UBOOT.USB"):
                uboot_reference_size = os.path.getsize("UBOOT.USB")
                print(f"📋 参考UBOOT.USB大小: {uboot_reference_size} bytes")

            # 查找AML标识 (DDR部分)
            aml_pos = data.find(b'AML')
            if aml_pos == -1:
                return False, "未找到DDR部分(AML标识)"

            print(f"🔍 找到DDR部分在位置: 0x{aml_pos:x}")

            # DDR提取策略
            if ddr_reference_size > 0:
                # 使用参考文件的大小
                ddr_start = 0
                ddr_size = ddr_reference_size
                print(f"📏 使用参考DDR大小: {ddr_size} bytes")
            else:
                # 默认策略：从开头到AMLC位置之前
                amlc_pos = data.find(b'AMLC')
                if amlc_pos != -1:
                    ddr_start = 0
                    ddr_size = amlc_pos
                else:
                    ddr_start = 0
                    ddr_size = 65536  # 默认64KB
                print(f"📏 计算DDR大小: {ddr_size} bytes")

            # 查找AMLC标识 (U-Boot部分)
            amlc_pos = data.find(b'AMLC')
            if amlc_pos != -1:
                print(f"🔍 找到U-Boot部分在位置: 0x{amlc_pos:x}")

                # U-Boot提取策略
                if uboot_reference_size > 0:
                    # 使用参考文件的大小
                    uboot_start = amlc_pos
                    uboot_size = uboot_reference_size
                    print(f"📏 使用参考U-Boot大小: {uboot_size} bytes")
                else:
                    # 默认策略：从AMLC到文件末尾，但限制最大大小
                    uboot_start = amlc_pos
                    uboot_size = len(data) - amlc_pos

                    # 限制最大大小为1MB
                    if uboot_size > 1024 * 1024:
                        uboot_size = 1024 * 1024
                    print(f"📏 计算U-Boot大小: {uboot_size} bytes")

                self.uboot_data = data[uboot_start:uboot_start + uboot_size]
                print(f"📦 U-Boot数据大小: {len(self.uboot_data)} bytes")
            else:
                return False, "未找到U-Boot部分(AMLC标识)"

            # 提取DDR数据
            self.ddr_data = data[ddr_start:ddr_start + ddr_size]
            print(f"📦 DDR数据大小: {len(self.ddr_data)} bytes")

            # 验证提取的数据
            if b'AML' in self.ddr_data:
                print("✅ DDR数据包含AML标识")
            else:
                print("⚠️  DDR数据未包含AML标识")

            if b'AMLC' in self.uboot_data:
                print("✅ U-Boot数据包含AMLC标识")
            else:
                print("⚠️  U-Boot数据未包含AMLC标识")

            return True, "Bootloader解析成功"

        except Exception as e:
            return False, f"Bootloader解析失败: {str(e)}"
    
    def save_ddr_usb(self, output_path):
        """保存DDR.USB文件"""
        if not self.ddr_data:
            return False, "DDR数据为空"
        
        try:
            with open(output_path, 'wb') as f:
                f.write(self.ddr_data)
            return True, f"DDR.USB保存成功: {len(self.ddr_data)} bytes"
        except Exception as e:
            return False, f"DDR.USB保存失败: {str(e)}"
    
    def save_uboot_usb(self, output_path):
        """保存UBOOT.USB文件"""
        if not self.uboot_data:
            return False, "U-Boot数据为空"
        
        try:
            with open(output_path, 'wb') as f:
                f.write(self.uboot_data)
            return True, f"UBOOT.USB保存成功: {len(self.uboot_data)} bytes"
        except Exception as e:
            return False, f"UBOOT.USB保存失败: {str(e)}"

class AmlogicFirmwareExtractor:
    """晶晨固件提取器"""
    
    def __init__(self):
        self.device_ip = "***************"
        self.device_port = "5555"
        self.connected = False
        self.device_info = {}
        
    def connect_device(self):
        """连接ADB设备"""
        try:
            # 先检查是否已经连接
            check_cmd = "adb devices"
            check_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=5)
            
            if check_result.returncode == 0 and f"{self.device_ip}:{self.device_port}" in check_result.stdout:
                if "device" in check_result.stdout:
                    self.connected = True
                    return True, "设备已连接"
            
            # 尝试连接
            cmd = f"adb connect {self.device_ip}:{self.device_port}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                output_lower = result.stdout.lower()
                if "connected" in output_lower or "already connected" in output_lower:
                    self.connected = True
                    return True, "设备连接成功"
                elif "cannot connect" in output_lower:
                    return False, "无法连接到设备，请检查IP地址和网络"
                else:
                    return False, f"连接状态不明确: {result.stdout}"
            else:
                return False, f"连接命令失败: {result.stderr}"
                
        except subprocess.TimeoutExpired:
            return False, "连接超时，请检查网络连接"
        except Exception as e:
            return False, f"连接异常: {str(e)}"
    
    def get_device_info(self):
        """获取设备信息"""
        if not self.connected:
            return {}
        
        try:
            info_commands = {
                'model': "adb shell getprop ro.product.model",
                'brand': "adb shell getprop ro.product.brand", 
                'device': "adb shell getprop ro.product.device",
                'platform': "adb shell getprop ro.board.platform",
                'hardware': "adb shell getprop ro.hardware",
                'android_version': "adb shell getprop ro.build.version.release",
                'build_id': "adb shell getprop ro.build.id",
                'build_date': "adb shell getprop ro.build.date",
                'chipset': "adb shell getprop ro.chipname"
            }
            
            info = {}
            for key, cmd in info_commands.items():
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                info[key] = result.stdout.strip() if result.returncode == 0 else "Unknown"
            
            self.device_info = info
            return info
        except Exception as e:
            return {"error": str(e)}
    
    def extract_partition(self, partition_name, output_file, progress_callback=None):
        """提取单个分区"""
        try:
            if progress_callback:
                progress_callback(f"正在提取分区: {partition_name}")
            
            # 尝试多个可能的分区路径
            possible_paths = [
                f"/dev/block/{partition_name}",
                f"/dev/{partition_name}",
                f"/dev/block/by-name/{partition_name}"
            ]
            
            partition_path = None
            for path in possible_paths:
                cmd = f'adb shell "test -e {path} && echo exists"'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=3)
                
                if result.returncode == 0 and 'exists' in result.stdout:
                    partition_path = path
                    break
            
            if not partition_path:
                return False, f"未找到分区: {partition_name}"
            
            # 提取分区数据
            cmd = f'adb pull {partition_path} "{output_file}"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                if file_size > 0:
                    return True, f"提取成功: {self._format_size(file_size)}"
                else:
                    return False, "提取的文件为空"
            else:
                return False, f"提取失败: {result.stderr}"
                
        except Exception as e:
            return False, f"提取异常: {str(e)}"
    
    def _format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        size = float(size_bytes)
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        if unit_index == 0:
            return f"{int(size)} {units[unit_index]}"
        else:
            return f"{size:.1f} {units[unit_index]}"

class AmlogicPackageBuilder:
    """晶晨线刷包构建器"""
    
    def __init__(self, output_dir, device_info):
        self.output_dir = output_dir
        self.device_info = device_info
        self.package_dir = os.path.join(output_dir, "USB_Burning_Tool")
        
    def create_package_structure(self):
        """创建线刷包目录结构"""
        try:
            # 创建主目录
            os.makedirs(self.package_dir, exist_ok=True)
            
            # 创建子目录
            subdirs = ['platform']
            for subdir in subdirs:
                os.makedirs(os.path.join(self.package_dir, subdir), exist_ok=True)
            
            return True, "目录结构创建成功"
        except Exception as e:
            return False, f"目录结构创建失败: {str(e)}"

    def create_platform_conf(self):
        """创建标准的platform.conf文件"""
        try:
            platform_conf_content = f"""[platform]
name={self.device_info.get('model', 'Unknown')}
brand={self.device_info.get('brand', 'Unknown')}
chip={self.device_info.get('hardware', 'amlogic')}
platform={self.device_info.get('platform', 'amlogic')}
variant=
board_version=

[configs]
DDR_FILE=DDR.USB
UBOOT_FILE=UBOOT.USB
PARTITION_FILE=image.cfg

[burning]
erase_bootloader=true
erase_flash=false
reboot=true
verify=true

[advanced]
ddr_timing_test=false
secure_boot=false
"""

            platform_conf_path = os.path.join(self.package_dir, "platform", "platform.conf")
            with open(platform_conf_path, 'w', encoding='utf-8') as f:
                f.write(platform_conf_content)

            return True, "platform.conf创建成功"
        except Exception as e:
            return False, f"platform.conf创建失败: {str(e)}"

    def create_image_cfg(self, partitions):
        """创建标准的image.cfg文件"""
        try:
            image_cfg_content = []

            # 添加头部信息
            image_cfg_content.append("[LIST_NORMAL]")

            # 添加DDR和U-Boot
            image_cfg_content.append('file="DDR.USB",main_type="USB",sub_type="DDR"')
            image_cfg_content.append('file="UBOOT.USB",main_type="USB",sub_type="UBOOT"')

            # 添加DTB
            image_cfg_content.append('file="meson1.dtb",main_type="dtb",sub_type="meson1"')

            # 添加分区映射
            partition_mapping = {
                'boot': ('PARTITION', 'boot'),
                'recovery': ('PARTITION', 'recovery'),
                'system': ('PARTITION', 'system'),
                'vendor': ('PARTITION', 'vendor'),
                'product': ('PARTITION', 'product'),
                'logo': ('PARTITION', 'logo'),
                'param': ('PARTITION', 'param'),
                'vbmeta': ('PARTITION', 'vbmeta'),
                'metadata': ('PARTITION', 'metadata'),
                'misc': ('PARTITION', 'misc'),
                'cache': ('PARTITION', 'cache'),
                'userdata': ('PARTITION', 'userdata')
            }

            for partition in partitions:
                partition_name = partition['name']
                if partition_name in partition_mapping:
                    main_type, sub_type = partition_mapping[partition_name]
                    image_cfg_content.append(f'file="{partition_name}.PARTITION",main_type="{main_type}",sub_type="{sub_type}"')

            # 添加验证列表
            image_cfg_content.append("")
            image_cfg_content.append("[LIST_VERIFY]")
            image_cfg_content.append("DDR")
            image_cfg_content.append("UBOOT")
            image_cfg_content.append("dtb")

            for partition in partitions:
                partition_name = partition['name']
                if partition_name in partition_mapping:
                    image_cfg_content.append(partition_name)

            # 写入文件
            image_cfg_path = os.path.join(self.package_dir, "image.cfg")
            with open(image_cfg_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(image_cfg_content))

            return True, "image.cfg创建成功"
        except Exception as e:
            return False, f"image.cfg创建失败: {str(e)}"

    def create_readme(self):
        """创建README文件"""
        try:
            readme_content = f"""# {self.device_info.get('model', 'Unknown')} 线刷包

## 设备信息
- 型号: {self.device_info.get('model', 'Unknown')}
- 品牌: {self.device_info.get('brand', 'Unknown')}
- 平台: {self.device_info.get('platform', 'Unknown')}
- 硬件: {self.device_info.get('hardware', 'Unknown')}
- Android版本: {self.device_info.get('android_version', 'Unknown')}
- 构建ID: {self.device_info.get('build_id', 'Unknown')}
- 构建日期: {self.device_info.get('build_date', 'Unknown')}

## 线刷包内容
- DDR.USB: DDR初始化文件
- UBOOT.USB: U-Boot引导文件
- meson1.dtb: 设备树文件
- *.PARTITION: 各分区镜像文件
- image.cfg: 镜像配置文件
- platform/platform.conf: 平台配置文件

## 使用方法
1. 使用晶晨USB Burning Tool
2. 选择此线刷包目录
3. 按照工具提示进行刷机

## 注意事项
- 刷机有风险，请确保设备型号匹配
- 刷机前请备份重要数据
- 刷机过程中请勿断电或拔线

## 制作信息
- 制作时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
- 制作工具: 标准晶晨线刷包制作工具 v2.0
- Copyright (c) 2025 By.举个🌰

## 技术规格
- 线刷包格式: USB Burning Tool标准格式
- DDR文件: 从bootloader分区提取
- U-Boot文件: 从bootloader分区提取
- 分区映射: 标准晶晨分区布局
"""

            readme_path = os.path.join(self.package_dir, "README.md")
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(readme_content)

            return True, "README.md创建成功"
        except Exception as e:
            return False, f"README.md创建失败: {str(e)}"

class FirmwareWorker(QThread):
    """固件制作工作线程"""
    progress_updated = Signal(int, str)
    finished = Signal(bool, str)

    def __init__(self, extractor, output_dir):
        super().__init__()
        self.extractor = extractor
        self.output_dir = output_dir
        self.running = True

    def run(self):
        """执行固件制作"""
        try:
            # 步骤1: 创建输出目录
            self.progress_updated.emit(5, "创建输出目录...")

            device_model = self.extractor.device_info.get('model', 'Unknown_Device').replace(' ', '_')
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            package_name = f"{device_model}_Firmware_{timestamp}"
            full_output_dir = os.path.join(self.output_dir, package_name)

            builder = AmlogicPackageBuilder(full_output_dir, self.extractor.device_info)

            success, msg = builder.create_package_structure()
            if not success:
                self.finished.emit(False, msg)
                return

            # 步骤2: 提取bootloader分区
            self.progress_updated.emit(10, "提取bootloader分区...")

            bootloader_file = os.path.join(full_output_dir, "bootloader.tmp")
            success, msg = self.extractor.extract_partition("bootloader", bootloader_file, self.progress_updated.emit)

            if not success:
                self.finished.emit(False, f"bootloader提取失败: {msg}")
                return

            # 步骤3: 解析bootloader并生成DDR.USB和UBOOT.USB
            self.progress_updated.emit(20, "解析bootloader，生成DDR.USB和UBOOT.USB...")

            parser = AmlogicBootloaderParser(bootloader_file)
            success, msg = parser.parse_bootloader()

            if not success:
                self.finished.emit(False, f"bootloader解析失败: {msg}")
                return

            # 保存DDR.USB
            ddr_path = os.path.join(builder.package_dir, "DDR.USB")
            success, msg = parser.save_ddr_usb(ddr_path)
            if success:
                self.progress_updated.emit(25, f"DDR.USB生成成功: {msg}")
            else:
                self.progress_updated.emit(25, f"DDR.USB生成警告: {msg}")

            # 保存UBOOT.USB
            uboot_path = os.path.join(builder.package_dir, "UBOOT.USB")
            success, msg = parser.save_uboot_usb(uboot_path)
            if success:
                self.progress_updated.emit(30, f"UBOOT.USB生成成功: {msg}")
            else:
                self.progress_updated.emit(30, f"UBOOT.USB生成警告: {msg}")

            # 清理临时文件
            if os.path.exists(bootloader_file):
                os.remove(bootloader_file)

            # 步骤4: 提取其他关键分区
            key_partitions = ['boot', 'recovery', 'system', 'vendor', 'product', 'logo', 'param', 'vbmeta', 'metadata', 'dtb']
            extracted_partitions = []

            current_progress = 35
            progress_step = 50 / len(key_partitions)  # 35-85%用于分区提取

            for partition_name in key_partitions:
                if not self.running:
                    break

                self.progress_updated.emit(int(current_progress), f"提取分区: {partition_name}")

                if partition_name == 'dtb':
                    output_file = os.path.join(builder.package_dir, "meson1.dtb")
                else:
                    output_file = os.path.join(builder.package_dir, f"{partition_name}.PARTITION")

                success, msg = self.extractor.extract_partition(partition_name, output_file, self.progress_updated.emit)

                if success:
                    extracted_partitions.append({
                        'name': partition_name,
                        'path': f"/dev/block/{partition_name}",
                        'size': msg.split(': ')[-1] if ': ' in msg else "Unknown"
                    })
                    self.progress_updated.emit(int(current_progress), f"{partition_name}提取成功: {msg}")
                else:
                    self.progress_updated.emit(int(current_progress), f"{partition_name}提取失败: {msg}")

                current_progress += progress_step

            # 步骤5: 生成配置文件
            self.progress_updated.emit(90, "生成配置文件...")

            # 生成platform.conf
            success, msg = builder.create_platform_conf()
            if success:
                self.progress_updated.emit(92, msg)
            else:
                self.progress_updated.emit(92, f"配置文件警告: {msg}")

            # 生成image.cfg
            success, msg = builder.create_image_cfg(extracted_partitions)
            if success:
                self.progress_updated.emit(95, msg)
            else:
                self.progress_updated.emit(95, f"配置文件警告: {msg}")

            # 生成README
            success, msg = builder.create_readme()
            if success:
                self.progress_updated.emit(98, msg)

            self.progress_updated.emit(100, "线刷包制作完成！")
            self.finished.emit(True, f"线刷包已创建: {builder.package_dir}")

        except Exception as e:
            self.finished.emit(False, f"制作过程异常: {str(e)}")

    def stop(self):
        """停止制作"""
        self.running = False

class MainWindow(QMainWindow):
    """主窗口"""

    def __init__(self):
        super().__init__()
        self.extractor = AmlogicFirmwareExtractor()
        self.firmware_worker = None
        self.output_dir = None
        self.init_ui()
        self.apply_style()

    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("标准晶晨线刷包制作工具 v2.0 - By.举个🌰")
        self.setGeometry(100, 100, 800, 600)
        self.setWindowIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))

        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 标题区域
        title_label = QLabel("🔧 标准晶晨线刷包制作工具 v2.0")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #bd93f9; padding: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # 功能说明
        desc_label = QLabel("✨ 完整支持DDR.USB、UBOOT.USB生成和标准配置文件格式")
        desc_label.setStyleSheet("font-size: 12px; color: #6272a4; padding: 5px;")
        desc_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(desc_label)

        # 连接区域
        connection_group = QGroupBox("设备连接")
        connection_layout = QHBoxLayout(connection_group)

        self.ip_label = QLabel(f"设备IP: {self.extractor.device_ip}")
        self.connect_btn = QPushButton("连接设备")
        self.connect_btn.clicked.connect(self.connect_device)
        self.status_label = QLabel("未连接")

        connection_layout.addWidget(self.ip_label)
        connection_layout.addWidget(self.connect_btn)
        connection_layout.addWidget(self.status_label)
        connection_layout.addStretch()

        # 设备信息区域
        info_group = QGroupBox("设备信息")
        info_layout = QVBoxLayout(info_group)
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(120)
        self.info_text.setReadOnly(True)
        info_layout.addWidget(self.info_text)

        # 输出路径区域
        path_group = QGroupBox("输出设置")
        path_layout = QHBoxLayout(path_group)

        self.path_label = QLabel("输出路径: 未选择")
        self.select_path_btn = QPushButton("选择输出路径")
        self.select_path_btn.clicked.connect(self.select_output_path)

        path_layout.addWidget(self.path_label)
        path_layout.addWidget(self.select_path_btn)

        # 制作区域
        make_group = QGroupBox("线刷包制作")
        make_layout = QVBoxLayout(make_group)

        # 制作按钮
        self.make_btn = QPushButton("🚀 制作标准线刷包")
        self.make_btn.clicked.connect(self.make_firmware)
        self.make_btn.setEnabled(False)
        self.make_btn.setStyleSheet("font-weight: bold; font-size: 14px; padding: 10px;")

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_label = QLabel("等待开始...")

        make_layout.addWidget(self.make_btn)
        make_layout.addWidget(self.progress_bar)
        make_layout.addWidget(self.progress_label)

        # 日志区域
        log_group = QGroupBox("制作日志")
        log_layout = QVBoxLayout(log_group)
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        # 添加到主布局
        main_layout.addWidget(connection_group)
        main_layout.addWidget(info_group)
        main_layout.addWidget(path_group)
        main_layout.addWidget(make_group)
        main_layout.addWidget(log_group)

    def apply_style(self):
        """应用样式"""
        style = """
        QMainWindow {
            background-color: #282a36;
            color: #f8f8f2;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #6272a4;
            border-radius: 5px;
            margin-top: 1ex;
            color: #f8f8f2;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #bd93f9;
        }
        QPushButton {
            background-color: #44475a;
            border: 1px solid #6272a4;
            border-radius: 4px;
            padding: 8px;
            color: #f8f8f2;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #6272a4;
        }
        QPushButton:pressed {
            background-color: #bd93f9;
        }
        QPushButton:disabled {
            background-color: #44475a;
            color: #6272a4;
        }
        QTextEdit {
            background-color: #44475a;
            border: 1px solid #6272a4;
            border-radius: 4px;
            color: #f8f8f2;
            selection-background-color: #bd93f9;
        }
        QProgressBar {
            border: 1px solid #6272a4;
            border-radius: 4px;
            text-align: center;
            color: #f8f8f2;
        }
        QProgressBar::chunk {
            background-color: #50fa7b;
            border-radius: 3px;
        }
        QLabel {
            color: #f8f8f2;
        }
        """
        self.setStyleSheet(style)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

    def connect_device(self):
        """连接设备"""
        self.connect_btn.setEnabled(False)
        self.connect_btn.setText("连接中...")
        self.log_message("正在连接设备...")

        def connect_thread():
            success, message = self.extractor.connect_device()
            QTimer.singleShot(0, lambda: self.on_connect_finished(success, message))

        threading.Thread(target=connect_thread, daemon=True).start()

    def on_connect_finished(self, success, message):
        """连接完成回调"""
        self.connect_btn.setEnabled(True)
        self.connect_btn.setText("连接设备")

        if success:
            self.status_label.setText("✅ 已连接")
            self.status_label.setStyleSheet("color: #50fa7b;")
            self.log_message("设备连接成功")

            # 获取设备信息
            self.update_device_info()
        else:
            self.status_label.setText("❌ 连接失败")
            self.status_label.setStyleSheet("color: #ff5555;")
            self.log_message(f"连接失败: {message}")

    def update_device_info(self):
        """更新设备信息"""
        def get_info_thread():
            info = self.extractor.get_device_info()
            QTimer.singleShot(0, lambda: self.display_device_info(info))

        threading.Thread(target=get_info_thread, daemon=True).start()

    def display_device_info(self, info):
        """显示设备信息"""
        if 'error' in info:
            self.info_text.setText(f"获取设备信息失败: {info['error']}")
        else:
            info_text = f"型号: {info.get('model', 'Unknown')}\n"
            info_text += f"品牌: {info.get('brand', 'Unknown')}\n"
            info_text += f"平台: {info.get('platform', 'Unknown')}\n"
            info_text += f"硬件: {info.get('hardware', 'Unknown')}\n"
            info_text += f"Android版本: {info.get('android_version', 'Unknown')}\n"
            info_text += f"构建ID: {info.get('build_id', 'Unknown')}"
            self.info_text.setText(info_text)

            # 检查是否可以开始制作
            if self.output_dir:
                self.make_btn.setEnabled(True)

    def select_output_path(self):
        """选择输出路径"""
        dialog = QFileDialog()
        path = dialog.getExistingDirectory(self, "选择线刷包输出路径")

        if path:
            self.output_dir = path
            self.path_label.setText(f"输出路径: {path}")
            self.log_message(f"输出路径设置为: {path}")

            # 检查是否可以开始制作
            if self.extractor.connected and self.extractor.device_info:
                self.make_btn.setEnabled(True)

    def make_firmware(self):
        """制作固件包"""
        if not self.extractor.connected:
            self.log_message("请先连接设备")
            return

        if not self.output_dir:
            self.log_message("请先选择输出路径")
            return

        # 禁用按钮
        self.make_btn.setEnabled(False)
        self.make_btn.setText("制作中...")

        # 启动制作线程
        self.firmware_worker = FirmwareWorker(self.extractor, self.output_dir)
        self.firmware_worker.progress_updated.connect(self.update_progress)
        self.firmware_worker.finished.connect(self.on_make_finished)
        self.firmware_worker.start()

        self.log_message("开始制作标准线刷包...")

    def update_progress(self, value, message):
        """更新进度"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(message)
        self.log_message(message)

    def on_make_finished(self, success, message):
        """制作完成回调"""
        self.make_btn.setEnabled(True)
        self.make_btn.setText("🚀 制作标准线刷包")

        if success:
            self.log_message("✅ 标准线刷包制作完成！")

            # 显示完成对话框
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("制作完成")
            msg_box.setText("标准线刷包制作完成！")
            msg_box.setDetailedText(f"输出路径: {message}")
            msg_box.setStandardButtons(QMessageBox.Ok | QMessageBox.Open)
            msg_box.setDefaultButton(QMessageBox.Open)

            result = msg_box.exec()
            if result == QMessageBox.Open:
                # 打开输出目录
                if os.name == 'nt':  # Windows
                    os.startfile(message)
                else:  # Linux/Mac
                    subprocess.run(['xdg-open', message])
        else:
            self.log_message(f"❌ 线刷包制作失败: {message}")
            QMessageBox.warning(self, "制作失败", message)

    def closeEvent(self, event):
        """关闭事件"""
        if self.firmware_worker and self.firmware_worker.isRunning():
            reply = QMessageBox.question(self, "确认退出", "线刷包制作正在进行中，确定要退出吗？")
            if reply == QMessageBox.Yes:
                self.firmware_worker.stop()
                self.firmware_worker.wait(3000)
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("标准晶晨线刷包制作工具")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("By.举个🌰")

    # 检查ADB是否可用
    try:
        result = subprocess.run("adb version", shell=True, capture_output=True, text=True, timeout=5)
        if result.returncode != 0:
            QMessageBox.critical(None, "错误", "未找到ADB工具，请确保ADB已安装并添加到系统PATH中。")
            sys.exit(1)
    except Exception as e:
        QMessageBox.critical(None, "错误", f"ADB检查失败: {str(e)}")
        sys.exit(1)

    window = MainWindow()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
