#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ADB root权限获取
Copyright (c) 2025 By.举个🌰
"""

import sys
import os
import subprocess

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 晶晨ADB备份工具 import ADBManager

def test_root_access():
    """测试root权限获取"""
    print("🧪 测试ADB root权限获取")
    print("=" * 50)
    
    # 创建ADB管理器
    adb_manager = ADBManager('***************', '5555')
    
    # 测试连接（包含root权限获取）
    print("🔗 连接设备并获取root权限...")
    success, message = adb_manager.connect_device()
    print(f"连接结果: {success}")
    print(f"消息: {message}")
    
    if not success:
        print("❌ 连接失败，无法继续测试")
        return False
    
    # 测试当前用户身份
    print("\n👤 检查当前用户身份...")
    adb_prefix = adb_manager._get_adb_command_prefix()
    
    cmd = f'{adb_prefix} shell "id"'
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
    
    if result.returncode == 0:
        print(f"用户身份: {result.stdout.strip()}")
        if "uid=0(root)" in result.stdout:
            print("✅ 当前具有root权限")
            has_root = True
        else:
            print("❌ 当前没有root权限")
            has_root = False
    else:
        print(f"❌ 无法获取用户身份: {result.stderr}")
        has_root = False
    
    # 测试分区访问权限
    print("\n🔍 测试分区访问权限...")
    test_partitions = [
        "/dev/block/mmcblk0p5",  # boot
        "/dev/block/mmcblk0p6",  # recovery
        "/dev/block/mmcblk0p7"   # system
    ]
    
    accessible_count = 0
    for partition in test_partitions:
        cmd = f'{adb_prefix} shell "test -r {partition} && echo readable || echo not_readable"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=3)
        
        if result.returncode == 0:
            status = result.stdout.strip()
            if "readable" in status:
                print(f"✅ {partition}: 可读")
                accessible_count += 1
            else:
                print(f"❌ {partition}: 不可读")
        else:
            print(f"⚠️  {partition}: 测试失败")
    
    print(f"\n📊 分区访问测试结果: {accessible_count}/{len(test_partitions)} 个分区可访问")
    
    # 测试备份命令
    if has_root and accessible_count > 0:
        print("\n🔧 测试备份命令...")
        test_partition = "/dev/block/mmcblk0p5"  # boot分区，通常较小
        
        # 测试dd命令
        print("测试dd命令...")
        cmd = f'{adb_prefix} shell "dd if={test_partition} bs=1024 count=1 2>/dev/null | wc -c"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and result.stdout.strip().isdigit():
            size = int(result.stdout.strip())
            if size > 0:
                print(f"✅ dd命令测试成功，读取了 {size} 字节")
            else:
                print("❌ dd命令返回0字节")
        else:
            print(f"❌ dd命令测试失败: {result.stderr}")
        
        # 测试cat命令
        print("测试cat命令...")
        cmd = f'{adb_prefix} shell "head -c 1024 {test_partition} 2>/dev/null | wc -c"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and result.stdout.strip().isdigit():
            size = int(result.stdout.strip())
            if size > 0:
                print(f"✅ cat命令测试成功，读取了 {size} 字节")
            else:
                print("❌ cat命令返回0字节")
        else:
            print(f"❌ cat命令测试失败: {result.stderr}")
    
    return has_root and accessible_count > 0

def test_manual_root():
    """手动测试root权限获取"""
    print("\n🔧 手动测试root权限获取")
    print("=" * 50)
    
    device_ip = "***************"
    device_port = "5555"
    
    # 手动执行adb root命令
    print("执行 adb root 命令...")
    cmd = f"adb -s {device_ip}:{device_port} root"
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
    
    print(f"返回码: {result.returncode}")
    print(f"输出: {result.stdout}")
    if result.stderr:
        print(f"错误: {result.stderr}")
    
    # 等待重启
    import time
    print("等待ADB重启...")
    time.sleep(3)
    
    # 检查root状态
    print("检查root状态...")
    cmd = f"adb -s {device_ip}:{device_port} shell id"
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
    
    print(f"用户身份: {result.stdout.strip()}")
    
    if "uid=0(root)" in result.stdout:
        print("✅ 手动获取root权限成功")
        return True
    else:
        print("❌ 手动获取root权限失败")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 ADB Root权限测试工具")
    print("Copyright (c) 2025 By.举个🌰")
    print("=" * 60)
    
    # 测试1: 自动root权限获取
    auto_success = test_root_access()
    
    # 测试2: 手动root权限获取
    manual_success = test_manual_root()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"  自动root权限获取: {'✅ 成功' if auto_success else '❌ 失败'}")
    print(f"  手动root权限获取: {'✅ 成功' if manual_success else '❌ 失败'}")
    
    if auto_success or manual_success:
        print("\n🎉 root权限获取成功！现在可以进行分区备份了")
    else:
        print("\n⚠️  root权限获取失败，可能的原因:")
        print("  1. 设备不支持adb root")
        print("  2. 设备未开启开发者选项")
        print("  3. 设备系统版本限制")
        print("  4. 需要手动在设备上授权")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
