Metadata-Version: 2.1
Name: pyzstd
Version: 0.17.0
Summary: Python bindings to Zstandard (zstd) compression library.
Home-page: https://github.com/Rogdham/pyzstd
Author: <PERSON>
Author-email: <EMAIL>
Maintainer: <PERSON><PERSON><PERSON>
Maintainer-email: <EMAIL>
License: BSD-3-Clause
Keywords: zstandard zstd zst compress decompress tar file seekable format
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Topic :: System :: Archiving :: Compression
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.5
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: typing-extensions >=4.13.2 ; python_version < "3.13"

<div align="center" size="15px">

# pyzstd

Python bindings to Zstandard (zstd) compression library

[![GitHub build status](https://img.shields.io/github/actions/workflow/status/rogdham/pyzstd/build.yml?branch=master)](https://github.com/rogdham/pyzstd/actions?query=branch:master)
[![Release on PyPI](https://img.shields.io/pypi/v/pyzstd)](https://pypi.org/project/pyzstd/)
[![BSD-3-Clause License](https://img.shields.io/pypi/l/pyzstd)](https://github.com/Rogdham/pyzstd/blob/master/LICENSE.txt)

---

[📖 Documentation](https://pyzstd.readthedocs.io/)&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;[📃 Changelog](./CHANGELOG.md)

</div>

---

Pyzstd module provides classes and functions for compressing and decompressing data, using Facebook's [Zstandard](http://www.zstd.net) (or zstd as short name) algorithm.

The API style is similar to Python's bz2/lzma/zlib modules.

- Includes zstd v1.5.6 source code
- Can also dynamically link to zstd library provided by system, see [this note](https://pyzstd.readthedocs.io/#build-pyzstd).
- Has a CFFI implementation that can work with PyPy
- Support sub-interpreter on CPython 3.12+
- `ZstdFile` class has C language level performance
- Supports [Zstandard Seekable Format](https://github.com/facebook/zstd/blob/dev/contrib/seekable_format/zstd_seekable_compression_format.md)
- Has a command line interface: `python -m pyzstd --help`
