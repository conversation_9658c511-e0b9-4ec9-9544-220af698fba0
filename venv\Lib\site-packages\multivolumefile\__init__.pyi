import contextlib
import io
import pathlib
from mmap import mmap
from typing import Any, Container, List, Optional, Union

def open(
    name: Union[pathlib.Path, str], mode: Any = ..., volume: Any = ...
) -> io.RawIOBase: ...

class _FileInfo:
    filename: Any = ...
    size: Any = ...
    def __init__(self, filename: Any, size: Any) -> None: ...

class MultiVolume(io.RawIOBase, contextlib.AbstractContextManager):
    def __init__(
        self,
        basename: Union[pathlib.Path, str],
        mode: Optional[str] = ...,
        *,
        volume: Optional[int] = ...,
        ext_digits: Optional[int] = ...,
        hex: Optional[bool] = ...,
        ext_start: Optional[int] = ...
    ) -> None: ...
    def read(self, size: int = ...) -> bytes: ...
    def readall(self) -> bytes: ...
    def readinto(
        self, b: Union[bytearray, memoryview, Container[Any], mmap]
    ) -> int: ...
    def write(
        self, b: Union[bytes, bytearray, memoryview, Container[Any], mmap]
    ) -> None: ...
    def close(self) -> None: ...
    @property
    def closed(self) -> bool: ...
    def fileno(self) -> int: ...
    def flush(self) -> None: ...
    def isatty(self) -> bool: ...
    def readable(self) -> bool: ...
    def readline(self, size: Optional[int] = ...) -> bytes: ...
    def readlines(self, hint: int = ...) -> List[bytes]: ...
    def seek(self, offset: int, whence: Optional[int] = ...) -> int: ...
    def seekable(self) -> bool: ...
    def tell(self) -> int: ...
    def truncate(self, size: Optional[int] = ...) -> int: ...
    def writable(self) -> bool: ...
    def writelines(self, lines: Any) -> None: ...
    def __del__(self) -> None: ...
    def __enter__(self): ...
    def __exit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None: ...
