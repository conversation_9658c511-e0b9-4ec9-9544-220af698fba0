[{"classes": [{"className": "QPlatformScreen", "gadget": true, "lineNumber": 43, "qualifiedClassName": "QPlatformScreen"}], "inputFile": "qplatformscreen.h", "outputRevision": 69}, {"classes": [{"className": "QFontVariableAxis", "gadget": true, "lineNumber": 17, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "tag", "read": "tagString", "required": false, "scriptable": true, "stored": true, "type": "QByteArray", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "minimumValue", "read": "minimumValue", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "maximumValue", "read": "maximumValue", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "defaultValue", "read": "defaultValue", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QFontVariableAxis"}], "inputFile": "qfontvariableaxis.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractTextDocumentLayout", "lineNumber": 22, "object": true, "qualifiedClassName": "QAbstractTextDocumentLayout", "signals": [{"access": "public", "arguments": [{"type": "QRectF"}], "index": 0, "name": "update", "returnType": "void"}, {"access": "public", "index": 1, "isCloned": true, "name": "update", "returnType": "void"}, {"access": "public", "arguments": [{"name": "block", "type": "QTextBlock"}], "index": 2, "name": "updateBlock", "returnType": "void"}, {"access": "public", "arguments": [{"name": "newSize", "type": "QSizeF"}], "index": 3, "name": "documentSizeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "newPages", "type": "int"}], "index": 4, "name": "pageCountChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 5, "name": "_q_dynamicPageCountSlot", "returnType": "int"}, {"access": "private", "index": 6, "name": "_q_dynamicDocumentSizeSlot", "returnType": "QSizeF"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstracttextdocumentlayout.h", "outputRevision": 69}, {"classes": [{"className": "QAccessible", "enums": [{"isClass": false, "isFlag": false, "name": "Event", "values": ["SoundPlayed", "<PERSON><PERSON>", "ForegroundChanged", "MenuStart", "MenuEnd", "PopupMenuStart", "PopupMenuEnd", "ContextHelpStart", "ContextHelpEnd", "DragDropStart", "DragDropEnd", "DialogStart", "DialogEnd", "ScrollingStart", "ScrollingEnd", "MenuC<PERSON>mand", "ActionChanged", "ActiveDescendantChanged", "AttributeChanged", "DocumentContentChanged", "DocumentLoadComplete", "DocumentLoadStopped", "DocumentReload", "HyperlinkEndIndexChanged", "HyperlinkNumberOfAnchorsChanged", "HyperlinkSelectedLinkChanged", "HypertextLinkActivated", "HypertextLinkSelected", "HyperlinkStartIndexChanged", "HypertextChanged", "HypertextNLinksChanged", "ObjectAttributeChanged", "Page<PERSON><PERSON>ed", "SectionChanged", "TableCaptionChanged", "TableColumnDescriptionChanged", "TableColumnHeaderChanged", "TableModelChanged", "TableRowDescriptionChanged", "TableRowHeaderChanged", "TableSummaryChanged", "TextAttributeChanged", "TextCaretMoved", "TextColumnChanged", "TextInserted", "TextRemoved", "TextUpdated", "TextSelectionChanged", "VisibleDataChanged", "ObjectCreated", "ObjectDestroyed", "ObjectShow", "ObjectHide", "ObjectReorder", "Focus", "Selection", "SelectionAdd", "SelectionRemove", "SelectionWithin", "StateChanged", "LocationChanged", "NameChanged", "DescriptionChanged", "ValueChanged", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HelpChanged", "DefaultActionChanged", "AcceleratorChanged", "Announcement", "IdentifierChanged", "InvalidEvent"]}, {"isClass": false, "isFlag": false, "name": "Role", "values": ["NoRole", "TitleBar", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>Bar", "<PERSON><PERSON>", "Sound", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "AlertM<PERSON>age", "Window", "Client", "PopupMenu", "MenuItem", "ToolTip", "Application", "Document", "Pane", "Chart", "Dialog", "Border", "Grouping", "Separator", "<PERSON><PERSON><PERSON><PERSON>", "StatusBar", "Table", "<PERSON>umn<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Column", "Row", "Cell", "Link", "HelpBalloon", "Assistant", "List", "ListItem", "Tree", "TreeItem", "PageTab", "PropertyPage", "Indicator", "Graphic", "StaticText", "EditableText", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckBox", "RadioButton", "ComboBox", "ProgressBar", "<PERSON><PERSON>", "HotkeyField", "Slide<PERSON>", "SpinBox", "<PERSON><PERSON>", "Animation", "Equation", "ButtonDropDown", "ButtonMenu", "ButtonDropGrid", "Whitespace", "PageTabList", "Clock", "Splitter", "LayeredPane", "Terminal", "Desktop", "Paragraph", "WebDocument", "Section", "Notification", "ColorChooser", "Footer", "Form", "Heading", "Note", "ComplementaryContent", "BlockQuote", "UserRole"]}, {"isClass": true, "isFlag": false, "name": "Attribute", "values": ["Custom", "Level"]}, {"isClass": true, "isFlag": false, "name": "AnnouncementPoliteness", "values": ["Polite", "Assertive"]}], "gadget": true, "lineNumber": 25, "qualifiedClassName": "QAccessible"}], "inputFile": "qaccessible_base.h", "outputRevision": 69}, {"classes": [{"className": "QAccessibleBridgePlugin", "lineNumber": 29, "object": true, "qualifiedClassName": "QAccessibleBridgePlugin", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaccessiblebridge.h", "outputRevision": 69}, {"classes": [{"className": "QAccessibleCache", "lineNumber": 30, "object": true, "qualifiedClassName": "QAccessibleCache", "slots": [{"access": "private", "arguments": [{"name": "obj", "type": "QObject*"}], "index": 0, "name": "objectDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaccessiblecache_p.h", "outputRevision": 69}, {"classes": [{"className": "QAccessiblePlugin", "lineNumber": 22, "object": true, "qualifiedClassName": "QAccessiblePlugin", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaccessibleplugin.h", "outputRevision": 69}, {"classes": [{"className": "QAction", "enums": [{"isClass": false, "isFlag": false, "name": "MenuRole", "values": ["NoRole", "TextHeuristicRole", "ApplicationSpecificRole", "AboutQtRole", "AboutRole", "PreferencesRole", "QuitRole"]}, {"isClass": false, "isFlag": false, "name": "Priority", "values": ["LowPriority", "NormalPriority", "HighPriority"]}], "lineNumber": 29, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "checkable", "notify": "checkableChanged", "read": "isCheckable", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setCheckable"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "checked", "notify": "toggled", "read": "isChecked", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setChecked"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "enabled", "notify": "enabledChanged", "read": "isEnabled", "required": false, "reset": "resetEnabled", "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setEnabled"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "icon", "notify": "changed", "read": "icon", "required": false, "scriptable": true, "stored": true, "type": "QIcon", "user": false, "write": "setIcon"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "text", "notify": "changed", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "iconText", "notify": "changed", "read": "iconText", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setIconText"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "toolTip", "notify": "changed", "read": "toolTip", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setToolTip"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "statusTip", "notify": "changed", "read": "statusTip", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setStatusTip"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "whatsThis", "notify": "changed", "read": "whatsThis", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setWhatsThis"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "font", "notify": "changed", "read": "font", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setFont"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "shortcut", "notify": "changed", "read": "shortcut", "required": false, "scriptable": true, "stored": true, "type": "QKeySequence", "user": false, "write": "setShortcut"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "shortcutContext", "notify": "changed", "read": "shortcutContext", "required": false, "scriptable": true, "stored": true, "type": "Qt::ShortcutContext", "user": false, "write": "setShortcutContext"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "autoRepeat", "notify": "changed", "read": "autoRepeat", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoRepeat"}, {"constant": false, "designable": true, "final": true, "index": 13, "name": "visible", "notify": "visibleChanged", "read": "isVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "menuRole", "notify": "changed", "read": "menuRole", "required": false, "scriptable": true, "stored": true, "type": "MenuRole", "user": false, "write": "setMenuRole"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "iconVisibleInMenu", "notify": "changed", "read": "isIconVisibleInMenu", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setIconVisibleInMenu"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "shortcutVisibleInContextMenu", "notify": "changed", "read": "isShortcutVisibleInContextMenu", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setShortcutVisibleInContextMenu"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "priority", "notify": "changed", "read": "priority", "required": false, "scriptable": true, "stored": true, "type": "Priority", "user": false, "write": "setPriority"}], "qualifiedClassName": "QAction", "signals": [{"access": "public", "index": 0, "name": "changed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 1, "name": "enabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "checkable", "type": "bool"}], "index": 2, "name": "checkableChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "visibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "checked", "type": "bool"}], "index": 4, "name": "triggered", "returnType": "void"}, {"access": "public", "index": 5, "isCloned": true, "name": "triggered", "returnType": "void"}, {"access": "public", "index": 6, "name": "hovered", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 7, "name": "toggled", "returnType": "void"}], "slots": [{"access": "public", "index": 8, "name": "trigger", "returnType": "void"}, {"access": "public", "index": 9, "name": "hover", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 10, "name": "setChecked", "returnType": "void"}, {"access": "public", "index": 11, "name": "toggle", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 12, "name": "setEnabled", "returnType": "void"}, {"access": "public", "index": 13, "name": "resetEnabled", "returnType": "void"}, {"access": "public", "arguments": [{"name": "b", "type": "bool"}], "index": 14, "name": "setDisabled", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 15, "name": "setVisible", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaction.h", "outputRevision": 69}, {"classes": [{"className": "QActionGroup", "enums": [{"isClass": true, "isFlag": false, "name": "ExclusionPolicy", "values": ["None", "Exclusive", "ExclusiveOptional"]}], "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "exclusionPolicy", "read": "exclusionPolicy", "required": false, "scriptable": true, "stored": true, "type": "QActionGroup::ExclusionPolicy", "user": false, "write": "setExclusionPolicy"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "enabled", "read": "isEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setEnabled"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "visible", "read": "isVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}], "qualifiedClassName": "QActionGroup", "signals": [{"access": "public", "arguments": [{"type": "QAction*"}], "index": 0, "name": "triggered", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QAction*"}], "index": 1, "name": "hovered", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"type": "bool"}], "index": 2, "name": "setEnabled", "returnType": "void"}, {"access": "public", "arguments": [{"name": "b", "type": "bool"}], "index": 3, "name": "setDisabled", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 4, "name": "setVisible", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 5, "name": "setExclusive", "returnType": "void"}, {"access": "public", "arguments": [{"name": "policy", "type": "ExclusionPolicy"}], "index": 6, "name": "setExclusionPolicy", "returnType": "void"}, {"access": "private", "index": 7, "name": "_q_actionTriggered", "returnType": "void"}, {"access": "private", "index": 8, "name": "_q_actionHovered", "returnType": "void"}, {"access": "private", "index": 9, "name": "_q_actionChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qactiongroup.h", "outputRevision": 69}, {"classes": [{"className": "QGradient", "enums": [{"isClass": false, "isFlag": false, "name": "Type", "values": ["LinearGradient", "RadialGrad<PERSON>", "ConicalGradient", "NoGradient"]}, {"isClass": false, "isFlag": false, "name": "Spread", "values": ["PadSpread", "ReflectSpread", "RepeatSpread"]}, {"isClass": false, "isFlag": false, "name": "CoordinateMode", "values": ["LogicalMode", "StretchToDeviceMode", "ObjectBoundingMode", "ObjectMode"]}, {"isClass": false, "isFlag": false, "name": "Preset", "values": ["WarmFlame", "NightFade", "SpringWarmth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "YoungPassion", "<PERSON><PERSON><PERSON><PERSON>", "SunnyMorning", "RainyAshville", "FrozenDreams", "<PERSON><PERSON><PERSON>", "Dusty<PERSON>rass", "TemptingAzure", "HeavyRain", "<PERSON><PERSON><PERSON><PERSON>", "MeanFruit", "DeepBlue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CloudyKnoxville", "MalibuBeach", "NewLife", "TrueSunset", "MorpheusDen", "RareWind", "NearMoon", "WildApple", "SaintPetersburg", "PlumPlate", "EverlastingSky", "<PERSON><PERSON><PERSON><PERSON>", "Blessing", "SharpeyeEagle", "LadogaBottom", "LemonGate", "ItmeoBranding", "ZeusMiracle", "OldHat", "StarWine", "HappyAcid", "AwesomePine", "NewYork", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MixedHopes", "FlyHigh", "<PERSON><PERSON><PERSON><PERSON>", "FreshMilk", "SnowAgain", "FebruaryInk", "KindSteel", "SoftGrass", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SharpBlues", "ShadyWater", "DirtyB<PERSON>ty", "GreatWhale", "TeenNotebook", "PoliteRumors", "SweetPeriod", "WideMatrix", "SoftCherish", "RedSalvation", "BurningSpring", "NightParty", "SkyGlider", "HeavenPeach", "PurpleDivision", "AquaSplash", "SpikyNaga", "<PERSON><PERSON><PERSON>", "CleanMirror", "PremiumDark", "ColdEvening", "CochitiLake", "SummerGames", "PassionateBed", "MountainRock", "DesertHump", "JungleDay", "PhoenixStart", "OctoberSilence", "FarawayRiver", "AlchemistLab", "OverSun", "PremiumWhite", "Mars<PERSON><PERSON><PERSON>", "EternalConstance", "JapanBlush", "<PERSON><PERSON><PERSON>", "CloudyApple", "BigMango", "HealthyWater", "AmourAmour", "RiskyConcrete", "StrongStick", "ViciousStance", "PaloAlto", "HappyMemories", "MidnightBloom", "Crystalline", "PartyBliss", "ConfidentCloud", "LeCocktail", "RiverCity", "FrozenBerry", "ChildCare", "FlyingLemon", "NewRetrowave", "Hidden<PERSON>aguar", "AboveTheSky", "Nega", "DenseWater", "Seashore", "MarbleWall", "Cheer<PERSON><PERSON><PERSON><PERSON>", "NightSky", "MagicLake", "Young<PERSON><PERSON>", "ColorfulPeach", "GentleCare", "PlumBath", "HappyUnicorn", "AfricanField", "SolidStone", "OrangeJuice", "GlassWater", "NorthMiracle", "FruitBlend", "MillenniumPine", "HighFlight", "MoleHall", "SpaceShift", "ForestInei", "RoyalGarden", "RichMetal", "JuicyCake", "SmartIndigo", "SandStrike", "NorseBeauty", "AquaGuidance", "SunVeggie", "SeaLord", "BlackSea", "GrassShampoo", "LandingAircraft", "WitchDance", "SleeplessNight", "AngelCare", "CrystalRiver", "SoftLipstick", "SaltMountain", "Perfect<PERSON><PERSON>e", "FreshOasis", "StrictNovember", "MorningSalad", "<PERSON><PERSON><PERSON><PERSON>", "SeaStrike", "NightCall", "SupremeSky", "LightBlue", "MindCrawl", "<PERSON><PERSON><PERSON><PERSON>", "SugarLollipop", "<PERSON><PERSON><PERSON><PERSON>", "MagicRay", "Teen<PERSON><PERSON><PERSON>", "FrozenHeat", "<PERSON><PERSON><PERSON>ie<PERSON>", "FabledSunset", "PerfectBlue", "NumPresets"]}], "gadget": true, "lineNumber": 153, "qualifiedClassName": "QGradient"}], "inputFile": "qbrush.h", "outputRevision": 69}, {"classes": [{"className": "QClipboard", "lineNumber": 19, "object": true, "qualifiedClassName": "QClipboard", "signals": [{"access": "public", "arguments": [{"name": "mode", "type": "QClipboard::Mode"}], "index": 0, "name": "changed", "returnType": "void"}, {"access": "public", "index": 1, "name": "selectionChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "findBufferChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "dataChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qclipboard.h", "outputRevision": 69}, {"classes": [{"className": "QColorSpace", "enums": [{"isClass": false, "isFlag": false, "name": "NamedColorSpace", "values": ["SRgb", "SRgbLinear", "AdobeRgb", "DisplayP3", "ProPhotoRgb", "Bt2020", "Bt2100Pq", "Bt2100Hlg"]}, {"isClass": true, "isFlag": false, "name": "Primaries", "values": ["Custom", "SRgb", "AdobeRgb", "DciP3D65", "ProPhotoRgb", "Bt2020"]}, {"isClass": true, "isFlag": false, "name": "TransferFunction", "values": ["Custom", "Linear", "Gamma", "SRgb", "ProPhotoRgb", "Bt2020", "St2084", "Hlg"]}, {"isClass": true, "isFlag": false, "name": "TransformModel", "type": "uint8_t", "values": ["ThreeComponentMatrix", "ElementListProcessing"]}, {"isClass": true, "isFlag": false, "name": "ColorModel", "type": "uint8_t", "values": ["Undefined", "Rgb", "<PERSON>", "Cmyk"]}], "gadget": true, "lineNumber": 20, "qualifiedClassName": "QColorSpace"}], "inputFile": "qcolorspace.h", "outputRevision": 69}, {"classes": [{"className": "QDragManager", "lineNumber": 57, "object": true, "qualifiedClassName": "QDragManager", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdnd_p.h", "outputRevision": 69}, {"classes": [{"className": "QDrag", "lineNumber": 21, "object": true, "qualifiedClassName": "QDrag", "signals": [{"access": "public", "arguments": [{"name": "action", "type": "Qt::DropAction"}], "index": 0, "name": "actionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "newTarget", "type": "QObject*"}], "index": 1, "name": "targetChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdrag.h", "outputRevision": 69}, {"classes": [{"className": "QPointerEvent", "gadget": true, "lineNumber": 72, "qualifiedClassName": "QPointerEvent", "superClasses": [{"access": "public", "name": "QInputEvent"}]}, {"className": "QSinglePointEvent", "gadget": true, "lineNumber": 108, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "exclusivePointGrabber", "read": "exclusivePointGrabber", "required": false, "scriptable": true, "stored": true, "type": "QObject*", "user": false, "write": "setExclusivePointGrabber"}], "qualifiedClassName": "QSinglePointEvent", "superClasses": [{"access": "public", "name": "QPointerEvent"}]}, {"className": "QWheelEvent", "gadget": true, "lineNumber": 281, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "device", "read": "pointingDevice", "required": false, "scriptable": true, "stored": true, "type": "const QPointingDevice*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pixelDelta", "read": "pixelDelta", "required": false, "scriptable": true, "stored": true, "type": "QPoint", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "<PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QPoint", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "phase", "read": "phase", "required": false, "scriptable": true, "stored": true, "type": "Qt::ScrollPhase", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "inverted", "read": "inverted", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QWheelEvent", "superClasses": [{"access": "public", "name": "QSinglePointEvent"}]}], "inputFile": "qevent.h", "outputRevision": 69}, {"classes": [{"className": "QEventPoint", "enums": [{"alias": "State", "isClass": false, "isFlag": true, "name": "States", "type": "quint8", "values": ["Unknown", "Stationary", "Pressed", "Updated", "Released"]}], "gadget": true, "lineNumber": 19, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "accepted", "read": "isAccepted", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAccepted"}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "device", "read": "device", "required": false, "scriptable": true, "stored": true, "type": "const QPointingDevice*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "id", "read": "id", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "uniqueId", "read": "uniqueId", "required": false, "scriptable": true, "stored": true, "type": "QPointingDeviceUniqueId", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "state", "read": "state", "required": false, "scriptable": true, "stored": true, "type": "State", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "timestamp", "read": "timestamp", "required": false, "scriptable": true, "stored": true, "type": "<PERSON><PERSON>", "user": false}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "pressTimestamp", "read": "pressTimestamp", "required": false, "scriptable": true, "stored": true, "type": "<PERSON><PERSON>", "user": false}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "lastTimestamp", "read": "lastTimestamp", "required": false, "scriptable": true, "stored": true, "type": "<PERSON><PERSON>", "user": false}, {"constant": true, "designable": true, "final": false, "index": 8, "name": "timeHeld", "read": "timeHeld", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 9, "name": "pressure", "read": "pressure", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 10, "name": "rotation", "read": "rotation", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 11, "name": "ellipseDiameters", "read": "ellipseDiameters", "required": false, "scriptable": true, "stored": true, "type": "QSizeF", "user": false}, {"constant": true, "designable": true, "final": false, "index": 12, "name": "velocity", "read": "velocity", "required": false, "scriptable": true, "stored": true, "type": "QVector2D", "user": false}, {"constant": true, "designable": true, "final": false, "index": 13, "name": "position", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false}, {"constant": true, "designable": true, "final": false, "index": 14, "name": "pressPosition", "read": "pressPosition", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false}, {"constant": true, "designable": true, "final": false, "index": 15, "name": "grabPosition", "read": "grabPosition", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false}, {"constant": true, "designable": true, "final": false, "index": 16, "name": "lastPosition", "read": "lastPosition", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false}, {"constant": true, "designable": true, "final": false, "index": 17, "name": "scenePosition", "read": "scenePosition", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false}, {"constant": true, "designable": true, "final": false, "index": 18, "name": "scenePressPosition", "read": "scenePressPosition", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false}, {"constant": true, "designable": true, "final": false, "index": 19, "name": "sceneGrabPosition", "read": "sceneGrabPosition", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false}, {"constant": true, "designable": true, "final": false, "index": 20, "name": "sceneLastPosition", "read": "sceneLastPosition", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false}, {"constant": true, "designable": true, "final": false, "index": 21, "name": "globalPosition", "read": "globalPosition", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false}, {"constant": true, "designable": true, "final": false, "index": 22, "name": "globalPressPosition", "read": "globalPressPosition", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false}, {"constant": true, "designable": true, "final": false, "index": 23, "name": "globalGrabPosition", "read": "globalGrabPosition", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false}, {"constant": true, "designable": true, "final": false, "index": 24, "name": "globalLastPosition", "read": "globalLastPosition", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false}], "qualifiedClassName": "QEventPoint"}], "inputFile": "qeventpoint.h", "outputRevision": 69}, {"classes": [{"className": "QFileInfoGatherer", "lineNumber": 125, "object": true, "qualifiedClassName": "QFileInfoGatherer", "signals": [{"access": "public", "arguments": [{"name": "directory", "type": "QString"}, {"name": "updates", "type": "QList<std::pair<QString,QFileInfo>>"}], "index": 0, "name": "updates", "returnType": "void"}, {"access": "public", "arguments": [{"name": "directory", "type": "QString"}, {"name": "listOfFiles", "type": "QStringList"}], "index": 1, "isConst": true, "name": "newListOfFiles", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fileName", "type": "QString"}, {"name": "resolvedName", "type": "QString"}], "index": 2, "isConst": true, "name": "nameResolved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "path", "type": "QString"}], "index": 3, "name": "directoryLoaded", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "directoryPath", "type": "QString"}], "index": 4, "name": "list", "returnType": "void"}, {"access": "public", "arguments": [{"name": "path", "type": "QString"}, {"name": "files", "type": "QStringList"}], "index": 5, "name": "fetchExtendedInformation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "path", "type": "QString"}], "index": 6, "name": "updateFile", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "index": 7, "name": "setResolveSymlinks", "returnType": "void"}, {"access": "public", "arguments": [{"name": "provider", "type": "QAbstractFileIconProvider*"}], "index": 8, "name": "setIconProvider", "returnType": "void"}, {"access": "private", "index": 9, "name": "driveAdded", "returnType": "void"}, {"access": "private", "index": 10, "name": "driveRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QThread"}]}], "inputFile": "qfileinfogatherer_p.h", "outputRevision": 69}, {"classes": [{"className": "QFileSystemModel", "enums": [{"isClass": false, "isFlag": false, "name": "Option", "values": ["DontWatchForChanges", "DontResolveSymlinks", "DontUseCustomDirectoryIcons"]}], "lineNumber": 20, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "resolveSymlinks", "read": "resolveSymlinks", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setResolveSymlinks"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "readOnly", "read": "isReadOnly", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setReadOnly"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "nameFilterDisables", "read": "nameFilterDisables", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setNameFilterDisables"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "options", "read": "options", "required": false, "scriptable": true, "stored": true, "type": "Options", "user": false, "write": "setOptions"}], "qualifiedClassName": "QFileSystemModel", "signals": [{"access": "public", "arguments": [{"name": "newPath", "type": "QString"}], "index": 0, "name": "rootPathChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "path", "type": "QString"}, {"name": "old<PERSON>ame", "type": "QString"}, {"name": "newName", "type": "QString"}], "index": 1, "name": "fileRenamed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "path", "type": "QString"}], "index": 2, "name": "directoryLoaded", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractItemModel"}]}], "inputFile": "qfilesystemmodel.h", "outputRevision": 69}, {"classes": [{"className": "QFont", "enums": [{"isClass": false, "isFlag": false, "name": "StyleHint", "values": ["Helvetica", "SansSeri<PERSON>", "Times", "<PERSON><PERSON>", "Courier", "TypeWriter", "OldEnglish", "Decorative", "System", "AnyStyle", "Cursive", "Monospace", "Fantasy"]}, {"isClass": false, "isFlag": false, "name": "StyleStrategy", "values": ["PreferDefault", "PreferBitmap", "PreferDevice", "PreferOutline", "ForceOutline", "PreferMatch", "PreferQuality", "PreferAntialias", "NoAntialias", "NoSubpixelAntialias", "PreferNoShaping", "ContextFontMerging", "PreferTypoLineMetrics", "NoFontMerging"]}, {"isClass": false, "isFlag": false, "name": "HintingPreference", "values": ["PreferDefaultHinting", "PreferNoHinting", "PreferVerticalHinting", "PreferFullHinting"]}, {"isClass": false, "isFlag": false, "name": "Weight", "values": ["Thin", "ExtraLight", "Light", "Normal", "Medium", "DemiBold", "Bold", "ExtraBold", "Black"]}, {"isClass": false, "isFlag": false, "name": "Style", "values": ["StyleNormal", "StyleItalic", "StyleOblique"]}, {"isClass": false, "isFlag": false, "name": "<PERSON><PERSON><PERSON>", "values": ["AnyStretch", "UltraCondensed", "ExtraCondensed", "Condensed", "SemiCondensed", "Unstretched", "SemiExpanded", "Expanded", "ExtraExpanded", "UltraExpanded"]}, {"isClass": false, "isFlag": false, "name": "Capitalization", "values": ["MixedCase", "AllUppercase", "AllLowercase", "SmallCaps", "Capitalize"]}, {"isClass": false, "isFlag": false, "name": "SpacingType", "values": ["PercentageSpacing", "AbsoluteSpacing"]}, {"isClass": false, "isFlag": false, "name": "ResolveProperties", "values": ["NoPropertiesResolved", "FamilyResolved", "SizeResolved", "StyleHintResolved", "StyleStrategyResolved", "WeightResolved", "StyleResolved", "UnderlineResolved", "OverlineResolved", "StrikeOutResolved", "FixedPitchResolved", "StretchResolved", "KerningResolved", "CapitalizationResolved", "LetterSpacingResolved", "WordSpacingResolved", "HintingPreferenceResolved", "StyleNameResolved", "FamiliesResolved", "FeaturesResolved", "VariableAxesResolved", "AllPropertiesResolved"]}], "gadget": true, "lineNumber": 21, "qualifiedClassName": "QFont"}], "inputFile": "qfont.h", "outputRevision": 69}, {"classes": [{"className": "QFontDatabase", "enums": [{"isClass": false, "isFlag": false, "name": "WritingSystem", "values": ["Any", "Latin", "Greek", "Cyrillic", "Armenian", "Hebrew", "Arabic", "Syriac", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Bengali", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Gujarati", "Oriya", "Tamil", "Telugu", "Kannada", "Malayalam", "Sinhala", "Thai", "Lao", "Tibetan", "Myanmar", "Georgian", "Khmer", "SimplifiedChinese", "TraditionalChinese", "Japanese", "Korean", "Vietnamese", "Symbol", "Other", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Nko", "WritingSystemsCount"]}, {"isClass": false, "isFlag": false, "name": "SystemFont", "values": ["GeneralFont", "FixedFont", "TitleFont", "SmallestReadableFont"]}], "gadget": true, "lineNumber": 18, "qualifiedClassName": "QFontDatabase"}], "inputFile": "qfontdatabase.h", "outputRevision": 69}, {"classes": [{"className": "QGenericPlugin", "lineNumber": 15, "object": true, "qualifiedClassName": "QGenericPlugin", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgenericplugin.h", "outputRevision": 69}, {"classes": [{"className": "QGuiApplication", "lineNumber": 36, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "windowIcon", "read": "windowIcon", "required": false, "scriptable": true, "stored": true, "type": "QIcon", "user": false, "write": "setWindowIcon"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "applicationDisplayName", "notify": "applicationDisplayNameChanged", "read": "applicationDisplayName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setApplicationDisplayName"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "desktopFileName", "read": "desktopFileName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setDesktopFileName"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "layoutDirection", "notify": "layoutDirectionChanged", "read": "layoutDirection", "required": false, "scriptable": true, "stored": true, "type": "Qt::LayoutDirection", "user": false, "write": "setLayoutDirection"}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "platformName", "read": "platformName", "required": false, "scriptable": true, "stored": false, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "quitOnLastWindowClosed", "read": "quitOnLastWindowClosed", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setQuitOnLastWindowClosed"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "primaryScreen", "notify": "primaryScreenChanged", "read": "primaryScreen", "required": false, "scriptable": true, "stored": false, "type": "QScreen*", "user": false}], "qualifiedClassName": "QGuiApplication", "signals": [{"access": "public", "index": 0, "name": "fontDatabaseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "screen", "type": "QScreen*"}], "index": 1, "name": "screenAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "screen", "type": "QScreen*"}], "index": 2, "name": "screenRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "screen", "type": "QScreen*"}], "index": 3, "name": "primaryScreenChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "lastWindowClosed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "focusObject", "type": "QObject*"}], "index": 5, "name": "focusObjectChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "focusWindow", "type": "QWindow*"}], "index": 6, "name": "focusWindowChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "Qt::ApplicationState"}], "index": 7, "name": "applicationStateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "direction", "type": "Qt::LayoutDirection"}], "index": 8, "name": "layoutDirectionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "session<PERSON>anager", "type": "QSessionManager&"}], "index": 9, "name": "commitDataRequest", "returnType": "void"}, {"access": "public", "arguments": [{"name": "session<PERSON>anager", "type": "QSessionManager&"}], "index": 10, "name": "saveStateRequest", "returnType": "void"}, {"access": "public", "index": 11, "name": "applicationDisplayNameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pal", "type": "QPalette"}], "index": 12, "name": "paletteChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "index": 13, "name": "fontChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "number", "type": "qint64"}], "index": 14, "name": "setBadgeNumber", "returnType": "void"}, {"access": "private", "arguments": [{"name": "object", "type": "QObject*"}], "index": 15, "name": "_q_updateFocusObject", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QCoreApplication"}]}], "inputFile": "qguiapplication.h", "outputRevision": 69}, {"classes": [{"className": "QHighDpiScaling", "enums": [{"isClass": true, "isFlag": false, "name": "DpiAdjustmentPolicy", "values": ["Unset", "Enabled", "Disabled", "UpOnly"]}], "gadget": true, "lineNumber": 38, "qualifiedClassName": "QHighDpiScaling"}], "inputFile": "qhighdpiscaling_p.h", "outputRevision": 69}, {"classes": [{"className": "QIconEnginePlugin", "lineNumber": 18, "object": true, "qualifiedClassName": "QIconEnginePlugin", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qiconengineplugin.h", "outputRevision": 69}, {"classes": [{"className": "QImage", "enums": [{"isClass": false, "isFlag": false, "name": "Format", "values": ["Format_Invalid", "Format_Mono", "Format_MonoLSB", "Format_Indexed8", "Format_RGB32", "Format_ARGB32", "Format_ARGB32_Premultiplied", "Format_RGB16", "Format_ARGB8565_Premultiplied", "Format_RGB666", "Format_ARGB6666_Premultiplied", "Format_RGB555", "Format_ARGB8555_Premultiplied", "Format_RGB888", "Format_RGB444", "Format_ARGB4444_Premultiplied", "Format_RGBX8888", "Format_RGBA8888", "Format_RGBA8888_Premultiplied", "Format_BGR30", "Format_A2BGR30_Premultiplied", "Format_RGB30", "Format_A2RGB30_Premultiplied", "Format_Alpha8", "Format_Grayscale8", "Format_RGBX64", "Format_RGBA64", "Format_RGBA64_Premultiplied", "Format_Grayscale16", "Format_BGR888", "Format_RGBX16FPx4", "Format_RGBA16FPx4", "Format_RGBA16FPx4_Premultiplied", "Format_RGBX32FPx4", "Format_RGBA32FPx4", "Format_RGBA32FPx4_Premultiplied", "Format_CMYK8888", "NImageFormats"]}], "gadget": true, "lineNumber": 36, "qualifiedClassName": "QImage", "superClasses": [{"access": "public", "name": "QPaintDevice"}]}], "inputFile": "qimage.h", "outputRevision": 69}, {"classes": [{"className": "QImageIOPlugin", "lineNumber": 101, "object": true, "qualifiedClassName": "QImageIOPlugin", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qimageiohandler.h", "outputRevision": 69}, {"classes": [{"className": "QInputControl", "lineNumber": 25, "object": true, "qualifiedClassName": "QInputControl", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qinputcontrol_p.h", "outputRevision": 69}, {"classes": [{"className": "QInputDevice", "enums": [{"alias": "DeviceType", "isClass": true, "isFlag": true, "name": "DeviceTypes", "values": ["Unknown", "Mouse", "TouchScreen", "TouchPad", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Airbrush", "Keyboard", "AllDevices"]}, {"alias": "Capability", "isClass": true, "isFlag": true, "name": "Capabilities", "values": ["None", "Position", "Area", "Pressure", "Velocity", "NormalizedPosition", "MouseEmulation", "PixelScroll", "<PERSON><PERSON>", "Hover", "Rotation", "XTilt", "YTilt", "TangentialPressure", "ZPosition", "All"]}], "lineNumber": 16, "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "type", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "DeviceType", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "capabilities", "notify": "capabilitiesChanged", "read": "capabilities", "required": false, "scriptable": true, "stored": true, "type": "Capabilities", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "systemId", "read": "systemId", "required": false, "scriptable": true, "stored": true, "type": "qint64", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "seatName", "read": "seatName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "availableVirtualGeometry", "notify": "availableVirtualGeometryChanged", "read": "availableVirtualGeometry", "required": false, "scriptable": true, "stored": true, "type": "QRect", "user": false}], "qualifiedClassName": "QInputDevice", "signals": [{"access": "public", "arguments": [{"name": "area", "type": "QRect"}], "index": 0, "name": "availableVirtualGeometryChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "capabilities", "type": "Capabilities"}], "index": 1, "name": "capabilitiesChanged", "returnType": "void", "revision": 1545}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qinputdevice.h", "outputRevision": 69}, {"classes": [{"className": "QInputDeviceManager", "lineNumber": 25, "object": true, "qualifiedClassName": "QInputDeviceManager", "signals": [{"access": "public", "arguments": [{"name": "type", "type": "QInputDeviceManager::DeviceType"}], "index": 0, "name": "deviceListChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pos", "type": "QPoint"}], "index": 1, "name": "cursorPositionChangeRequested", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qinputdevicemanager_p.h", "outputRevision": 69}, {"classes": [{"className": "QInputMethod", "enums": [{"isClass": false, "isFlag": false, "name": "Action", "values": ["Click", "ContextMenu"]}], "lineNumber": 18, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "cursor<PERSON><PERSON>tangle", "notify": "cursorRectangleChanged", "read": "cursor<PERSON><PERSON>tangle", "required": false, "scriptable": true, "stored": true, "type": "QRectF", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "anchorRectangle", "notify": "anchorRectangleChanged", "read": "anchorRectangle", "required": false, "scriptable": true, "stored": true, "type": "QRectF", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "keyboardRectangle", "notify": "keyboardRectangleChanged", "read": "keyboardRectangle", "required": false, "scriptable": true, "stored": true, "type": "QRectF", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "inputItemClipRectangle", "notify": "inputItemClipRectangleChanged", "read": "inputItemClipRectangle", "required": false, "scriptable": true, "stored": true, "type": "QRectF", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "visible", "notify": "visibleChanged", "read": "isVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "animating", "notify": "animatingChanged", "read": "isAnimating", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "locale", "notify": "localeChanged", "read": "locale", "required": false, "scriptable": true, "stored": true, "type": "QLocale", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "inputDirection", "notify": "inputDirectionChanged", "read": "inputDirection", "required": false, "scriptable": true, "stored": true, "type": "Qt::LayoutDirection", "user": false}], "qualifiedClassName": "QInputMethod", "signals": [{"access": "public", "index": 0, "name": "cursorRectangleChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "anchorRectangleChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "keyboardRectangleChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "inputItemClipRectangleChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "visibleChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "animatingChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "localeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "newDirection", "type": "Qt::LayoutDirection"}], "index": 7, "name": "inputDirectionChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 8, "name": "show", "returnType": "void"}, {"access": "public", "index": 9, "name": "hide", "returnType": "void"}, {"access": "public", "arguments": [{"name": "queries", "type": "Qt::InputMethodQueries"}], "index": 10, "name": "update", "returnType": "void"}, {"access": "public", "index": 11, "name": "reset", "returnType": "void"}, {"access": "public", "index": 12, "name": "commit", "returnType": "void"}, {"access": "public", "arguments": [{"name": "a", "type": "Action"}, {"name": "cursorPosition", "type": "int"}], "index": 13, "name": "invokeAction", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qinputmethod.h", "outputRevision": 69}, {"classes": [{"className": "QInternalMimeData", "lineNumber": 31, "object": true, "qualifiedClassName": "QInternalMimeData", "superClasses": [{"access": "public", "name": "QMimeData"}]}], "inputFile": "qinternalmimedata_p.h", "outputRevision": 69}, {"classes": [{"className": "QKeyMapper", "lineNumber": 28, "object": true, "qualifiedClassName": "QKeyMapper", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qkeymapper_p.h", "outputRevision": 69}, {"classes": [{"className": "QKeySequence", "enums": [{"isClass": false, "isFlag": false, "name": "StandardKey", "values": ["<PERSON><PERSON><PERSON>", "HelpContents", "WhatsThis", "Open", "Close", "Save", "New", "Delete", "Cut", "Copy", "Paste", "Undo", "Redo", "Back", "Forward", "Refresh", "ZoomIn", "ZoomOut", "Print", "AddTab", "NextChild", "<PERSON><PERSON><PERSON><PERSON>", "Find", "FindNext", "FindPrevious", "Replace", "SelectAll", "Bold", "Italic", "Underline", "MoveToNextChar", "MoveToPreviousChar", "MoveToNextWord", "MoveToPreviousWord", "MoveToNextLine", "MoveToPreviousLine", "MoveToNextPage", "MoveToPreviousPage", "MoveToStartOfLine", "MoveToEndOfLine", "MoveToStartOfBlock", "MoveToEndOfBlock", "MoveToStartOfDocument", "MoveToEndOfDocument", "SelectNextChar", "SelectPreviousChar", "SelectNextWord", "SelectPreviousWord", "SelectNextLine", "SelectPreviousLine", "SelectNextPage", "SelectPreviousPage", "SelectStartOfLine", "SelectEndOfLine", "SelectStartOfBlock", "SelectEndOfBlock", "SelectStartOfDocument", "SelectEndOfDocument", "DeleteStartOfWord", "DeleteEndOfWord", "DeleteEndOfLine", "InsertParagraphSeparator", "InsertLineSeparator", "SaveAs", "Preferences", "Quit", "FullScreen", "Deselect", "DeleteCompleteLine", "Backspace", "Cancel"]}, {"isClass": false, "isFlag": false, "name": "SequenceFormat", "values": ["NativeText", "PortableText"]}, {"isClass": false, "isFlag": false, "name": "SequenceMatch", "values": ["NoMatch", "PartialMatch", "ExactMatch"]}], "gadget": true, "lineNumber": 34, "qualifiedClassName": "QKeySequence"}], "inputFile": "qkeysequence.h", "outputRevision": 69}, {"classes": [{"className": "QLayoutPolicy", "enums": [{"alias": "PolicyFlag", "isClass": false, "isFlag": true, "name": "Policy", "values": ["GrowFlag", "ExpandFlag", "ShrinkFlag", "IgnoreFlag"]}], "gadget": true, "lineNumber": 31, "qualifiedClassName": "QLayoutPolicy"}], "inputFile": "qlayoutpolicy_p.h", "outputRevision": 69}, {"classes": [{"className": "QMovie", "enums": [{"isClass": false, "isFlag": false, "name": "MovieState", "values": ["NotRunning", "Paused", "Running"]}, {"isClass": false, "isFlag": false, "name": "CacheMode", "values": ["CacheNone", "CacheAll"]}], "lineNumber": 27, "object": true, "properties": [{"bindable": "bindableSpeed", "constant": false, "designable": true, "final": false, "index": 0, "name": "speed", "read": "speed", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSpeed"}, {"bindable": "bindableCacheMode", "constant": false, "designable": true, "final": false, "index": 1, "name": "cacheMode", "read": "cacheMode", "required": false, "scriptable": true, "stored": true, "type": "CacheMode", "user": false, "write": "setCacheMode"}], "qualifiedClassName": "QMovie", "signals": [{"access": "public", "index": 0, "name": "started", "returnType": "void"}, {"access": "public", "arguments": [{"name": "size", "type": "QSize"}], "index": 1, "name": "resized", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rect", "type": "QRect"}], "index": 2, "name": "updated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "QMovie::MovieState"}], "index": 3, "name": "stateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QImageReader::ImageReaderError"}], "index": 4, "name": "error", "returnType": "void"}, {"access": "public", "index": 5, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "frameNumber", "type": "int"}], "index": 6, "name": "frameChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 7, "name": "start", "returnType": "void"}, {"access": "public", "index": 8, "name": "jumpToNextFrame", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "paused", "type": "bool"}], "index": 9, "name": "setPaused", "returnType": "void"}, {"access": "public", "index": 10, "name": "stop", "returnType": "void"}, {"access": "public", "arguments": [{"name": "percentSpeed", "type": "int"}], "index": 11, "name": "setSpeed", "returnType": "void"}, {"access": "private", "index": 12, "name": "_q_loadNextFrame", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmovie.h", "outputRevision": 69}, {"classes": [{"className": "QOffscreenSurface", "lineNumber": 20, "object": true, "qualifiedClassName": "QOffscreenSurface", "signals": [{"access": "public", "arguments": [{"name": "screen", "type": "QScreen*"}], "index": 0, "name": "screenChanged", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "screen", "type": "QObject*"}], "index": 1, "name": "screenDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QSurface"}]}], "inputFile": "qoffscreensurface.h", "outputRevision": 69}, {"classes": [{"className": "QOpenGLContextGroup", "lineNumber": 42, "object": true, "qualifiedClassName": "QOpenGLContextGroup", "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QOpenGLContext", "lineNumber": 66, "object": true, "qualifiedClassName": "QOpenGLContext", "signals": [{"access": "public", "index": 0, "name": "aboutToBeDestroyed", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "object", "type": "QObject*"}], "index": 1, "name": "_q_screenDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qopenglcontext.h", "outputRevision": 69}, {"classes": [{"className": "QPaintDeviceWindow", "lineNumber": 16, "object": true, "qualifiedClassName": "QPaintDeviceWindow", "slots": [{"access": "public", "index": 0, "name": "update", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWindow"}, {"access": "public", "name": "QPaintDevice"}]}], "inputFile": "qpaintdevicewindow.h", "outputRevision": 69}, {"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON>", "enums": [{"isClass": false, "isFlag": false, "name": "RenderHint", "values": ["Antialiasing", "TextAntialiasing", "SmoothPixmapTransform", "VerticalSubpixelPositioning", "LosslessImageRendering", "NonCosmeticBrushPatterns"]}, {"alias": "RenderHint", "isClass": false, "isFlag": true, "name": "RenderHints", "values": ["Antialiasing", "TextAntialiasing", "SmoothPixmapTransform", "VerticalSubpixelPositioning", "LosslessImageRendering", "NonCosmeticBrushPatterns"]}], "gadget": true, "lineNumber": 45, "qualifiedClassName": "<PERSON><PERSON><PERSON><PERSON>"}], "inputFile": "qpainter.h", "outputRevision": 69}, {"classes": [{"className": "QPalette", "enums": [{"isClass": false, "isFlag": false, "name": "ColorGroup", "values": ["Active", "Disabled", "Inactive", "NColorGroups", "Current", "All", "Normal"]}, {"isClass": false, "isFlag": false, "name": "ColorRole", "values": ["WindowText", "<PERSON><PERSON>", "Light", "Midlight", "Dark", "Mid", "Text", "BrightText", "ButtonText", "Base", "Window", "Shadow", "Highlight", "HighlightedText", "Link", "LinkVisited", "AlternateBase", "NoRole", "ToolTipBase", "ToolTipText", "PlaceholderText", "Accent", "NColorRoles"]}], "gadget": true, "lineNumber": 18, "qualifiedClassName": "QPalette"}], "inputFile": "qpalette.h", "outputRevision": 69}, {"classes": [{"className": "QPdfWriter", "enums": [{"isClass": true, "isFlag": false, "name": "ColorModel", "values": ["RGB", "Grayscale", "CMYK", "Auto"]}], "lineNumber": 22, "object": true, "qualifiedClassName": "QPdfWriter", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QPagedPaintDevice"}]}], "inputFile": "qpdfwriter.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformBackingStoreRhiConfig", "enums": [{"isClass": false, "isFlag": false, "name": "Api", "values": ["OpenGL", "Metal", "Vulkan", "D3D11", "D3D12", "<PERSON><PERSON>"]}], "gadget": true, "lineNumber": 40, "qualifiedClassName": "QPlatformBackingStoreRhiConfig"}, {"className": "QPlatformTextureList", "lineNumber": 91, "object": true, "qualifiedClassName": "QPlatformTextureList", "signals": [{"access": "public", "arguments": [{"type": "bool"}], "index": 0, "name": "locked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformbackingstore.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformDialogHelper", "enums": [{"alias": "StandardButton", "isClass": false, "isFlag": true, "name": "StandardButtons", "values": ["NoButton", "Ok", "Save", "SaveAll", "Open", "Yes", "YesToAll", "No", "NoToAll", "Abort", "Retry", "Ignore", "Close", "Cancel", "Discard", "Help", "Apply", "Reset", "RestoreDefault<PERSON>", "FirstButton", "LastButton", "LowestBit", "HighestBit"]}, {"isClass": false, "isFlag": false, "name": "ButtonRole", "values": ["InvalidRole", "AcceptRole", "RejectRole", "DestructiveRole", "ActionRole", "HelpRole", "YesRole", "NoRole", "ResetRole", "ApplyRole", "NRoles", "RoleMask", "AlternateRole", "<PERSON><PERSON><PERSON>", "Reverse", "EOL"]}, {"isClass": false, "isFlag": false, "name": "ButtonLayout", "values": ["UnknownLayout", "WinLayout", "MacLayout", "KdeLayout", "GnomeLayout", "AndroidLayout"]}], "lineNumber": 44, "object": true, "qualifiedClassName": "QPlatformDialogHelper", "signals": [{"access": "public", "index": 0, "name": "accept", "returnType": "void"}, {"access": "public", "index": 1, "name": "reject", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QColorDialogOptions", "enums": [{"alias": "ColorDialogOption", "isClass": false, "isFlag": true, "name": "ColorDialogOptions", "values": ["ShowAlphaChannel", "NoButtons", "DontUseNativeDialog", "NoEyeDropperButton"]}], "gadget": true, "lineNumber": 149, "qualifiedClassName": "QColorDialogOptions"}, {"className": "QPlatformColorDialogHelper", "lineNumber": 191, "object": true, "qualifiedClassName": "QPlatformColorDialogHelper", "signals": [{"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 0, "name": "currentColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 1, "name": "colorSelected", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPlatformDialogHelper"}]}, {"className": "QFontDialogOptions", "enums": [{"alias": "FontDialogOption", "isClass": false, "isFlag": true, "name": "FontDialogOptions", "values": ["NoButtons", "DontUseNativeDialog", "ScalableFonts", "NonScalableFonts", "MonospacedFonts", "ProportionalFonts"]}], "gadget": true, "lineNumber": 209, "qualifiedClassName": "QFontDialogOptions"}, {"className": "QPlatformFontDialogHelper", "lineNumber": 245, "object": true, "qualifiedClassName": "QPlatformFontDialogHelper", "signals": [{"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "index": 0, "name": "currentFontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "index": 1, "name": "fontSelected", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPlatformDialogHelper"}]}, {"className": "QFileDialogOptions", "enums": [{"isClass": false, "isFlag": false, "name": "ViewMode", "values": ["Detail", "List"]}, {"isClass": false, "isFlag": false, "name": "FileMode", "values": ["AnyFile", "ExistingFile", "Directory", "ExistingFiles", "DirectoryOnly"]}, {"isClass": false, "isFlag": false, "name": "AcceptMode", "values": ["AcceptOpen", "AcceptSave"]}, {"isClass": false, "isFlag": false, "name": "DialogLabel", "values": ["LookIn", "FileName", "FileType", "Accept", "Reject", "DialogLabel<PERSON>ount"]}, {"alias": "FileDialogOption", "isClass": false, "isFlag": true, "name": "FileDialogOptions", "values": ["ShowDirsOnly", "DontResolveSymlinks", "DontConfirmOverwrite", "DontUseNativeDialog", "Read<PERSON>nly", "HideNameFilterDetails", "DontUseCustomDirectoryIcons"]}], "gadget": true, "lineNumber": 263, "qualifiedClassName": "QFileDialogOptions"}, {"className": "QPlatformFileDialogHelper", "lineNumber": 364, "object": true, "qualifiedClassName": "QPlatformFileDialogHelper", "signals": [{"access": "public", "arguments": [{"name": "file", "type": "QUrl"}], "index": 0, "name": "fileSelected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "files", "type": "QList<QUrl>"}], "index": 1, "name": "filesSelected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "path", "type": "QUrl"}], "index": 2, "name": "currentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "directory", "type": "QUrl"}], "index": 3, "name": "directoryEntered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "filter", "type": "QString"}], "index": 4, "name": "filterSelected", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPlatformDialogHelper"}]}, {"className": "QMessageDialogOptions", "enums": [{"alias": "Option", "isClass": true, "isFlag": true, "name": "Options", "values": ["DontUseNativeDialog"]}, {"isClass": false, "isFlag": false, "name": "StandardIcon", "values": ["NoIcon", "Information", "Warning", "Critical", "Question"]}], "gadget": true, "lineNumber": 398, "qualifiedClassName": "QMessageDialogOptions"}, {"className": "QPlatformMessageDialogHelper", "lineNumber": 482, "object": true, "qualifiedClassName": "QPlatformMessageDialogHelper", "signals": [{"access": "public", "arguments": [{"name": "button", "type": "QPlatformDialogHelper::StandardButton"}, {"name": "role", "type": "QPlatformDialogHelper::ButtonRole"}], "index": 0, "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "Qt::CheckState"}], "index": 1, "name": "checkBoxStateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPlatformDialogHelper"}]}], "inputFile": "qplatformdialoghelper.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformGraphicsBuffer", "enums": [{"isClass": false, "isFlag": false, "name": "AccessType", "values": ["None", "SWReadAccess", "SWWriteAccess", "TextureAccess", "HWCompositor"]}, {"isClass": false, "isFlag": false, "name": "Origin", "values": ["OriginBottomLeft", "OriginTopLeft"]}], "lineNumber": 26, "object": true, "qualifiedClassName": "QPlatformGraphicsBuffer", "signals": [{"access": "public", "arguments": [{"name": "previousAccessTypes", "type": "AccessTypes"}], "index": 0, "name": "unlocked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformgraphicsbuffer.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformInputContext", "lineNumber": 23, "object": true, "qualifiedClassName": "QPlatformInputContext", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatforminputcontext.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformInputContextPlugin", "lineNumber": 29, "object": true, "qualifiedClassName": "QPlatformInputContextPlugin", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatforminputcontextplugin_p.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformIntegrationPlugin", "lineNumber": 27, "object": true, "qualifiedClassName": "QPlatformIntegrationPlugin", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformintegrationplugin.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformMenuItem", "enums": [{"isClass": false, "isFlag": false, "name": "MenuRole", "values": ["NoRole", "TextHeuristicRole", "ApplicationSpecificRole", "AboutQtRole", "AboutRole", "PreferencesRole", "QuitRole", "CutRole", "CopyRole", "PasteRole", "SelectAllRole", "RoleCount"]}], "lineNumber": 28, "object": true, "qualifiedClassName": "QPlatformMenuItem", "signals": [{"access": "public", "index": 0, "name": "activated", "returnType": "void"}, {"access": "public", "index": 1, "name": "hovered", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QPlatformMenu", "enums": [{"isClass": false, "isFlag": false, "name": "MenuType", "values": ["DefaultMenu", "EditMenu"]}], "lineNumber": 71, "object": true, "qualifiedClassName": "QPlatformMenu", "signals": [{"access": "public", "index": 0, "name": "aboutToShow", "returnType": "void"}, {"access": "public", "index": 1, "name": "aboutToHide", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QPlatformMenuBar", "lineNumber": 120, "object": true, "qualifiedClassName": "QPlatformMenuBar", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformmenu.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformNativeInterface", "lineNumber": 30, "object": true, "qualifiedClassName": "QPlatformNativeInterface", "signals": [{"access": "public", "arguments": [{"name": "window", "type": "QPlatformWindow*"}, {"name": "propertyName", "type": "QString"}], "index": 0, "name": "windowPropertyChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformnativeinterface.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformServiceColorPicker", "lineNumber": 24, "object": true, "qualifiedClassName": "QPlatformServiceColorPicker", "signals": [{"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 0, "name": "colorPicked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformservices.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformSharedGraphicsCache", "lineNumber": 22, "object": true, "qualifiedClassName": "QPlatformSharedGraphicsCache", "signals": [{"access": "public", "arguments": [{"name": "cacheId", "type": "QByteArray"}, {"name": "itemIds", "type": "QList<quint32>"}], "index": 0, "name": "itemsMissing", "returnType": "void"}, {"access": "public", "arguments": [{"name": "cacheId", "type": "QByteArray"}, {"name": "bufferId", "type": "void*"}, {"name": "itemIds", "type": "QList<quint32>"}, {"name": "positions<PERSON>n<PERSON><PERSON>er", "type": "QList<QPoint>"}], "index": 1, "name": "itemsAvailable", "returnType": "void"}, {"access": "public", "arguments": [{"name": "cacheId", "type": "QByteArray"}, {"name": "itemIds", "type": "QList<quint32>"}], "index": 2, "name": "itemsInvalidated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "cacheId", "type": "QByteArray"}, {"name": "bufferId", "type": "void*"}, {"name": "itemIds", "type": "QList<quint32>"}, {"name": "positions<PERSON>n<PERSON><PERSON>er", "type": "QList<QPoint>"}], "index": 3, "name": "itemsUpdated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformsharedgraphicscache.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformSystemTrayIcon", "enums": [{"isClass": false, "isFlag": false, "name": "ActivationReason", "values": ["Unknown", "Context", "DoubleClick", "<PERSON><PERSON>", "MiddleClick"]}, {"isClass": false, "isFlag": false, "name": "MessageIcon", "values": ["NoIcon", "Information", "Warning", "Critical"]}], "lineNumber": 21, "object": true, "qualifiedClassName": "QPlatformSystemTrayIcon", "signals": [{"access": "public", "arguments": [{"name": "reason", "type": "QPlatformSystemTrayIcon::ActivationReason"}], "index": 0, "name": "activated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "globalPos", "type": "QPoint"}, {"name": "screen", "type": "const QPlatformScreen*"}], "index": 1, "name": "contextMenuRequested", "returnType": "void"}, {"access": "public", "index": 2, "name": "messageClicked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformsystemtrayicon.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformTheme", "enums": [{"isClass": false, "isFlag": false, "name": "ThemeHint", "values": ["CursorFlashTime", "KeyboardInputInterval", "MouseDoubleClickInterval", "StartDragDistance", "StartDragTime", "KeyboardAutoRepeatRate", "PasswordMaskDelay", "StartDragVelocity", "TextCursorWidth", "DropShadow", "MaximumScrollBarDragDistance", "ToolButtonStyle", "ToolBarIconSize", "ItemViewActivateItemOnSingleClick", "SystemIconThemeName", "SystemIconFallbackThemeName", "IconThemeSearchPaths", "StyleNames", "WindowAutoPlacement", "DialogButtonBoxLayout", "DialogButtonBoxButtonsHaveIcons", "UseFullScreenForPopupMenu", "KeyboardScheme", "UiEffects", "SpellCheckUnderlineStyle", "TabFocusBehavior", "IconPixmapSizes", "PasswordMaskCharacter", "DialogSnapToDefaultButton", "ContextMenuOnMouseRelease", "MousePressAndHoldInterval", "MouseDoubleClickDistance", "WheelScrollLines", "TouchDoubleTapDistance", "ShowShortcutsInContextMenus", "IconFallbackSearchPaths", "MouseQuickSelectionThreshold", "InteractiveResizeAcrossScreens", "ShowDirectoriesFirst", "PreselectFirstFileInDirectory", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SetFocusOnTouchRelease", "FlickStartDistance", "FlickMaximumVelocity", "FlickDeceleration", "MenuBarFocusOnAltPressRelease", "MouseCursorTheme", "MouseCursorSize", "UnderlineShortcut", "ShowIconsInMenus", "PreferFileIconFromTheme"]}, {"isClass": false, "isFlag": false, "name": "DialogType", "values": ["FileDialog", "ColorDialog", "FontDialog", "MessageDialog"]}, {"isClass": false, "isFlag": false, "name": "Palette", "values": ["SystemPalette", "ToolTipPalette", "ToolButtonPalette", "ButtonPalette", "CheckBoxPalette", "RadioButtonPalette", "<PERSON>er<PERSON><PERSON><PERSON>", "ComboBoxPalette", "ItemViewPalette", "MessageBoxLabelPelette", "MessageBoxLabelPalette", "TabBarPalette", "LabelPalette", "GroupBoxPalette", "MenuPalette", "MenuBarPalette", "TextEditPalette", "TextLineEditPalette", "NPalettes"]}, {"isClass": false, "isFlag": false, "name": "Font", "values": ["SystemFont", "MenuFont", "MenuBarFont", "MenuItemFont", "MessageBoxFont", "LabelFont", "TipLabelFont", "StatusBarFont", "TitleBarFont", "MdiSubWindowTitleFont", "DockWidgetTitleFont", "PushButtonFont", "CheckBoxFont", "RadioButtonFont", "ToolButtonFont", "ItemViewFont", "ListViewFont", "HeaderViewFont", "ListBoxFont", "ComboMenuItemFont", "ComboLineEditFont", "SmallFont", "MiniFont", "FixedFont", "GroupBoxTitleFont", "TabButtonFont", "EditorFont", "NFonts"]}, {"isClass": false, "isFlag": false, "name": "StandardPixmap", "values": ["TitleBarMenuButton", "TitleBarMinButton", "TitleBarMaxButton", "TitleBarCloseButton", "TitleBarNormalButton", "TitleBarShadeB<PERSON>on", "TitleBarUnshadeButton", "TitleBarContextHelpButton", "DockWidgetCloseButton", "MessageBoxInformation", "MessageBoxWarning", "MessageBoxCritical", "MessageBoxQuestion", "DesktopIcon", "TrashIcon", "ComputerIcon", "DriveFDIcon", "DriveHDIcon", "DriveCDIcon", "DriveDVDIcon", "DriveNetIcon", "DirOpenIcon", "DirClosedIcon", "DirLinkIcon", "DirLinkOpenIcon", "FileIcon", "FileLinkIcon", "ToolBarHorizontalExtensionButton", "ToolBarVerticalExtensionButton", "FileDialogStart", "FileDialogEnd", "FileDialogToParent", "FileDialogNewFolder", "FileDialogDetailedView", "FileDialogInfoView", "FileDialogContentsView", "FileDialogListView", "FileDialogBack", "DirIcon", "DialogOkButton", "DialogCancelButton", "DialogHelpButton", "DialogOpenButton", "DialogSaveButton", "DialogCloseButton", "DialogApplyButton", "DialogResetButton", "DialogDiscardButton", "DialogYesButton", "DialogNoButton", "ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight", "ArrowBack", "ArrowForward", "DirHomeIcon", "CommandLink", "VistaShield", "BrowserReload", "BrowserStop", "MediaPlay", "MediaStop", "MediaPause", "MediaSkipForward", "MediaSkipBackward", "MediaSeekForward", "MediaSeekBackward", "MediaVolume", "MediaVolumeMuted", "LineEditClearButton", "DialogYesToAllButton", "DialogNoToAllButton", "DialogSaveAllButton", "DialogAbortButton", "DialogRetryButton", "DialogIgnoreButton", "RestoreDefaultsButton", "TabCloseButton", "NStandardPixmap", "CustomBase"]}, {"isClass": false, "isFlag": false, "name": "KeyboardSchemes", "values": ["WindowsKeyboardScheme", "MacKeyboardScheme", "X11KeyboardScheme", "KdeKeyboardScheme", "GnomeKeyboardScheme", "CdeKeyboardScheme"]}, {"isClass": false, "isFlag": false, "name": "UiEffect", "values": ["GeneralUiEffect", "AnimateMenuUiEffect", "FadeMenuUiEffect", "AnimateComboUiEffect", "AnimateTooltipUiEffect", "FadeTooltipUiEffect", "AnimateToolBoxUiEffect", "HoverEffect"]}], "gadget": true, "lineNumber": 42, "qualifiedClassName": "QPlatformTheme"}], "inputFile": "qplatformtheme.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformThemePlugin", "lineNumber": 26, "object": true, "qualifiedClassName": "QPlatformThemePlugin", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformthemeplugin.h", "outputRevision": 69}, {"classes": [{"className": "QPointingDeviceUniqueId", "gadget": true, "lineNumber": 20, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "numericId", "read": "numericId", "required": false, "scriptable": true, "stored": true, "type": "qint64", "user": false}], "qualifiedClassName": "QPointingDeviceUniqueId"}, {"className": "QPointingDevice", "enums": [{"alias": "PointerType", "isClass": true, "isFlag": true, "name": "PointerTypes", "values": ["Unknown", "Generic", "Finger", "Pen", "Eraser", "<PERSON><PERSON><PERSON>", "AllPointerTypes"]}, {"isClass": false, "isFlag": false, "name": "GrabTransition", "values": ["GrabPassive", "UngrabPassive", "CancelGrabPassive", "OverrideGrabPassive", "GrabExclusive", "UngrabExclusive", "CancelGrabExclusive"]}], "lineNumber": 50, "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "pointerType", "read": "pointerType", "required": false, "scriptable": true, "stored": true, "type": "PointerType", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "maximumPoints", "read": "maximumPoints", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "buttonCount", "read": "buttonCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "uniqueId", "read": "uniqueId", "required": false, "scriptable": true, "stored": true, "type": "QPointingDeviceUniqueId", "user": false}], "qualifiedClassName": "QPointingDevice", "signals": [{"access": "public", "arguments": [{"name": "grabber", "type": "QObject*"}, {"name": "transition", "type": "GrabTransition"}, {"name": "event", "type": "const QPointerEvent*"}, {"name": "point", "type": "QEventPoint"}], "index": 0, "isConst": true, "name": "grabChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QInputDevice"}]}], "inputFile": "qpointingdevice.h", "outputRevision": 69}, {"classes": [{"className": "QRasterWindow", "lineNumber": 14, "object": true, "qualifiedClassName": "QRasterWindow", "superClasses": [{"access": "public", "name": "QPaintDeviceWindow"}]}], "inputFile": "qrasterwindow.h", "outputRevision": 69}, {"classes": [{"className": "QScreen", "lineNumber": 31, "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "manufacturer", "read": "manufacturer", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "model", "read": "model", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "serialNumber", "read": "serialNumber", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "depth", "read": "depth", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "size", "notify": "geometryChanged", "read": "size", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "availableSize", "notify": "availableGeometryChanged", "read": "availableSize", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "virtualSize", "notify": "virtualGeometryChanged", "read": "virtualSize", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "availableVirtualSize", "notify": "virtualGeometryChanged", "read": "availableVirtualSize", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "geometry", "notify": "geometryChanged", "read": "geometry", "required": false, "scriptable": true, "stored": true, "type": "QRect", "user": false}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "availableGeometry", "notify": "availableGeometryChanged", "read": "availableGeometry", "required": false, "scriptable": true, "stored": true, "type": "QRect", "user": false}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "virtualGeometry", "notify": "virtualGeometryChanged", "read": "virtualGeometry", "required": false, "scriptable": true, "stored": true, "type": "QRect", "user": false}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "availableVirtualGeometry", "notify": "virtualGeometryChanged", "read": "availableVirtualGeometry", "required": false, "scriptable": true, "stored": true, "type": "QRect", "user": false}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "physicalSize", "notify": "physicalSizeChanged", "read": "physicalSize", "required": false, "scriptable": true, "stored": true, "type": "QSizeF", "user": false}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "physicalDotsPerInchX", "notify": "physicalDotsPerInchChanged", "read": "physicalDotsPerInchX", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "physicalDotsPerInchY", "notify": "physicalDotsPerInchChanged", "read": "physicalDotsPerInchY", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "physicalDotsPerInch", "notify": "physicalDotsPerInchChanged", "read": "physicalDotsPerInch", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "logicalDotsPerInchX", "notify": "logicalDotsPerInchChanged", "read": "logicalDotsPerInchX", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "logicalDotsPerInchY", "notify": "logicalDotsPerInchChanged", "read": "logicalDotsPerInchY", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "logicalDotsPerInch", "notify": "logicalDotsPerInchChanged", "read": "logicalDotsPerInch", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 20, "name": "devicePixelRatio", "notify": "physicalDotsPerInchChanged", "read": "devicePixelRatio", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 21, "name": "primaryOrientation", "notify": "primaryOrientationChanged", "read": "primaryOrientation", "required": false, "scriptable": true, "stored": true, "type": "Qt::ScreenOrientation", "user": false}, {"constant": false, "designable": true, "final": false, "index": 22, "name": "orientation", "notify": "orientationChanged", "read": "orientation", "required": false, "scriptable": true, "stored": true, "type": "Qt::ScreenOrientation", "user": false}, {"constant": false, "designable": true, "final": false, "index": 23, "name": "nativeOrientation", "read": "nativeOrientation", "required": false, "scriptable": true, "stored": true, "type": "Qt::ScreenOrientation", "user": false}, {"constant": false, "designable": true, "final": false, "index": 24, "name": "refreshRate", "notify": "refreshRateChanged", "read": "refreshRate", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QScreen", "signals": [{"access": "public", "arguments": [{"name": "geometry", "type": "QRect"}], "index": 0, "name": "geometryChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "geometry", "type": "QRect"}], "index": 1, "name": "availableGeometryChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "size", "type": "QSizeF"}], "index": 2, "name": "physicalSizeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "dpi", "type": "qreal"}], "index": 3, "name": "physicalDotsPerInchChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "dpi", "type": "qreal"}], "index": 4, "name": "logicalDotsPerInchChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rect", "type": "QRect"}], "index": 5, "name": "virtualGeometryChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "orientation", "type": "Qt::ScreenOrientation"}], "index": 6, "name": "primaryOrientationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "orientation", "type": "Qt::ScreenOrientation"}], "index": 7, "name": "orientationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "refreshRate", "type": "qreal"}], "index": 8, "name": "refreshRateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qscreen.h", "outputRevision": 69}, {"classes": [{"className": "QSessionManager", "lineNumber": 22, "object": true, "qualifiedClassName": "QSessionManager", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qsessionmanager.h", "outputRevision": 69}, {"classes": [{"className": "QShapedPixmapWindow", "lineNumber": 26, "object": true, "qualifiedClassName": "QShapedPixmapWindow", "superClasses": [{"access": "public", "name": "QRasterWindow"}]}], "inputFile": "qshapedpixmapdndwindow_p.h", "outputRevision": 69}, {"classes": [{"className": "QShortcut", "lineNumber": 18, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "key", "read": "key", "required": false, "scriptable": true, "stored": true, "type": "QKeySequence", "user": false, "write": "<PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "enabled", "read": "isEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setEnabled"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "autoRepeat", "read": "autoRepeat", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoRepeat"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "context", "read": "context", "required": false, "scriptable": true, "stored": true, "type": "Qt::ShortcutContext", "user": false, "write": "setContext"}], "qualifiedClassName": "QShortcut", "signals": [{"access": "public", "index": 0, "name": "activated", "returnType": "void"}, {"access": "public", "index": 1, "name": "activatedAmbiguously", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qshortcut.h", "outputRevision": 69}, {"classes": [{"className": "QStandardItemModel", "lineNumber": 278, "object": true, "properties": [{"bindable": "bindableSortRole", "constant": false, "designable": true, "final": false, "index": 0, "name": "sortRole", "read": "sortRole", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSortRole"}], "qualifiedClassName": "QStandardItemModel", "signals": [{"access": "public", "arguments": [{"name": "item", "type": "QStandardItem*"}], "index": 0, "name": "itemChanged", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}], "index": 1, "name": "_q_emitItemChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractItemModel"}]}], "inputFile": "qstandarditemmodel.h", "outputRevision": 69}, {"classes": [{"className": "QStyleHints", "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "cursorFlashTime", "notify": "cursorFlashTimeChanged", "read": "cursorFlashTime", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": true, "index": 1, "name": "fontSmoothingGamma", "read": "fontSmoothingGamma", "required": false, "scriptable": true, "stored": false, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": true, "index": 2, "name": "keyboardAutoRepeatRate", "read": "keyboardAutoRepeatRate", "required": false, "scriptable": true, "stored": false, "type": "int", "user": false}, {"constant": true, "designable": true, "final": true, "index": 3, "name": "keyboardAutoRepeatRateF", "read": "keyboardAutoRepeatRateF", "required": false, "scriptable": true, "stored": false, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "keyboardInputInterval", "notify": "keyboardInputIntervalChanged", "read": "keyboardInputInterval", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "mouseDoubleClickInterval", "notify": "mouseDoubleClickIntervalChanged", "read": "mouseDoubleClickInterval", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": true, "index": 6, "name": "mousePressAndHoldInterval", "notify": "mousePressAndHoldIntervalChanged", "read": "mousePressAndHoldInterval", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": true, "index": 7, "name": "passwordMaskCharacter", "read": "passwordMaskCharacter", "required": false, "scriptable": true, "stored": false, "type": "QChar", "user": false}, {"constant": true, "designable": true, "final": true, "index": 8, "name": "passwordMaskDelay", "read": "passwordMaskDelay", "required": false, "scriptable": true, "stored": false, "type": "int", "user": false}, {"constant": true, "designable": true, "final": true, "index": 9, "name": "setFocusOnTouchRelease", "read": "setFocusOnTouchRelease", "required": false, "scriptable": true, "stored": false, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": true, "index": 10, "name": "showIsFullScreen", "read": "showIsFullScreen", "required": false, "scriptable": true, "stored": false, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": true, "index": 11, "name": "showIsMaximized", "read": "showIsMaximized", "required": false, "scriptable": true, "stored": false, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": true, "index": 12, "name": "showShortcutsInContextMenus", "notify": "showShortcutsInContextMenusChanged", "read": "showShortcutsInContextMenus", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setShowShortcutsInContextMenus"}, {"constant": false, "designable": true, "final": true, "index": 13, "name": "contextMenuTrigger", "notify": "contextMenuTriggerChanged", "read": "contextMenuTrigger", "required": false, "scriptable": true, "stored": true, "type": "Qt::ContextMenuTrigger", "user": false, "write": "setContextMenuTrigger"}, {"constant": false, "designable": true, "final": true, "index": 14, "name": "startDragDistance", "notify": "startDragDistanceChanged", "read": "startDragDistance", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": true, "index": 15, "name": "startDragTime", "notify": "startDragTimeChanged", "read": "startDragTime", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": true, "index": 16, "name": "startDragVelocity", "read": "startDragVelocity", "required": false, "scriptable": true, "stored": false, "type": "int", "user": false}, {"constant": true, "designable": true, "final": true, "index": 17, "name": "useRtlExtensions", "read": "useRtlExtensions", "required": false, "scriptable": true, "stored": false, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": true, "index": 18, "name": "tabFocusBehavior", "notify": "tabFocusBehaviorChanged", "read": "tabFocusBehavior", "required": false, "scriptable": true, "stored": true, "type": "Qt::TabFocusBehavior", "user": false}, {"constant": true, "designable": true, "final": true, "index": 19, "name": "singleClickActivation", "read": "singleClickActivation", "required": false, "scriptable": true, "stored": false, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": true, "index": 20, "name": "useHoverEffects", "notify": "useHoverEffectsChanged", "read": "useHoverEffects", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setUseHoverEffects"}, {"constant": false, "designable": true, "final": true, "index": 21, "name": "wheelScrollLines", "notify": "wheelScrollLinesChanged", "read": "wheelScrollLines", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": true, "index": 22, "name": "mouseQuickSelectionThreshold", "notify": "mouseQuickSelectionThresholdChanged", "read": "mouseQuickSelectionThreshold", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMouseQuickSelectionThreshold"}, {"constant": true, "designable": true, "final": true, "index": 23, "name": "mouseDoubleClickDistance", "read": "mouseDoubleClickDistance", "required": false, "scriptable": true, "stored": false, "type": "int", "user": false}, {"constant": true, "designable": true, "final": true, "index": 24, "name": "touchDoubleTapDistance", "read": "touchDoubleTapDistance", "required": false, "scriptable": true, "stored": false, "type": "int", "user": false}, {"constant": false, "designable": true, "final": true, "index": 25, "name": "colorScheme", "notify": "colorSchemeChanged", "read": "colorScheme", "required": false, "reset": "unsetColorScheme", "scriptable": true, "stored": true, "type": "Qt::ColorScheme", "user": false, "write": "setColorScheme"}], "qualifiedClassName": "QStyleHints", "signals": [{"access": "public", "arguments": [{"name": "cursorFlashTime", "type": "int"}], "index": 0, "name": "cursorFlashTimeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "keyboardInputInterval", "type": "int"}], "index": 1, "name": "keyboardInputIntervalChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mouseDoubleClickInterval", "type": "int"}], "index": 2, "name": "mouseDoubleClickIntervalChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mousePressAndHoldInterval", "type": "int"}], "index": 3, "name": "mousePressAndHoldIntervalChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startDragDistance", "type": "int"}], "index": 4, "name": "startDragDistanceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startDragTime", "type": "int"}], "index": 5, "name": "startDragTimeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "tabFocusBehavior", "type": "Qt::TabFocusBehavior"}], "index": 6, "name": "tabFocusBehaviorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "useHoverEffects", "type": "bool"}], "index": 7, "name": "useHoverEffectsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 8, "name": "showShortcutsInContextMenusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "contextMenuTrigger", "type": "Qt::ContextMenuTrigger"}], "index": 9, "name": "contextMenuTriggerChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "scrollLines", "type": "int"}], "index": 10, "name": "wheelScrollLinesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "threshold", "type": "int"}], "index": 11, "name": "mouseQuickSelectionThresholdChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "colorScheme", "type": "Qt::ColorScheme"}], "index": 12, "name": "colorSchemeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qstylehints.h", "outputRevision": 69}, {"classes": [{"className": "QSurface", "enums": [{"isClass": false, "isFlag": false, "name": "SurfaceClass", "values": ["Window", "Offscreen"]}, {"isClass": false, "isFlag": false, "name": "SurfaceType", "values": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "OpenGLSurface", "RasterG<PERSON>ur<PERSON>", "OpenVGSurface", "VulkanSurface", "MetalSurface", "Direct3DSurface"]}], "gadget": true, "lineNumber": 20, "qualifiedClassName": "QSurface"}], "inputFile": "qsurface.h", "outputRevision": 69}, {"classes": [{"className": "QSurfaceFormat", "enums": [{"isClass": false, "isFlag": false, "name": "FormatOption", "values": ["StereoBuffers", "DebugContext", "DeprecatedFunctions", "ResetNotification", "ProtectedContent"]}, {"isClass": false, "isFlag": false, "name": "Swap<PERSON><PERSON><PERSON><PERSON>", "values": ["DefaultSwapBehavior", "SingleBuffer", "<PERSON><PERSON><PERSON><PERSON>", "TripleBuffer"]}, {"isClass": false, "isFlag": false, "name": "RenderableType", "values": ["DefaultRenderableType", "OpenGL", "OpenGLES", "OpenVG"]}, {"isClass": false, "isFlag": false, "name": "OpenGLContextProfile", "values": ["NoProfile", "CoreProfile", "CompatibilityProfile"]}, {"isClass": false, "isFlag": false, "name": "ColorSpace", "values": ["DefaultColorSpace", "sRGBColorSpace"]}], "gadget": true, "lineNumber": 15, "qualifiedClassName": "QSurfaceFormat"}], "inputFile": "qsurfaceformat.h", "outputRevision": 69}, {"classes": [{"className": "QSyntaxHighlighter", "lineNumber": 24, "object": true, "qualifiedClassName": "QSyntaxHighlighter", "slots": [{"access": "public", "index": 0, "name": "rehighlight", "returnType": "void"}, {"access": "public", "arguments": [{"name": "block", "type": "QTextBlock"}], "index": 1, "name": "rehighlightBlock", "returnType": "void"}, {"access": "private", "arguments": [{"name": "from", "type": "int"}, {"name": "chars<PERSON><PERSON>oved", "type": "int"}, {"name": "charsAdded", "type": "int"}], "index": 2, "name": "_q_reformatBlocks", "returnType": "void"}, {"access": "private", "index": 3, "name": "_q_<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qsyntaxhighlighter.h", "outputRevision": 69}, {"classes": [{"className": "QTextDocument", "enums": [{"alias": "MarkdownFeature", "isClass": false, "isFlag": true, "name": "MarkdownFeatures", "values": ["MarkdownNoHTML", "MarkdownDialectCommonMark", "MarkdownDialectGitHub"]}, {"isClass": false, "isFlag": false, "name": "ResourceType", "values": ["UnknownResource", "HtmlResource", "ImageResource", "StyleSheetResource", "MarkdownResource", "UserResource"]}], "lineNumber": 55, "methods": [{"access": "protected", "arguments": [{"name": "type", "type": "int"}, {"name": "name", "type": "QUrl"}], "index": 15, "name": "loadResource", "returnType": "Q<PERSON><PERSON><PERSON>"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "undoRedoEnabled", "read": "isUndoRedoEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setUndoRedoEnabled"}, {"constant": false, "designable": false, "final": false, "index": 1, "name": "modified", "read": "isModified", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setModified"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "pageSize", "read": "pageSize", "required": false, "scriptable": true, "stored": true, "type": "QSizeF", "user": false, "write": "setPageSize"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "defaultFont", "read": "defaultFont", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setDefaultFont"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "useDesignMetrics", "read": "useDesignMetrics", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setUseDesignMetrics"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "layoutEnabled", "read": "isLayoutEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setLayoutEnabled"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "size", "read": "size", "required": false, "scriptable": true, "stored": true, "type": "QSizeF", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "textWidth", "read": "textWidth", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setTextWidth"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "blockCount", "read": "blockCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "indentWidth", "read": "indentWidth", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setIndentWidth"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "defaultStyleSheet", "read": "defaultStyleSheet", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setDefaultStyleSheet"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "maximumBlockCount", "read": "maximumBlockCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMaximumBlockCount"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "documentMargin", "read": "documentMargin", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setDocumentMargin"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "baseUrl", "notify": "baseUrlChanged", "read": "baseUrl", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setBaseUrl"}], "qualifiedClassName": "QTextDocument", "signals": [{"access": "public", "arguments": [{"name": "from", "type": "int"}, {"name": "chars<PERSON><PERSON>oved", "type": "int"}, {"name": "charsAdded", "type": "int"}], "index": 0, "name": "contentsChange", "returnType": "void"}, {"access": "public", "index": 1, "name": "contentsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 2, "name": "undoAvailable", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 3, "name": "redoAvailable", "returnType": "void"}, {"access": "public", "index": 4, "name": "undoCommandAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "m", "type": "bool"}], "index": 5, "name": "modificationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "cursor", "type": "QTextCursor"}], "index": 6, "name": "cursorPositionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "newBlockCount", "type": "int"}], "index": 7, "name": "blockCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "index": 8, "name": "baseUrlChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "documentLayoutChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 10, "name": "undo", "returnType": "void"}, {"access": "public", "index": 11, "name": "redo", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QAbstractUndoItem*"}], "index": 12, "name": "appendUndoItem", "returnType": "void"}, {"access": "public", "arguments": [{"name": "m", "type": "bool"}], "index": 13, "name": "setModified", "returnType": "void"}, {"access": "public", "index": 14, "isCloned": true, "name": "setModified", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtextdocument.h", "outputRevision": 69}, {"classes": [{"className": "QTextDocumentLayout", "lineNumber": 32, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "cursor<PERSON><PERSON><PERSON>", "read": "cursor<PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setCursorWidth"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "idealWidth", "read": "idealWidth", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "contentHasAlignment", "read": "contentHasAlignment", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QTextDocumentLayout", "superClasses": [{"access": "public", "name": "QAbstractTextDocumentLayout"}]}], "inputFile": "qtextdocumentlayout_p.h", "outputRevision": 69}, {"classes": [{"className": "QTextFormat", "enums": [{"isClass": false, "isFlag": false, "name": "FormatType", "values": ["InvalidFormat", "BlockFormat", "CharFormat", "ListFormat", "FrameFormat", "UserFormat"]}, {"isClass": false, "isFlag": false, "name": "Property", "values": ["ObjectIndex", "CssFloat", "LayoutDirection", "OutlinePen", "BackgroundBrush", "ForegroundBrush", "BackgroundImageUrl", "BlockAlignment", "BlockTopMargin", "BlockBottomMargin", "BlockLeftMargin", "BlockRightMargin", "TextIndent", "TabPositions", "BlockIndent", "LineHeight", "LineHeightType", "BlockNonBreakableLines", "BlockTrailingHorizontalRulerWidth", "HeadingLevel", "BlockQuoteLevel", "BlockCodeLanguage", "BlockCodeFence", "Block<PERSON>ark<PERSON>", "FirstFontProperty", "FontCapitalization", "FontLetterSpacing", "FontWordSpacing", "FontStyleHint", "FontStyleStrategy", "FontKerning", "FontHintingPreference", "FontFamilies", "FontStyleName", "FontLetterSpacingType", "FontStretch", "FontFamily", "FontPointSize", "FontSizeAdjustment", "FontSizeIncrement", "FontWeight", "FontItalic", "FontUnderline", "FontOverline", "FontStrikeOut", "FontFixedPitch", "FontPixelSize", "LastFontProperty", "TextUnderlineColor", "TextVerticalAlignment", "TextOutline", "TextUnderlineStyle", "TextToolTip", "TextSuperScriptBaseline", "TextSubScriptBaseline", "TextBaselineOffset", "IsAnchor", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "OldFontLetterSpacingType", "OldFontStretch", "OldTextUnderlineColor", "OldFontFamily", "ObjectType", "ListStyle", "ListIndent", "ListNumberPrefix", "ListNumberSuffix", "ListStart", "FrameBorder", "<PERSON>ame<PERSON><PERSON><PERSON>", "FramePadding", "Frame<PERSON><PERSON><PERSON>", "FrameHeight", "FrameTopMargin", "FrameBottomMargin", "FrameLeftMargin", "FrameRightMargin", "FrameBorderBrush", "FrameBorderStyle", "TableColumns", "TableColumnWidthConstraints", "TableCellSpacing", "TableCellPadding", "TableHeaderRowCount", "TableBorderCollapse", "TableCellRowSpan", "TableCellColumnSpan", "TableCellTopPadding", "TableCellBottomPadding", "TableCellLeftPadding", "TableCellRightPadding", "TableCellTopBorder", "TableCellBottomBorder", "TableCellLeftBorder", "TableCellRightBorder", "TableCellTopBorderStyle", "TableCellBottomBorderStyle", "TableCellLeftBorderStyle", "TableCellRightBorderStyle", "TableCellTopBorderBrush", "TableCellBottomBorderBrush", "TableCellLeftBorderBrush", "TableCellRightBorderBrush", "ImageName", "ImageTitle", "ImageAltText", "ImageWidth", "ImageHeight", "ImageQuality", "ImageMaxWidth", "FullWidthSelection", "PageBreakPolicy", "UserProperty"]}, {"isClass": false, "isFlag": false, "name": "ObjectTypes", "values": ["NoObject", "ImageObject", "TableObject", "TableCellObject", "UserObject"]}], "gadget": true, "lineNumber": 89, "qualifiedClassName": "QTextFormat"}], "inputFile": "qtextformat.h", "outputRevision": 69}, {"classes": [{"className": "QTextImageHandler", "interfaces": [[{"className": "QTextObjectInterface", "id": "\"org.qt-project.Qt.QTextObjectInterface\""}]], "lineNumber": 26, "object": true, "qualifiedClassName": "QTextImageHandler", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QTextObjectInterface"}]}], "inputFile": "qtextimagehandler_p.h", "outputRevision": 69}, {"classes": [{"className": "QTextList", "lineNumber": 17, "object": true, "qualifiedClassName": "QTextList", "superClasses": [{"access": "public", "name": "QTextBlockGroup"}]}], "inputFile": "qtextlist.h", "outputRevision": 69}, {"classes": [{"className": "QTextObject", "lineNumber": 24, "object": true, "qualifiedClassName": "QTextObject", "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QTextBlockGroup", "lineNumber": 52, "object": true, "qualifiedClassName": "QTextBlockGroup", "superClasses": [{"access": "public", "name": "QTextObject"}]}, {"className": "QTextFrame", "lineNumber": 80, "object": true, "qualifiedClassName": "QTextFrame", "superClasses": [{"access": "public", "name": "QTextObject"}]}], "inputFile": "qtextobject.h", "outputRevision": 69}, {"classes": [{"className": "QTextTable", "lineNumber": 62, "object": true, "qualifiedClassName": "QTextTable", "superClasses": [{"access": "public", "name": "QTextFrame"}]}], "inputFile": "qtexttable.h", "outputRevision": 69}, {"classes": [{"className": "QUndoGroup", "lineNumber": 19, "object": true, "qualifiedClassName": "QUndoGroup", "signals": [{"access": "public", "arguments": [{"name": "stack", "type": "QUndoStack*"}], "index": 0, "name": "activeStackChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "idx", "type": "int"}], "index": 1, "name": "indexChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "clean", "type": "bool"}], "index": 2, "name": "cleanChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "canUndo", "type": "bool"}], "index": 3, "name": "canUndoChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "canRedo", "type": "bool"}], "index": 4, "name": "canRedoChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "undoText", "type": "QString"}], "index": 5, "name": "undoTextChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "redoText", "type": "QString"}], "index": 6, "name": "redoTextChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 7, "name": "undo", "returnType": "void"}, {"access": "public", "index": 8, "name": "redo", "returnType": "void"}, {"access": "public", "arguments": [{"name": "stack", "type": "QUndoStack*"}], "index": 9, "name": "setActiveStack", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qundogroup.h", "outputRevision": 69}, {"classes": [{"className": "QUndoStack", "lineNumber": 51, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "active", "read": "isActive", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setActive"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "undoLimit", "read": "undoLimit", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setUndoLimit"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "canUndo", "notify": "canUndoChanged", "read": "canUndo", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "canRedo", "notify": "canRedoChanged", "read": "canRedo", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "undoText", "notify": "undoTextChanged", "read": "undoText", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "redoText", "notify": "redoTextChanged", "read": "redoText", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "clean", "notify": "cleanChanged", "read": "isClean", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QUndoStack", "signals": [{"access": "public", "arguments": [{"name": "idx", "type": "int"}], "index": 0, "name": "indexChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "clean", "type": "bool"}], "index": 1, "name": "cleanChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "canUndo", "type": "bool"}], "index": 2, "name": "canUndoChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "canRedo", "type": "bool"}], "index": 3, "name": "canRedoChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "undoText", "type": "QString"}], "index": 4, "name": "undoTextChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "redoText", "type": "QString"}], "index": 5, "name": "redoTextChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 6, "name": "setClean", "returnType": "void"}, {"access": "public", "index": 7, "name": "resetClean", "returnType": "void"}, {"access": "public", "arguments": [{"name": "idx", "type": "int"}], "index": 8, "name": "setIndex", "returnType": "void"}, {"access": "public", "index": 9, "name": "undo", "returnType": "void"}, {"access": "public", "index": 10, "name": "redo", "returnType": "void"}, {"access": "public", "arguments": [{"name": "active", "type": "bool"}], "index": 11, "name": "setActive", "returnType": "void"}, {"access": "public", "index": 12, "isCloned": true, "name": "setActive", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qundostack.h", "outputRevision": 69}, {"classes": [{"className": "QValidator", "enums": [{"isClass": false, "isFlag": false, "name": "State", "values": ["Invalid", "Intermediate", "Acceptable"]}], "lineNumber": 23, "object": true, "qualifiedClassName": "QValidator", "signals": [{"access": "public", "index": 0, "name": "changed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QIntValidator", "lineNumber": 55, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "bottom", "notify": "bottomChanged", "read": "bottom", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBottom"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "top", "notify": "topChanged", "read": "top", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTop"}], "qualifiedClassName": "QIntValidator", "signals": [{"access": "public", "arguments": [{"name": "bottom", "type": "int"}], "index": 0, "name": "bottomChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "top", "type": "int"}], "index": 1, "name": "topChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QValidator"}]}, {"className": "QDoubleValidator", "enums": [{"isClass": false, "isFlag": false, "name": "Notation", "values": ["StandardNotation", "ScientificNotation"]}], "lineNumber": 88, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "bottom", "notify": "bottomChanged", "read": "bottom", "required": false, "scriptable": true, "stored": true, "type": "double", "user": false, "write": "setBottom"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "top", "notify": "topChanged", "read": "top", "required": false, "scriptable": true, "stored": true, "type": "double", "user": false, "write": "setTop"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "decimals", "notify": "decimalsChanged", "read": "decimals", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setDecimals"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "notation", "notify": "notationChanged", "read": "notation", "required": false, "scriptable": true, "stored": true, "type": "Notation", "user": false, "write": "setNotation"}], "qualifiedClassName": "QDoubleValidator", "signals": [{"access": "public", "arguments": [{"name": "bottom", "type": "double"}], "index": 0, "name": "bottomChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "top", "type": "double"}], "index": 1, "name": "topChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "decimals", "type": "int"}], "index": 2, "name": "decimalsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "notation", "type": "QDoubleValidator::Notation"}], "index": 3, "name": "notationChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QValidator"}]}, {"className": "QRegularExpressionValidator", "lineNumber": 140, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "regularExpression", "notify": "regularExpressionChanged", "read": "regularExpression", "required": false, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setRegularExpression"}], "qualifiedClassName": "QRegularExpressionValidator", "signals": [{"access": "public", "arguments": [{"name": "re", "type": "QRegularExpression"}], "index": 0, "name": "regularExpressionChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "re", "type": "QRegularExpression"}], "index": 1, "name": "setRegularExpression", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QValidator"}]}], "inputFile": "qvalidator.h", "outputRevision": 69}, {"classes": [{"className": "QVulkanWindow", "lineNumber": 65, "object": true, "qualifiedClassName": "QVulkanWindow", "signals": [{"access": "public", "arguments": [{"name": "image", "type": "QImage"}], "index": 0, "name": "frameGrabbed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWindow"}]}], "inputFile": "qvulkanwindow.h", "outputRevision": 69}, {"classes": [{"className": "QWindow", "enums": [{"isClass": false, "isFlag": false, "name": "Visibility", "values": ["Hidden", "AutomaticVisibility", "Windowed", "Minimized", "Maximized", "FullScreen"]}, {"isClass": false, "isFlag": false, "name": "AncestorMode", "values": ["ExcludeTransients", "IncludeTransients"]}], "lineNumber": 62, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "title", "notify": "windowTitleChanged", "read": "title", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTitle"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "modality", "notify": "modalityChanged", "read": "modality", "required": false, "scriptable": true, "stored": true, "type": "Qt::WindowModality", "user": false, "write": "setModality"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "flags", "read": "flags", "required": false, "scriptable": true, "stored": true, "type": "Qt::WindowFlags", "user": false, "write": "setFlags"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "x", "notify": "xChanged", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setX"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "y", "notify": "y<PERSON><PERSON><PERSON>", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setY"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "width", "notify": "widthChanged", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "height", "notify": "heightChanged", "read": "height", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setHeight"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "minimumWidth", "notify": "minimumWidthChanged", "read": "minimumWidth", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMinimumWidth"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "minimumHeight", "notify": "minimumHeightChanged", "read": "minimumHeight", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMinimumHeight"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "maximumWidth", "notify": "maximumWidthChanged", "read": "maximumWidth", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMaximumWidth"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "maximumHeight", "notify": "maximumHeightChanged", "read": "maximumHeight", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMaximumHeight"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "visible", "notify": "visibleChanged", "read": "isVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "active", "notify": "activeChanged", "read": "isActive", "required": false, "revision": 513, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "visibility", "notify": "visibilityChanged", "read": "visibility", "required": false, "revision": 513, "scriptable": true, "stored": true, "type": "Visibility", "user": false, "write": "setVisibility"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "contentOrientation", "notify": "contentOrientationChanged", "read": "contentOrientation", "required": false, "scriptable": true, "stored": true, "type": "Qt::ScreenOrientation", "user": false, "write": "reportContentOrientationChange"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "opacity", "notify": "opacityChanged", "read": "opacity", "required": false, "revision": 513, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setOpacity"}, {"constant": false, "designable": true, "final": false, "index": 16, "member": "transientParent", "name": "transientParent", "notify": "transientParentChanged", "privateClass": "QWindow::d_func()", "required": false, "revision": 525, "scriptable": true, "stored": true, "type": "QWindow*", "user": false, "write": "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "QWindow", "signals": [{"access": "public", "arguments": [{"name": "screen", "type": "QScreen*"}], "index": 0, "name": "screenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "modality", "type": "Qt::WindowModality"}], "index": 1, "name": "modalityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "windowState", "type": "Qt::WindowState"}], "index": 2, "name": "windowStateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "title", "type": "QString"}], "index": 3, "name": "windowTitleChanged", "returnType": "void", "revision": 514}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 4, "name": "xChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 5, "name": "y<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 6, "name": "widthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 7, "name": "heightChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 8, "name": "minimumWidthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 9, "name": "minimumHeightChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 10, "name": "maximumWidthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 11, "name": "maximumHeightChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "<PERSON><PERSON><PERSON><PERSON>"}], "index": 12, "name": "safeAreaMarginsChanged", "returnType": "void", "revision": 1545}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 13, "name": "visibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visibility", "type": "QWindow::Visibility"}], "index": 14, "name": "visibilityChanged", "returnType": "void", "revision": 513}, {"access": "public", "index": 15, "name": "activeChanged", "returnType": "void", "revision": 513}, {"access": "public", "arguments": [{"name": "orientation", "type": "Qt::ScreenOrientation"}], "index": 16, "name": "contentOrientationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "object", "type": "QObject*"}], "index": 17, "name": "focusObjectChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "opacity", "type": "qreal"}], "index": 18, "name": "opacityChanged", "returnType": "void", "revision": 513}, {"access": "public", "arguments": [{"name": "transientParent", "type": "QWindow*"}], "index": 19, "name": "transientParentChanged", "returnType": "void", "revision": 525}], "slots": [{"access": "public", "index": 20, "name": "requestActivate", "returnType": "void", "revision": 513}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "index": 21, "name": "setVisible", "returnType": "void"}, {"access": "public", "index": 22, "name": "show", "returnType": "void"}, {"access": "public", "index": 23, "name": "hide", "returnType": "void"}, {"access": "public", "index": 24, "name": "showMinimized", "returnType": "void"}, {"access": "public", "index": 25, "name": "showMaximized", "returnType": "void"}, {"access": "public", "index": 26, "name": "showFullScreen", "returnType": "void"}, {"access": "public", "index": 27, "name": "showNormal", "returnType": "void"}, {"access": "public", "index": 28, "name": "close", "returnType": "bool"}, {"access": "public", "index": 29, "name": "raise", "returnType": "void"}, {"access": "public", "index": 30, "name": "lower", "returnType": "void"}, {"access": "public", "arguments": [{"name": "edges", "type": "Qt::<PERSON><PERSON>"}], "index": 31, "name": "startSystemResize", "returnType": "bool"}, {"access": "public", "index": 32, "name": "startSystemMove", "returnType": "bool"}, {"access": "public", "arguments": [{"type": "QString"}], "index": 33, "name": "setTitle", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 34, "name": "setX", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 35, "name": "setY", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 36, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 37, "name": "setHeight", "returnType": "void"}, {"access": "public", "arguments": [{"name": "posx", "type": "int"}, {"name": "posy", "type": "int"}, {"name": "w", "type": "int"}, {"name": "h", "type": "int"}], "index": 38, "name": "setGeometry", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rect", "type": "QRect"}], "index": 39, "name": "setGeometry", "returnType": "void"}, {"access": "public", "arguments": [{"name": "w", "type": "int"}], "index": 40, "name": "setMinimumWidth", "returnType": "void"}, {"access": "public", "arguments": [{"name": "h", "type": "int"}], "index": 41, "name": "setMinimumHeight", "returnType": "void"}, {"access": "public", "arguments": [{"name": "w", "type": "int"}], "index": 42, "name": "setMaximumWidth", "returnType": "void"}, {"access": "public", "arguments": [{"name": "h", "type": "int"}], "index": 43, "name": "setMaximumHeight", "returnType": "void"}, {"access": "public", "arguments": [{"name": "msec", "type": "int"}], "index": 44, "name": "alert", "returnType": "void", "revision": 513}, {"access": "public", "index": 45, "name": "requestUpdate", "returnType": "void", "revision": 515}, {"access": "private", "index": 46, "name": "_q_clear<PERSON><PERSON>t", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QSurface"}]}], "inputFile": "qwindow.h", "outputRevision": 69}, {"classes": [{"className": "QWindowsGuiEventDispatcher", "lineNumber": 23, "object": true, "qualifiedClassName": "QWindowsGuiEventDispatcher", "superClasses": [{"access": "public", "name": "QEventDispatcherWin32"}]}], "inputFile": "qwindowsguieventdispatcher_p.h", "outputRevision": 69}, {"classes": [{"className": "QPMCache", "lineNumber": 177, "object": true, "qualifiedClassName": "QPMCache", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QCache<QPixmapCache::Key,QPixmapCacheEntry>"}]}], "inputFile": "qpixmapcache.cpp", "outputRevision": 69}]