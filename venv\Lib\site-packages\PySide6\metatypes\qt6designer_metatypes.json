[{"classes": [{"className": "QtColorButton", "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "background<PERSON><PERSON><PERSON>ed", "read": "isBackgroundCheckered", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBackground<PERSON>he<PERSON>ed"}], "qualifiedClassName": "QtColorButton", "signals": [{"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 0, "name": "colorChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 1, "name": "setColor", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QToolButton"}]}], "inputFile": "qtcolorbutton_p.h", "outputRevision": 69}, {"classes": [{"className": "QtColorLine", "enums": [{"isClass": false, "isFlag": false, "name": "ColorComponent", "values": ["Red", "Green", "Blue", "<PERSON><PERSON>", "Saturation", "Value", "Alpha"]}], "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "color", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "indicatorSpace", "read": "indicatorSpace", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setIndicatorSpace"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "indicatorSize", "read": "indicatorSize", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setIndicatorSize"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "flip", "read": "flip", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFlip"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "background<PERSON><PERSON><PERSON>ed", "read": "isBackgroundCheckered", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBackground<PERSON>he<PERSON>ed"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "colorComponent", "read": "colorComponent", "required": false, "scriptable": true, "stored": true, "type": "ColorComponent", "user": false, "write": "setColorComponent"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "orientation", "read": "orientation", "required": false, "scriptable": true, "stored": true, "type": "Qt::Orientation", "user": false, "write": "setOrientation"}], "qualifiedClassName": "QtColorLine", "signals": [{"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 0, "name": "colorChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 1, "name": "setColor", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "qtcolorline_p.h", "outputRevision": 69}, {"classes": [{"className": "QtGradientDialog", "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "gradient", "read": "gradient", "required": false, "scriptable": true, "stored": true, "type": "QGradient", "user": false, "write": "setGradient"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "background<PERSON><PERSON><PERSON>ed", "read": "isBackgroundCheckered", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBackground<PERSON>he<PERSON>ed"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "detailsVisible", "read": "detailsVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setDetailsVisible"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "detailsButtonVisible", "read": "isDetailsButtonVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setDetailsButtonVisible"}], "qualifiedClassName": "QtGradientDialog", "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "qtgradientdialog_p.h", "outputRevision": 69}, {"classes": [{"className": "QtGradientEditor", "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "gradient", "read": "gradient", "required": false, "scriptable": true, "stored": true, "type": "QGradient", "user": false, "write": "setGradient"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "background<PERSON><PERSON><PERSON>ed", "read": "isBackgroundCheckered", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBackground<PERSON>he<PERSON>ed"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "detailsVisible", "read": "detailsVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setDetailsVisible"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "detailsButtonVisible", "read": "isDetailsButtonVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setDetailsButtonVisible"}], "qualifiedClassName": "QtGradientEditor", "signals": [{"access": "public", "arguments": [{"name": "gradient", "type": "QGradient"}], "index": 0, "name": "gradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "details", "type": "bool"}, {"name": "extenstionWidthHint", "type": "int"}], "index": 1, "name": "aboutToShowDetails", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "qtgradienteditor_p.h", "outputRevision": 69}, {"classes": [{"className": "QtGradientManager", "lineNumber": 31, "object": true, "qualifiedClassName": "QtGradientManager", "signals": [{"access": "public", "arguments": [{"name": "id", "type": "QString"}, {"name": "gradient", "type": "QGradient"}], "index": 0, "name": "gradientAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "QString"}, {"name": "newId", "type": "QString"}], "index": 1, "name": "gradientRenamed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "QString"}, {"name": "newGradient", "type": "QGradient"}], "index": 2, "name": "gradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "QString"}], "index": 3, "name": "gradientRemoved", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "id", "type": "QString"}, {"name": "gradient", "type": "QGradient"}], "index": 4, "name": "addGradient", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "id", "type": "QString"}, {"name": "newId", "type": "QString"}], "index": 5, "name": "renameGradient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "QString"}, {"name": "newGradient", "type": "QGradient"}], "index": 6, "name": "changeGradient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "QString"}], "index": 7, "name": "removeGradient", "returnType": "void"}, {"access": "public", "index": 8, "name": "clear", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtgradientmanager_p.h", "outputRevision": 69}, {"classes": [{"className": "QtGradientStopsController", "lineNumber": 26, "object": true, "qualifiedClassName": "QtGradientStopsController", "signals": [{"access": "public", "arguments": [{"name": "stops", "type": "QGradientStops"}], "index": 0, "name": "gradientStopsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtgradientstopscontroller_p.h", "outputRevision": 69}, {"classes": [{"className": "QtGradientStopsModel", "lineNumber": 43, "object": true, "qualifiedClassName": "QtGradientStopsModel", "signals": [{"access": "public", "arguments": [{"name": "stop", "type": "QtGradientStop*"}], "index": 0, "name": "stopAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "stop", "type": "QtGradientStop*"}], "index": 1, "name": "stopRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "stop", "type": "QtGradientStop*"}, {"name": "newPos", "type": "qreal"}], "index": 2, "name": "stopMoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "stop1", "type": "QtGradientStop*"}, {"name": "stop2", "type": "QtGradientStop*"}], "index": 3, "name": "stopsSwapped", "returnType": "void"}, {"access": "public", "arguments": [{"name": "stop", "type": "QtGradientStop*"}, {"name": "newColor", "type": "QColor"}], "index": 4, "name": "stopChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "stop", "type": "QtGradientStop*"}, {"name": "selected", "type": "bool"}], "index": 5, "name": "stopSelected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "stop", "type": "QtGradientStop*"}], "index": 6, "name": "currentStopChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtgradientstopsmodel_p.h", "outputRevision": 69}, {"classes": [{"className": "QtGradientStopsWidget", "lineNumber": 25, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "background<PERSON><PERSON><PERSON>ed", "read": "isBackgroundCheckered", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBackground<PERSON>he<PERSON>ed"}], "qualifiedClassName": "QtGradientStopsWidget", "signals": [{"access": "public", "arguments": [{"name": "zoom", "type": "double"}], "index": 0, "name": "zoomChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractScrollArea"}]}], "inputFile": "qtgradientstopswidget_p.h", "outputRevision": 69}, {"classes": [{"className": "QtGradientView", "lineNumber": 28, "object": true, "qualifiedClassName": "QtGradientView", "signals": [{"access": "public", "arguments": [{"name": "id", "type": "QString"}], "index": 0, "name": "currentGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "QString"}], "index": 1, "name": "gradientActivated", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "id", "type": "QString"}, {"name": "gradient", "type": "QGradient"}], "index": 2, "name": "slotGradientAdded", "returnType": "void"}, {"access": "private", "arguments": [{"name": "id", "type": "QString"}, {"name": "newId", "type": "QString"}], "index": 3, "name": "slotGradientRenamed", "returnType": "void"}, {"access": "private", "arguments": [{"name": "id", "type": "QString"}, {"name": "newGradient", "type": "QGradient"}], "index": 4, "name": "slotGradientChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "id", "type": "QString"}], "index": 5, "name": "slotGradientRemoved", "returnType": "void"}, {"access": "private", "index": 6, "name": "slotNewGradient", "returnType": "void"}, {"access": "private", "index": 7, "name": "slotEditGradient", "returnType": "void"}, {"access": "private", "index": 8, "name": "slotRemoveGradient", "returnType": "void"}, {"access": "private", "index": 9, "name": "slotRenameGradient", "returnType": "void"}, {"access": "private", "arguments": [{"name": "item", "type": "QListWidgetItem*"}], "index": 10, "name": "slotRenameGradientItem", "returnType": "void"}, {"access": "private", "arguments": [{"name": "item", "type": "QListWidgetItem*"}], "index": 11, "name": "slotCurrentItemChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "item", "type": "QListWidgetItem*"}], "index": 12, "name": "slotGradientActivated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "qtgradientview_p.h", "outputRevision": 69}, {"classes": [{"className": "QtGradientViewDialog", "lineNumber": 26, "object": true, "qualifiedClassName": "QtGradientViewDialog", "slots": [{"access": "private", "arguments": [{"name": "id", "type": "QString"}], "index": 0, "name": "slotGradientSelected", "returnType": "void"}, {"access": "private", "arguments": [{"name": "id", "type": "QString"}], "index": 1, "name": "slotGradientActivated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "qtgradientviewdialog_p.h", "outputRevision": 69}, {"classes": [{"className": "QtGradientWidget", "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "background<PERSON><PERSON><PERSON>ed", "read": "isBackgroundCheckered", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBackground<PERSON>he<PERSON>ed"}], "qualifiedClassName": "QtGradientWidget", "signals": [{"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "index": 0, "name": "startLinearChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "index": 1, "name": "endLinearChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "index": 2, "name": "centralRadialChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "index": 3, "name": "focalRadialChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "qreal"}], "index": 4, "name": "radiusRadialChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "index": 5, "name": "centralConicalChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "angle", "type": "qreal"}], "index": 6, "name": "angleConicalChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "stops", "type": "QGradientStops"}], "index": 7, "name": "setGradientStops", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "qtgradientwidget_p.h", "outputRevision": 69}, {"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON>", "lineNumber": 79, "object": true, "qualifiedClassName": "<PERSON><PERSON><PERSON><PERSON>", "signals": [{"access": "public", "index": 0, "name": "popupMenu", "returnType": "void"}, {"access": "public", "arguments": [{"name": "code", "type": "int"}, {"name": "text", "type": "QString"}, {"name": "autorep", "type": "bool"}], "index": 1, "name": "skinKeyPressEvent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "code", "type": "int"}, {"name": "text", "type": "QString"}, {"name": "autorep", "type": "bool"}], "index": 2, "name": "skinKeyReleaseEvent", "returnType": "void"}], "slots": [{"access": "protected", "index": 3, "name": "skinKeyRepeat", "returnType": "void"}, {"access": "protected", "index": 4, "name": "moveParent", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "deviceskin_p.h", "outputRevision": 69}, {"classes": [{"className": "ActionEditor", "lineNumber": 41, "object": true, "qualifiedClassName": "qdesigner_internal::ActionEditor", "signals": [{"access": "public", "arguments": [{"name": "item", "type": "QAction*"}, {"name": "column", "type": "int"}], "index": 0, "name": "itemActivated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "menu", "type": "QMenu*"}, {"name": "item", "type": "QAction*"}], "index": 1, "name": "contextMenuRequested", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "filter", "type": "QString"}], "index": 2, "name": "setFilter", "returnType": "void"}, {"access": "public", "index": 3, "name": "mainContainerChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "clearSelection", "returnType": "void"}, {"access": "public", "arguments": [{"name": "a", "type": "QAction*"}], "index": 5, "name": "selectAction", "returnType": "void"}, {"access": "private", "arguments": [{"name": "item", "type": "QAction*"}], "index": 6, "name": "slotCurrentItemChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "selected", "type": "QItemSelection"}, {"name": "deselected", "type": "QItemSelection"}], "index": 7, "name": "slotSelectionChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "item", "type": "QAction*"}, {"name": "column", "type": "int"}], "index": 8, "name": "editAction", "returnType": "void"}, {"access": "private", "arguments": [{"name": "item", "type": "QAction*"}], "index": 9, "isCloned": true, "name": "editAction", "returnType": "void"}, {"access": "private", "index": 10, "name": "editCurrentAction", "returnType": "void"}, {"access": "private", "index": 11, "name": "navigateToSlotCurrentAction", "returnType": "void"}, {"access": "private", "index": 12, "name": "slotActionChanged", "returnType": "void"}, {"access": "private", "index": 13, "name": "slotNewAction", "returnType": "void"}, {"access": "private", "index": 14, "name": "slotDelete", "returnType": "void"}, {"access": "private", "arguments": [{"name": "path", "type": "QString"}, {"name": "action", "type": "QAction*"}], "index": 15, "name": "resourceImageDropped", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QContextMenuEvent*"}, {"type": "QAction*"}], "index": 16, "name": "slotContextMenuRequested", "returnType": "void"}, {"access": "private", "arguments": [{"name": "a", "type": "QAction*"}], "index": 17, "name": "slotViewMode", "returnType": "void"}, {"access": "private", "arguments": [{"name": "w", "type": "QWidget*"}], "index": 18, "name": "slotSelectAssociatedWidget", "returnType": "void"}, {"access": "private", "index": 19, "name": "slotCopy", "returnType": "void"}, {"access": "private", "index": 20, "name": "slotCut", "returnType": "void"}, {"access": "private", "index": 21, "name": "slotPaste", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDesignerActionEditorInterface"}]}], "inputFile": "actioneditor_p.h", "outputRevision": 69}, {"classes": [{"className": "ActionModel", "lineNumber": 38, "object": true, "qualifiedClassName": "qdesigner_internal::ActionModel", "signals": [{"access": "public", "arguments": [{"name": "path", "type": "QString"}, {"name": "action", "type": "QAction*"}], "index": 0, "name": "resourceImageDropped", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QStandardItemModel"}]}, {"className": "ActionTreeView", "lineNumber": 90, "object": true, "qualifiedClassName": "qdesigner_internal::ActionTreeView", "signals": [{"access": "public", "arguments": [{"name": "event", "type": "QContextMenuEvent*"}, {"type": "QAction*"}], "index": 0, "name": "actionContextMenuRequested", "returnType": "void"}, {"access": "public", "arguments": [{"name": "action", "type": "QAction*"}], "index": 1, "name": "currentActionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "action", "type": "QAction*"}, {"name": "column", "type": "int"}], "index": 2, "name": "actionActivated", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 3, "name": "filter", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "current", "type": "QModelIndex"}, {"name": "previous", "type": "QModelIndex"}], "index": 4, "name": "currentChanged", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QModelIndex"}], "index": 5, "name": "slotActivated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QTreeView"}]}, {"className": "ActionListView", "lineNumber": 124, "object": true, "qualifiedClassName": "qdesigner_internal::ActionListView", "signals": [{"access": "public", "arguments": [{"name": "event", "type": "QContextMenuEvent*"}, {"type": "QAction*"}], "index": 0, "name": "actionContextMenuRequested", "returnType": "void"}, {"access": "public", "arguments": [{"name": "action", "type": "QAction*"}], "index": 1, "name": "currentActionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "action", "type": "QAction*"}], "index": 2, "name": "actionActivated", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 3, "name": "filter", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "current", "type": "QModelIndex"}, {"name": "previous", "type": "QModelIndex"}], "index": 4, "name": "currentChanged", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QModelIndex"}], "index": 5, "name": "slotActivated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QListView"}]}, {"className": "ActionView", "lineNumber": 161, "object": true, "qualifiedClassName": "qdesigner_internal::ActionView", "signals": [{"access": "public", "arguments": [{"name": "event", "type": "QContextMenuEvent*"}, {"type": "QAction*"}], "index": 0, "name": "contextMenuRequested", "returnType": "void"}, {"access": "public", "arguments": [{"name": "action", "type": "QAction*"}], "index": 1, "name": "currentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "action", "type": "QAction*"}, {"name": "column", "type": "int"}], "index": 2, "name": "activated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "selected", "type": "QItemSelection"}, {"name": "deselected", "type": "QItemSelection"}], "index": 3, "name": "selectionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "data", "type": "QString"}, {"name": "action", "type": "QAction*"}], "index": 4, "name": "resourceImageDropped", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 5, "name": "filter", "returnType": "void"}, {"access": "public", "index": 6, "name": "selectAll", "returnType": "void"}, {"access": "public", "index": 7, "name": "clearSelection", "returnType": "void"}, {"access": "public", "arguments": [{"name": "a", "type": "QAction*"}], "index": 8, "name": "selectAction", "returnType": "void"}, {"access": "private", "arguments": [{"name": "action", "type": "QAction*"}], "index": 9, "name": "slotCurrentChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QStackedWidget"}]}, {"className": "ActionRepositoryMimeData", "lineNumber": 208, "object": true, "qualifiedClassName": "qdesigner_internal::ActionRepositoryMimeData", "superClasses": [{"access": "public", "name": "QMimeData"}]}], "inputFile": "actionrepository_p.h", "outputRevision": 69}, {"classes": [{"className": "CodeDialog", "lineNumber": 30, "object": true, "qualifiedClassName": "qdesigner_internal::CodeDialog", "slots": [{"access": "private", "index": 0, "name": "slotSaveAs", "returnType": "void"}, {"access": "private", "index": 1, "name": "copyAll", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "codedialog_p.h", "outputRevision": 69}, {"classes": [{"className": "ConnectionEdit", "lineNumber": 127, "object": true, "qualifiedClassName": "qdesigner_internal::ConnectionEdit", "signals": [{"access": "public", "arguments": [{"name": "idx", "type": "int"}], "index": 0, "name": "aboutToAddConnection", "returnType": "void"}, {"access": "public", "arguments": [{"name": "con", "type": "qdesigner_internal::Connection*"}], "index": 1, "name": "connectionAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "con", "type": "qdesigner_internal::Connection*"}], "index": 2, "name": "aboutToRemoveConnection", "returnType": "void"}, {"access": "public", "arguments": [{"name": "idx", "type": "int"}], "index": 3, "name": "connectionRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "con", "type": "qdesigner_internal::Connection*"}], "index": 4, "name": "connectionSelected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "wgt", "type": "QWidget*"}], "index": 5, "name": "widgetActivated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "con", "type": "qdesigner_internal::Connection*"}], "index": 6, "name": "connectionChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 7, "name": "selectNone", "returnType": "void"}, {"access": "public", "index": 8, "name": "selectAll", "returnType": "void"}, {"access": "public", "index": 9, "name": "deleteSelected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "background", "type": "QWidget*"}], "index": 10, "name": "setBackground", "returnType": "void"}, {"access": "public", "index": 11, "name": "updateBackground", "returnType": "void"}, {"access": "public", "arguments": [{"name": "w", "type": "QWidget*"}], "index": 12, "name": "widgetRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "o", "type": "QObject*"}], "index": 13, "name": "objectRemoved", "returnType": "void"}, {"access": "public", "index": 14, "name": "updateLines", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "index": 15, "name": "enableUpdateBackground", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}, {"access": "public", "name": "CETypes"}]}], "inputFile": "connectionedit_p.h", "outputRevision": 69}, {"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lineNumber": 37, "object": true, "qualifiedClassName": "qdesigner_internal::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "superClasses": [{"access": "public", "name": "QSyntaxHighlighter"}]}], "inputFile": "csshighlighter_p.h", "outputRevision": 69}, {"classes": [{"className": "FormLayoutMenu", "lineNumber": 34, "object": true, "qualifiedClassName": "qdesigner_internal::FormLayoutMenu", "slots": [{"access": "private", "index": 0, "name": "slotAddRow", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "formlayoutmenu_p.h", "outputRevision": 69}, {"classes": [{"className": "FormWindowBase", "lineNumber": 42, "object": true, "qualifiedClassName": "qdesigner_internal::FormWindowBase", "slots": [{"access": "public", "arguments": [{"name": "resourceSet", "type": "QtResourceSet*"}, {"name": "resourceSetChanged", "type": "bool"}], "index": 0, "name": "resourceSetActivated", "returnType": "void"}, {"access": "private", "arguments": [{"name": "w", "type": "QWidget*"}], "index": 1, "name": "triggerDefaultAction", "returnType": "void"}, {"access": "private", "arguments": [{"name": "object", "type": "QObject*"}], "index": 2, "name": "sheetDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDesignerFormWindowInterface"}]}], "inputFile": "formwindowbase_p.h", "outputRevision": 69}, {"classes": [{"className": "GridPanel", "lineNumber": 32, "object": true, "qualifiedClassName": "qdesigner_internal::GridPanel", "slots": [{"access": "private", "index": 0, "name": "reset", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "gridpanel_p.h", "outputRevision": 69}, {"classes": [{"className": "Html<PERSON><PERSON><PERSON><PERSON>", "lineNumber": 27, "object": true, "qualifiedClassName": "qdesigner_internal::Html<PERSON><PERSON><PERSON><PERSON>", "superClasses": [{"access": "public", "name": "QSyntaxHighlighter"}]}], "inputFile": "htmlhighlighter_p.h", "outputRevision": 69}, {"classes": [{"className": "InvisibleWidget", "lineNumber": 26, "object": true, "qualifiedClassName": "qdesigner_internal::InvisibleWidget", "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "invisible_widget_p.h", "outputRevision": 69}, {"classes": [{"className": "Layout", "lineNumber": 35, "object": true, "qualifiedClassName": "qdesigner_internal::Layout", "slots": [{"access": "private", "index": 0, "name": "widgetD<PERSON>royed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "layout_p.h", "outputRevision": 69}, {"classes": [{"className": "MetaDataBase", "lineNumber": 63, "object": true, "qualifiedClassName": "qdesigner_internal::MetaDataBase", "slots": [{"access": "private", "arguments": [{"name": "object", "type": "QObject*"}], "index": 0, "name": "slotDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDesignerMetaDataBaseInterface"}]}], "inputFile": "metadatabase_p.h", "outputRevision": 69}, {"classes": [{"className": "MorphMenu", "lineNumber": 29, "object": true, "qualifiedClassName": "qdesigner_internal::MorphMenu", "slots": [{"access": "private", "arguments": [{"name": "newClassName", "type": "QString"}], "index": 0, "name": "slotMorph", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "morphmenu_p.h", "outputRevision": 69}, {"classes": [{"className": "NewActionDialog", "lineNumber": 60, "object": true, "qualifiedClassName": "qdesigner_internal::NewActionDialog", "slots": [{"access": "public", "index": 0, "name": "focusName", "returnType": "void"}, {"access": "public", "index": 1, "name": "focusText", "returnType": "void"}, {"access": "public", "index": 2, "name": "focusTooltip", "returnType": "void"}, {"access": "public", "index": 3, "name": "focusShortcut", "returnType": "void"}, {"access": "public", "index": 4, "name": "focusCheckable", "returnType": "void"}, {"access": "public", "index": 5, "name": "focusMenuRole", "returnType": "void"}, {"access": "private", "arguments": [{"name": "text", "type": "QString"}], "index": 6, "name": "onEditActionTextTextEdited", "returnType": "void"}, {"access": "private", "arguments": [{"name": "text", "type": "QString"}], "index": 7, "name": "onEditObjectNameTextEdited", "returnType": "void"}, {"access": "private", "index": 8, "name": "slotEditToolTip", "returnType": "void"}, {"access": "private", "index": 9, "name": "slotResetKeySequence", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "newactiondialog_p.h", "outputRevision": 69}, {"classes": [{"className": "NewFormWidget", "lineNumber": 43, "object": true, "qualifiedClassName": "qdesigner_internal::NewFormWidget", "slots": [{"access": "private", "arguments": [{"name": "item", "type": "QTreeWidgetItem*"}], "index": 0, "name": "treeWidgetItemActivated", "returnType": "void"}, {"access": "private", "arguments": [{"name": "current", "type": "QTreeWidgetItem*"}], "index": 1, "name": "treeWidgetCurrentItemChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "item", "type": "QTreeWidgetItem*"}], "index": 2, "name": "treeWidgetItemPressed", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 3, "name": "slotDeviceProfileIndexChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDesignerNewFormWidgetInterface"}]}], "inputFile": "newformwidget_p.h", "outputRevision": 69}, {"classes": [{"className": "OrderDialog", "lineNumber": 33, "object": true, "qualifiedClassName": "qdesigner_internal::OrderDialog", "slots": [{"access": "private", "index": 0, "name": "upButtonClicked", "returnType": "void"}, {"access": "private", "index": 1, "name": "downButtonClicked", "returnType": "void"}, {"access": "private", "arguments": [{"name": "row", "type": "int"}], "index": 2, "name": "pageListCurrentRowChanged", "returnType": "void"}, {"access": "private", "index": 3, "name": "slotEnableButtonsAfterDnD", "returnType": "void"}, {"access": "private", "index": 4, "name": "slotReset", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "orderdialog_p.h", "outputRevision": 69}, {"classes": [{"className": "PlainTextEditorDialog", "lineNumber": 28, "object": true, "qualifiedClassName": "qdesigner_internal::PlainTextEditorDialog", "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "plaintexteditor_p.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerPluginManager", "lineNumber": 73, "object": true, "qualifiedClassName": "QDesignerPluginManager", "slots": [{"access": "public", "index": 0, "name": "syncSettings", "returnType": "bool"}, {"access": "public", "index": 1, "name": "ensureInitialized", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "pluginmanager_p.h", "outputRevision": 69}, {"classes": [{"className": "PreviewConfigurationWidget", "lineNumber": 32, "object": true, "qualifiedClassName": "qdesigner_internal::PreviewConfigurationWidget", "slots": [{"access": "private", "index": 0, "name": "slotEditAppStyleSheet", "returnType": "void"}, {"access": "private", "index": 1, "name": "slotDeleteSkinEntry", "returnType": "void"}, {"access": "private", "arguments": [{"type": "int"}], "index": 2, "name": "slotSkinChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QGroupBox"}]}], "inputFile": "previewconfigurationwidget_p.h", "outputRevision": 69}, {"classes": [{"className": "PreviewManager", "lineNumber": 79, "object": true, "qualifiedClassName": "qdesigner_internal::PreviewManager", "signals": [{"access": "public", "index": 0, "name": "firstPreviewOpened", "returnType": "void"}, {"access": "public", "index": 1, "name": "lastPreviewClosed", "returnType": "void"}], "slots": [{"access": "public", "index": 2, "name": "closeAllPreviews", "returnType": "void"}, {"access": "private", "arguments": [{"type": "int"}], "index": 3, "name": "slotZoomChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "previewmanager_p.h", "outputRevision": 69}, {"classes": [{"className": "PromotionModel", "lineNumber": 30, "object": true, "qualifiedClassName": "qdesigner_internal::PromotionModel", "signals": [{"access": "public", "arguments": [{"type": "QDesignerWidgetDataBaseItemInterface*"}, {"name": "includeFile", "type": "QString"}], "index": 0, "name": "includeFileChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QDesignerWidgetDataBaseItemInterface*"}, {"name": "newName", "type": "QString"}], "index": 1, "name": "classNameChanged", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "item", "type": "QStandardItem*"}], "index": 2, "name": "slotItemChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QStandardItemModel"}]}], "inputFile": "promotionmodel_p.h", "outputRevision": 69}, {"classes": [{"className": "PromotionTaskMenu", "lineNumber": 37, "object": true, "qualifiedClassName": "qdesigner_internal::PromotionTaskMenu", "slots": [{"access": "private", "arguments": [{"name": "customClassName", "type": "QString"}], "index": 0, "name": "slotPromoteToCustomWidget", "returnType": "void"}, {"access": "private", "index": 1, "name": "slotDemoteFromCustomWidget", "returnType": "void"}, {"access": "private", "index": 2, "name": "slotEditPromotedWidgets", "returnType": "void"}, {"access": "private", "index": 3, "name": "slotEditPromoteTo", "returnType": "void"}, {"access": "private", "index": 4, "name": "slotEditSignalsSlots", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "promotiontaskmenu_p.h", "outputRevision": 69}, {"classes": [{"className": "PropertyLineEdit", "lineNumber": 27, "object": true, "qualifiedClassName": "qdesigner_internal::PropertyLineEdit", "slots": [{"access": "private", "index": 0, "name": "insertNewLine", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QLineEdit"}]}], "inputFile": "propertylineedit_p.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerMimeData", "lineNumber": 65, "object": true, "qualifiedClassName": "qdesigner_internal::QDesignerMimeData", "superClasses": [{"access": "public", "name": "QMimeData"}]}], "inputFile": "qdesigner_dnditem_p.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerDockWidget", "lineNumber": 28, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "dockWidgetArea", "read": "dockWidgetArea", "required": false, "scriptable": true, "stored": false, "type": "Qt::DockWidgetArea", "user": false, "write": "setDockWidgetArea"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "docked", "read": "docked", "required": false, "scriptable": true, "stored": false, "type": "bool", "user": false, "write": "setDocked"}], "qualifiedClassName": "QDesignerDockWidget", "superClasses": [{"access": "public", "name": "QDockWidget"}]}, {"className": "QDockWidgetPropertySheet", "lineNumber": 50, "object": true, "qualifiedClassName": "QDockWidgetPropertySheet", "superClasses": [{"access": "public", "name": "QDesignerPropertySheet"}]}], "inputFile": "qdesigner_dockwidget_p.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerFormWindowManager", "lineNumber": 30, "object": true, "qualifiedClassName": "qdesigner_internal::QDesignerFormWindowManager", "superClasses": [{"access": "public", "name": "QDesignerFormWindowManagerInterface"}]}], "inputFile": "qdesigner_formwindowmanager_p.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerMemberSheet", "interfaces": [[{"className": "QDesignerMemberSheetExtension", "id": "\"org.qt-project.Qt.Designer.MemberSheet\""}]], "lineNumber": 28, "object": true, "qualifiedClassName": "QDesignerMemberSheet", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerMemberSheetExtension"}]}, {"className": "QDesignerMemberSheetFactory", "interfaces": [[{"className": "QAbstractExtensionFactory", "id": "\"org.qt-project.Qt.QAbstractExtensionFactory\""}]], "lineNumber": 65, "object": true, "qualifiedClassName": "QDesignerMemberSheetFactory", "superClasses": [{"access": "public", "name": "QExtensionFactory"}]}], "inputFile": "qdesigner_membersheet_p.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerMenu", "lineNumber": 44, "object": true, "qualifiedClassName": "QDesignerMenu", "slots": [{"access": "private", "index": 0, "name": "slotAddSeparator", "returnType": "void"}, {"access": "private", "index": 1, "name": "slotRemoveSelectedAction", "returnType": "void"}, {"access": "private", "index": 2, "name": "slotShowSubMenuNow", "returnType": "void"}, {"access": "private", "index": 3, "name": "slotDeactivateNow", "returnType": "void"}, {"access": "private", "index": 4, "name": "slotAdjustSizeNow", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QMenu"}]}], "inputFile": "qdesigner_menu_p.h", "outputRevision": 69}, {"classes": [{"className": "SpecialMenuAction", "lineNumber": 38, "object": true, "qualifiedClassName": "qdesigner_internal::SpecialMenuAction", "superClasses": [{"access": "public", "name": "QAction"}]}, {"className": "QDesignerMenuBar", "lineNumber": 48, "object": true, "qualifiedClassName": "QDesignerMenuBar", "slots": [{"access": "private", "index": 0, "name": "deleteMenu", "returnType": "void"}, {"access": "private", "index": 1, "name": "slotRemoveMenuBar", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QMenuBar"}]}], "inputFile": "qdesigner_menubar_p.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerObjectInspector", "lineNumber": 46, "object": true, "qualifiedClassName": "qdesigner_internal::QDesignerObjectInspector", "slots": [{"access": "public", "index": 0, "name": "mainContainerChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDesignerObjectInspectorInterface"}]}], "inputFile": "qdesigner_objectinspector_p.h", "outputRevision": 69}, {"classes": [{"className": "NewPromotedClassPanel", "lineNumber": 42, "object": true, "qualifiedClassName": "qdesigner_internal::NewPromotedClassPanel", "signals": [{"access": "public", "arguments": [{"type": "PromotionParameters"}, {"name": "ok", "type": "bool*"}], "index": 0, "name": "newPromotedClass", "returnType": "void"}], "slots": [{"access": "public", "index": 1, "name": "grabFocus", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QString"}], "index": 2, "name": "chooseBaseClass", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QString"}], "index": 3, "name": "slotNameChanged", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QString"}], "index": 4, "name": "slotIncludeFileChanged", "returnType": "void"}, {"access": "private", "index": 5, "name": "slotAdd", "returnType": "void"}, {"access": "private", "index": 6, "name": "slotReset", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QGroupBox"}]}, {"className": "QDesignerPromotionDialog", "lineNumber": 82, "object": true, "qualifiedClassName": "qdesigner_internal::QDesignerPromotionDialog", "signals": [{"access": "public", "arguments": [{"type": "QString"}], "index": 0, "name": "selectedBaseClassChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 1, "name": "slotRemove", "returnType": "void"}, {"access": "private", "index": 2, "name": "slotAcceptPromoteTo", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QItemSelection"}, {"type": "QItemSelection"}], "index": 3, "name": "slotSelectionChanged", "returnType": "void"}, {"access": "private", "arguments": [{"type": "PromotionParameters"}, {"name": "ok", "type": "bool*"}], "index": 4, "name": "slotNewPromotedClass", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QDesignerWidgetDataBaseItemInterface*"}, {"name": "includeFile", "type": "QString"}], "index": 5, "name": "slotIncludeFileChanged", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QDesignerWidgetDataBaseItemInterface*"}, {"name": "newName", "type": "QString"}], "index": 6, "name": "slotClassNameChanged", "returnType": "void"}, {"access": "private", "index": 7, "name": "slotUpdateFromWidgetDatabase", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QPoint"}], "index": 8, "name": "slotTreeViewContextMenu", "returnType": "void"}, {"access": "private", "index": 9, "name": "slotEditSignalsSlots", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "qdesigner_promotiondialog_p.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerPropertyEditor", "lineNumber": 30, "object": true, "qualifiedClassName": "qdesigner_internal::QDesignerPropertyEditor", "signals": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}, {"name": "enableSubPropertyHandling", "type": "bool"}], "index": 0, "name": "propertyValueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 1, "name": "resetProperty", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 2, "name": "addDynamicProperty", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 3, "name": "removeDynamicProperty", "returnType": "void"}, {"access": "public", "index": 4, "name": "editor<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "index": 5, "name": "editorClosed", "returnType": "void"}], "slots": [{"access": "public", "index": 6, "name": "updatePropertySheet", "returnType": "void"}, {"access": "public", "index": 7, "name": "reloadResourceProperties", "returnType": "void"}, {"access": "private", "arguments": [{"name": "name", "type": "QString"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 8, "name": "slotPropertyChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDesignerPropertyEditorInterface"}]}], "inputFile": "qdesigner_propertyeditor_p.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerPropertySheet", "interfaces": [[{"className": "QDesignerPropertySheetExtension", "id": "\"org.qt-project.Qt.Designer.PropertySheet\""}], [{"className": "QDesignerDynamicPropertySheetExtension", "id": "\"org.qt-project.Qt.Designer.DynamicPropertySheet\""}]], "lineNumber": 42, "object": true, "qualifiedClassName": "QDesignerPropertySheet", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerPropertySheetExtension"}, {"access": "public", "name": "QDesignerDynamicPropertySheetExtension"}]}, {"className": "QDesignerAbstractPropertySheetFactory", "interfaces": [[{"className": "QAbstractExtensionFactory", "id": "\"org.qt-project.Qt.QAbstractExtensionFactory\""}]], "lineNumber": 173, "object": true, "qualifiedClassName": "QDesignerAbstractPropertySheetFactory", "slots": [{"access": "private", "arguments": [{"name": "object", "type": "QObject*"}], "index": 0, "name": "objectDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QExtensionFactory"}]}], "inputFile": "qdesigner_propertysheet_p.h", "outputRevision": 69}, {"classes": [{"className": "QStackedWidgetPreviewEventFilter", "lineNumber": 36, "object": true, "qualifiedClassName": "QStackedWidgetPreviewEventFilter", "slots": [{"access": "public", "index": 0, "name": "updateButtons", "returnType": "void"}, {"access": "public", "index": 1, "name": "prevPage", "returnType": "void"}, {"access": "public", "index": 2, "name": "nextPage", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QStackedWidgetEventFilter", "lineNumber": 70, "object": true, "qualifiedClassName": "QStackedWidgetEventFilter", "slots": [{"access": "private", "index": 0, "name": "removeCurrentPage", "returnType": "void"}, {"access": "private", "index": 1, "name": "addPage", "returnType": "void"}, {"access": "private", "index": 2, "name": "addPageAfter", "returnType": "void"}, {"access": "private", "index": 3, "name": "changeOrder", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QStackedWidgetPreviewEventFilter"}]}], "inputFile": "qdesigner_stackedbox_p.h", "outputRevision": 69}, {"classes": [{"className": "QTabWidgetEventFilter", "lineNumber": 38, "object": true, "qualifiedClassName": "QTabWidgetEventFilter", "slots": [{"access": "private", "index": 0, "name": "removeCurrentPage", "returnType": "void"}, {"access": "private", "index": 1, "name": "addPage", "returnType": "void"}, {"access": "private", "index": 2, "name": "addPageAfter", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdesigner_tabwidget_p.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerTaskMenu", "interfaces": [[{"className": "QDesignerTaskMenuExtension", "id": "\"org.qt-project.Qt.Designer.TaskMenu\""}]], "lineNumber": 36, "object": true, "qualifiedClassName": "qdesigner_internal::QDesignerTaskMenu", "slots": [{"access": "private", "index": 0, "name": "changeObjectName", "returnType": "void"}, {"access": "private", "index": 1, "name": "changeToolTip", "returnType": "void"}, {"access": "private", "index": 2, "name": "changeWhatsThis", "returnType": "void"}, {"access": "private", "index": 3, "name": "changeStyleSheet", "returnType": "void"}, {"access": "private", "index": 4, "name": "createMenuBar", "returnType": "void"}, {"access": "private", "arguments": [{"name": "area", "type": "Qt::ToolBarArea"}], "index": 5, "name": "addToolBar", "returnType": "void"}, {"access": "private", "index": 6, "name": "createStatusBar", "returnType": "void"}, {"access": "private", "index": 7, "name": "removeStatusBar", "returnType": "void"}, {"access": "private", "index": 8, "name": "containerFakeMethods", "returnType": "void"}, {"access": "private", "index": 9, "name": "slotNavigateToSlot", "returnType": "void"}, {"access": "private", "arguments": [{"name": "a", "type": "QAction*"}], "index": 10, "name": "applySize", "returnType": "void"}, {"access": "private", "index": 11, "name": "slotLayoutAlignment", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerTaskMenuExtension"}]}], "inputFile": "qdesigner_taskmenu_p.h", "outputRevision": 69}, {"classes": [{"className": "ToolBarEventFilter", "lineNumber": 42, "object": true, "qualifiedClassName": "qdesigner_internal::ToolBarEventFilter", "slots": [{"access": "private", "index": 0, "name": "slotRemoveSelectedAction", "returnType": "void"}, {"access": "private", "index": 1, "name": "slotRemoveToolBar", "returnType": "void"}, {"access": "private", "index": 2, "name": "slotInsertSeparator", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdesigner_toolbar_p.h", "outputRevision": 69}, {"classes": [{"className": "QToolBoxHelper", "lineNumber": 34, "object": true, "qualifiedClassName": "QToolBoxHelper", "slots": [{"access": "private", "index": 0, "name": "removeCurrentPage", "returnType": "void"}, {"access": "private", "index": 1, "name": "addPage", "returnType": "void"}, {"access": "private", "index": 2, "name": "addPageAfter", "returnType": "void"}, {"access": "private", "index": 3, "name": "changeOrder", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdesigner_toolbox_p.h", "outputRevision": 69}, {"classes": [{"className": "DesignerPixmapCache", "lineNumber": 296, "object": true, "qualifiedClassName": "qdesigner_internal::DesignerPixmapCache", "signals": [{"access": "public", "index": 0, "name": "reloaded", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "DesignerIconCache", "lineNumber": 310, "object": true, "qualifiedClassName": "qdesigner_internal::DesignerIconCache", "signals": [{"access": "public", "index": 0, "name": "reloaded", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdesigner_utils_p.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerWidget", "lineNumber": 30, "object": true, "qualifiedClassName": "QDesignerWidget", "superClasses": [{"access": "public", "name": "QWidget"}]}, {"className": "QDesignerDialog", "lineNumber": 51, "object": true, "qualifiedClassName": "QDesignerDialog", "superClasses": [{"access": "public", "name": "QDialog"}]}, {"className": "Line", "lineNumber": 67, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "orientation", "read": "orientation", "required": false, "scriptable": true, "stored": true, "type": "Qt::Orientation", "user": false, "write": "setOrientation"}], "qualifiedClassName": "Line", "superClasses": [{"access": "public", "name": "QFrame"}]}], "inputFile": "qdesigner_widget_p.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerWidgetBox", "lineNumber": 29, "object": true, "qualifiedClassName": "qdesigner_internal::QDesignerWidgetBox", "superClasses": [{"access": "public", "name": "QDesignerWidgetBoxInterface"}]}], "inputFile": "qdesigner_widgetbox_p.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerWidgetItem", "lineNumber": 44, "object": true, "qualifiedClassName": "qdesigner_internal::QDesignerWidgetItem", "slots": [{"access": "private", "index": 0, "name": "layoutChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QWidgetItemV2"}]}], "inputFile": "qdesigner_widgetitem_p.h", "outputRevision": 69}, {"classes": [{"className": "QLayoutSupport", "interfaces": [[{"className": "QDesignerLayoutDecorationExtension", "id": "\"org.qt-project.Qt.Designer.LayoutDecoration\""}]], "lineNumber": 139, "object": true, "qualifiedClassName": "qdesigner_internal::QLayoutSupport", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerLayoutDecorationExtension"}]}, {"className": "QLayoutWidget", "lineNumber": 223, "object": true, "qualifiedClassName": "QLayoutWidget", "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "qlayout_widget_p.h", "outputRevision": 69}, {"classes": [{"className": "RichTextEditorDialog", "lineNumber": 33, "object": true, "qualifiedClassName": "qdesigner_internal::RichTextEditorDialog", "slots": [{"access": "private", "arguments": [{"name": "newIndex", "type": "int"}], "index": 0, "name": "tabIndexChanged", "returnType": "void"}, {"access": "private", "index": 1, "name": "richTextChanged", "returnType": "void"}, {"access": "private", "index": 2, "name": "sourceChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "richtexteditor_p.h", "outputRevision": 69}, {"classes": [{"className": "SheetDelegate", "lineNumber": 30, "object": true, "qualifiedClassName": "qdesigner_internal::SheetDelegate", "superClasses": [{"access": "public", "name": "QStyledItemDelegate"}]}], "inputFile": "sheet_delegate_p.h", "outputRevision": 69}, {"classes": [{"className": "Spacer", "lineNumber": 28, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "spacerName", "read": "objectName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setObjectName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "orientation", "read": "orientation", "required": false, "scriptable": true, "stored": true, "type": "Qt::Orientation", "user": false, "write": "setOrientation"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "sizeType", "read": "sizeType", "required": false, "scriptable": true, "stored": true, "type": "QSizePolicy::Policy", "user": false, "write": "setSizeType"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "sizeHint", "read": "sizeHintProperty", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setSizeHintProperty"}], "qualifiedClassName": "Spacer", "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "spacer_widget_p.h", "outputRevision": 69}, {"classes": [{"className": "StyleSheetEditor", "lineNumber": 33, "object": true, "qualifiedClassName": "qdesigner_internal::StyleSheetEditor", "superClasses": [{"access": "public", "name": "QTextEdit"}]}, {"className": "StyleSheetEditorDialog", "lineNumber": 41, "object": true, "qualifiedClassName": "qdesigner_internal::StyleSheetEditorDialog", "slots": [{"access": "private", "index": 0, "name": "validateStyleSheet", "returnType": "void"}, {"access": "private", "arguments": [{"name": "pos", "type": "QPoint"}], "index": 1, "name": "slotContextMenuRequested", "returnType": "void"}, {"access": "private", "arguments": [{"name": "property", "type": "QString"}], "index": 2, "name": "slotAddResource", "returnType": "void"}, {"access": "private", "arguments": [{"name": "property", "type": "QString"}], "index": 3, "name": "slotAddGradient", "returnType": "void"}, {"access": "private", "arguments": [{"name": "property", "type": "QString"}], "index": 4, "name": "slotAddColor", "returnType": "void"}, {"access": "private", "index": 5, "name": "slotAddFont", "returnType": "void"}, {"access": "private", "index": 6, "name": "slotRequestHelp", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}]}, {"className": "StyleSheetPropertyEditorDialog", "lineNumber": 90, "object": true, "qualifiedClassName": "qdesigner_internal::StyleSheetPropertyEditorDialog", "slots": [{"access": "private", "index": 0, "name": "applyStyleSheet", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::StyleSheetEditorDialog", "name": "StyleSheetEditorDialog"}]}], "inputFile": "stylesheeteditor_p.h", "outputRevision": 69}, {"classes": [{"className": "TextPropertyEditor", "lineNumber": 32, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "text", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": true, "write": "setText"}], "qualifiedClassName": "qdesigner_internal::TextPropertyEditor", "signals": [{"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 0, "name": "textChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "editingFinished", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 2, "name": "setText", "returnType": "void"}, {"access": "public", "index": 3, "name": "selectAll", "returnType": "void"}, {"access": "public", "index": 4, "name": "clear", "returnType": "void"}, {"access": "private", "arguments": [{"name": "text", "type": "QString"}], "index": 5, "name": "slotTextChanged", "returnType": "void"}, {"access": "private", "index": 6, "name": "slotTextEdited", "returnType": "void"}, {"access": "private", "index": 7, "name": "slotEditingFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "textpropertyeditor_p.h", "outputRevision": 69}, {"classes": [{"className": "WidgetDataBase", "lineNumber": 123, "object": true, "qualifiedClassName": "qdesigner_internal::WidgetDataBase", "slots": [{"access": "public", "index": 0, "name": "loadPlugins", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDesignerWidgetDataBaseInterface"}]}], "inputFile": "widgetdatabase_p.h", "outputRevision": 69}, {"classes": [{"className": "WidgetFactory", "lineNumber": 43, "object": true, "qualifiedClassName": "qdesigner_internal::WidgetFactory", "slots": [{"access": "public", "index": 0, "name": "loadPlugins", "returnType": "void"}, {"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 1, "name": "activeFormWindowChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 2, "name": "formWindowAdded", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDesignerWidgetFactoryInterface"}]}], "inputFile": "widgetfactory_p.h", "outputRevision": 69}, {"classes": [{"className": "ZoomMenu", "lineNumber": 36, "object": true, "qualifiedClassName": "qdesigner_internal::ZoomMenu", "signals": [{"access": "public", "arguments": [{"type": "int"}], "index": 0, "name": "zoomChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "percent", "type": "int"}], "index": 1, "name": "setZoom", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QAction*"}], "index": 2, "name": "slotZoomMenu", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "ZoomView", "lineNumber": 66, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "zoom", "read": "zoom", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setZoom"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "zoomContextMenuEnabled", "read": "isZoomContextMenuEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setZoomContextMenuEnabled"}], "qualifiedClassName": "qdesigner_internal::ZoomView", "slots": [{"access": "public", "arguments": [{"name": "percent", "type": "int"}], "index": 0, "name": "setZoom", "returnType": "void"}, {"access": "public", "arguments": [{"name": "globalPos", "type": "QPoint"}], "index": 1, "name": "showContextMenu", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QGraphicsView"}]}, {"className": "ZoomWidget", "lineNumber": 134, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "widgetZoomContextMenuEnabled", "read": "isWidgetZoomContextMenuEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setWidgetZoomContextMenuEnabled"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "itemAcceptDrops", "read": "itemAcceptDrops", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setItemAcceptDrops"}], "qualifiedClassName": "qdesigner_internal::ZoomWidget", "slots": [{"access": "public", "index": 0, "isConst": true, "name": "dump", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::ZoomView", "name": "ZoomView"}]}], "inputFile": "zoomwidget_p.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerActionEditorInterface", "lineNumber": 16, "object": true, "qualifiedClassName": "QDesignerActionEditorInterface", "slots": [{"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 0, "name": "setFormWindow", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "abstractactioneditor.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerFormEditorInterface", "lineNumber": 39, "object": true, "qualifiedClassName": "QDesignerFormEditorInterface", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "abstractformeditor.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerFormWindowInterface", "enums": [{"isClass": false, "isFlag": false, "name": "ResourceFileSaveMode", "values": ["SaveAllResourceFiles", "SaveOnlyUsedResourceFiles", "DontSaveResourceFiles"]}], "lineNumber": 20, "object": true, "qualifiedClassName": "QDesignerFormWindowInterface", "signals": [{"access": "public", "arguments": [{"name": "mainContainer", "type": "QWidget*"}], "index": 0, "name": "mainContainerChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "toolIndex", "type": "int"}], "index": 1, "name": "toolChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fileName", "type": "QString"}], "index": 2, "name": "fileNameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "f", "type": "Feature"}], "index": 3, "name": "featureChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "selectionChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "geometryChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "resourceFilesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "widget", "type": "QWidget*"}], "index": 7, "name": "widgetManaged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "widget", "type": "QWidget*"}], "index": 8, "name": "widgetUnmanaged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "widget", "type": "QWidget*"}], "index": 9, "name": "aboutToUnmanageWidget", "returnType": "void"}, {"access": "public", "arguments": [{"name": "widget", "type": "QWidget*"}], "index": 10, "name": "activated", "returnType": "void"}, {"access": "public", "index": 11, "name": "changed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "w", "type": "QWidget*"}], "index": 12, "name": "widgetRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "o", "type": "QObject*"}], "index": 13, "name": "objectRemoved", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "widget", "type": "QWidget*"}], "index": 14, "name": "manageWidget", "returnType": "void"}, {"access": "public", "arguments": [{"name": "widget", "type": "QWidget*"}], "index": 15, "name": "unmanageWidget", "returnType": "void"}, {"access": "public", "arguments": [{"name": "f", "type": "Feature"}], "index": 16, "name": "setFeatures", "returnType": "void"}, {"access": "public", "arguments": [{"name": "dirty", "type": "bool"}], "index": 17, "name": "set<PERSON>irty", "returnType": "void"}, {"access": "public", "arguments": [{"name": "changePropertyDisplay", "type": "bool"}], "index": 18, "name": "clearSelection", "returnType": "void"}, {"access": "public", "index": 19, "isCloned": true, "name": "clearSelection", "returnType": "void"}, {"access": "public", "arguments": [{"name": "w", "type": "QWidget*"}, {"name": "select", "type": "bool"}], "index": 20, "name": "selectWidget", "returnType": "void"}, {"access": "public", "arguments": [{"name": "w", "type": "QWidget*"}], "index": 21, "isCloned": true, "name": "selectWidget", "returnType": "void"}, {"access": "public", "arguments": [{"name": "grid", "type": "QPoint"}], "index": 22, "name": "<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fileName", "type": "QString"}], "index": 23, "name": "setFileName", "returnType": "void"}, {"access": "public", "arguments": [{"name": "contents", "type": "QString"}], "index": 24, "name": "setContents", "returnType": "bool"}, {"access": "public", "index": 25, "name": "editWidgets", "returnType": "void"}, {"access": "public", "arguments": [{"name": "paths", "type": "QStringList"}, {"name": "errorCount", "type": "int*"}, {"name": "errorMessages", "type": "QString*"}], "index": 26, "name": "activateResourceFilePaths", "returnType": "void"}, {"access": "public", "arguments": [{"name": "paths", "type": "QStringList"}, {"name": "errorCount", "type": "int*"}], "index": 27, "isCloned": true, "name": "activateResourceFilePaths", "returnType": "void"}, {"access": "public", "arguments": [{"name": "paths", "type": "QStringList"}], "index": 28, "isCloned": true, "name": "activateResourceFilePaths", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "abstractformwindow.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerFormWindowManagerInterface", "enums": [{"isClass": false, "isFlag": false, "name": "Action", "values": ["CutAction", "CopyAction", "PasteAction", "DeleteAction", "SelectAllAction", "LowerAction", "RaiseAction", "UndoAction", "RedoAction", "HorizontalLayoutAction", "VerticalLayoutAction", "SplitHorizontalAction", "SplitVerticalAction", "GridLayoutAction", "FormLayoutAction", "BreakLayoutAction", "AdjustSizeAction", "SimplifyLayoutAction", "DefaultPreviewAction", "FormWindowSettingsDialogAction"]}, {"isClass": false, "isFlag": false, "name": "ActionGroup", "values": ["StyledPreviewActionGroup"]}], "lineNumber": 23, "object": true, "qualifiedClassName": "QDesignerFormWindowManagerInterface", "signals": [{"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 0, "name": "formWindowAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 1, "name": "formWindowRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 2, "name": "activeFormWindowChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fw", "type": "QDesignerFormWindowInterface*"}], "index": 3, "name": "formWindowSettingsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 4, "name": "addFormWindow", "returnType": "void"}, {"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 5, "name": "removeFormWindow", "returnType": "void"}, {"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 6, "name": "setActiveFormWindow", "returnType": "void"}, {"access": "public", "index": 7, "name": "showPreview", "returnType": "void"}, {"access": "public", "index": 8, "name": "closeAllPreviews", "returnType": "void"}, {"access": "public", "index": 9, "name": "showPluginDialog", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "abstractformwindowmanager.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerFormWindowToolInterface", "lineNumber": 19, "object": true, "qualifiedClassName": "QDesignerFormWindowToolInterface", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "abstractformwindowtool.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerIntegrationInterface", "enums": [{"isClass": false, "isFlag": false, "name": "ResourceFileWatcherBehaviour", "values": ["NoResourceFileWatcher", "ReloadResourceFileSilently", "PromptToReloadResourceFile"]}], "lineNumber": 28, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "headerSuffix", "read": "headerSuffix", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setHeaderSuffix"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "headerLowercase", "read": "isHeaderLowercase", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHeaderLowercase"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "qtVersion", "read": "qtVersion", "required": false, "scriptable": true, "stored": true, "type": "QVersionNumber", "user": false, "write": "setQtVersion"}], "qualifiedClassName": "QDesignerIntegrationInterface", "signals": [{"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}, {"name": "name", "type": "QString"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 0, "name": "propertyChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}, {"name": "object", "type": "QObject*"}, {"name": "newName", "type": "QString"}, {"name": "old<PERSON>ame", "type": "QString"}], "index": 1, "name": "objectNameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "manual", "type": "QString"}, {"name": "document", "type": "QString"}], "index": 2, "name": "helpRequested", "returnType": "void"}, {"access": "public", "arguments": [{"name": "objectName", "type": "QString"}, {"name": "signalSignature", "type": "QString"}, {"name": "parameterNames", "type": "QStringList"}], "index": 3, "name": "navigateToSlot", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slotSignature", "type": "QString"}], "index": 4, "name": "navigateToSlot", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "f", "type": "Feature"}], "index": 5, "name": "setFeatures", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}, {"name": "enableSubPropertyHandling", "type": "bool"}], "index": 6, "name": "updateProperty", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 7, "name": "updateProperty", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 8, "name": "resetProperty", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 9, "name": "addDynamicProperty", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 10, "name": "removeDynamicProperty", "returnType": "void"}, {"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 11, "name": "updateActiveFormWindow", "returnType": "void"}, {"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 12, "name": "setupFormWindow", "returnType": "void"}, {"access": "public", "index": 13, "name": "updateSelection", "returnType": "void"}, {"access": "public", "index": 14, "name": "updateCustomWidgetPlugins", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QDesignerIntegration", "lineNumber": 111, "object": true, "qualifiedClassName": "QDesignerIntegration", "superClasses": [{"access": "public", "name": "QDesignerIntegrationInterface"}]}], "inputFile": "abstractintegration.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerMetaDataBaseInterface", "lineNumber": 39, "object": true, "qualifiedClassName": "QDesignerMetaDataBaseInterface", "signals": [{"access": "public", "index": 0, "name": "changed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "abstractmetadatabase.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerNewFormWidgetInterface", "lineNumber": 15, "object": true, "qualifiedClassName": "QDesignerNewFormWidgetInterface", "signals": [{"access": "public", "index": 0, "name": "templateActivated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "templateSelected", "type": "bool"}], "index": 1, "name": "currentTemplateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "abstractnewformwidget.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerObjectInspectorInterface", "lineNumber": 16, "object": true, "qualifiedClassName": "QDesignerObjectInspectorInterface", "slots": [{"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 0, "name": "setFormWindow", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "abstractobjectinspector.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerPropertyEditorInterface", "lineNumber": 17, "object": true, "qualifiedClassName": "QDesignerPropertyEditorInterface", "signals": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 0, "name": "propertyChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "object", "type": "QObject*"}], "index": 1, "name": "setObject", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}, {"name": "changed", "type": "bool"}], "index": 2, "name": "setPropertyV<PERSON>ue", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 3, "isCloned": true, "name": "setPropertyV<PERSON>ue", "returnType": "void"}, {"access": "public", "arguments": [{"name": "readOnly", "type": "bool"}], "index": 4, "name": "setReadOnly", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "abstractpropertyeditor.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerResourceBrowserInterface", "lineNumber": 15, "object": true, "qualifiedClassName": "QDesignerResourceBrowserInterface", "signals": [{"access": "public", "arguments": [{"name": "filePath", "type": "QString"}], "index": 0, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "filePath", "type": "QString"}], "index": 1, "name": "pathActivated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "abstractresourcebrowser.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerWidgetBoxInterface", "lineNumber": 21, "object": true, "qualifiedClassName": "QDesignerWidgetBoxInterface", "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "abstractwidgetbox.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerWidgetDataBaseInterface", "lineNumber": 67, "object": true, "qualifiedClassName": "QDesignerWidgetDataBaseInterface", "signals": [{"access": "public", "index": 0, "name": "changed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "abstractwidgetdatabase.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerWidgetFactoryInterface", "lineNumber": 16, "object": true, "qualifiedClassName": "QDesignerWidgetFactoryInterface", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "abstractwidgetfactory.h", "outputRevision": 69}, {"classes": [{"className": "QExtensionFactory", "interfaces": [[{"className": "QAbstractExtensionFactory", "id": "\"org.qt-project.Qt.QAbstractExtensionFactory\""}]], "lineNumber": 18, "object": true, "qualifiedClassName": "QExtensionFactory", "slots": [{"access": "private", "arguments": [{"name": "object", "type": "QObject*"}], "index": 0, "name": "objectDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QAbstractExtensionFactory"}]}], "inputFile": "default_extensionfactory.h", "outputRevision": 69}, {"classes": [{"className": "QExtensionManager", "interfaces": [[{"className": "QAbstractExtensionManager", "id": "\"org.qt-project.Qt.QAbstractExtensionManager\""}]], "lineNumber": 15, "object": true, "qualifiedClassName": "QExtensionManager", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QAbstractExtensionManager"}]}], "inputFile": "qextensionmanager.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractFormBuilderGadget", "lineNumber": 47, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "itemFlags", "read": "fakeItemFlags", "required": false, "scriptable": true, "stored": true, "type": "Qt::<PERSON>emFlags", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "checkState", "read": "fakeCheckState", "required": false, "scriptable": true, "stored": true, "type": "Qt::CheckState", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "textAlignment", "read": "fakeAlignment", "required": false, "scriptable": true, "stored": true, "type": "Qt::Alignment", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "orientation", "read": "fakeOrientation", "required": false, "scriptable": true, "stored": true, "type": "Qt::Orientation", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "sizeType", "read": "fakeSizeType", "required": false, "scriptable": true, "stored": true, "type": "QSizePolicy::Policy", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "colorRole", "read": "fakeColorRole", "required": false, "scriptable": true, "stored": true, "type": "QPalette::ColorRole", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "colorGroup", "read": "fakeColorGroup", "required": false, "scriptable": true, "stored": true, "type": "QPalette::ColorGroup", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "styleStrategy", "read": "fakeStyleStrategy", "required": false, "scriptable": true, "stored": true, "type": "QFont::StyleStrategy", "user": false}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "hintingPreference", "read": "fakeHintingPreference", "required": false, "scriptable": true, "stored": true, "type": "QFont::HintingPreference", "user": false}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "fontWeight", "read": "fakeFontWeight", "required": false, "scriptable": true, "stored": true, "type": "QFont::Weight", "user": false}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "cursor<PERSON><PERSON>pe", "read": "fakeCursorShape", "required": false, "scriptable": true, "stored": true, "type": "Qt::CursorShape", "user": false}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "brushStyle", "read": "fakeBrushStyle", "required": false, "scriptable": true, "stored": true, "type": "Qt::BrushStyle", "user": false}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "toolBarArea", "read": "fakeToolBarArea", "required": false, "scriptable": true, "stored": true, "type": "Qt::ToolBarArea", "user": false}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "gradientType", "read": "fakeGradientType", "required": false, "scriptable": true, "stored": true, "type": "QGradient::Type", "user": false}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "gradientSpread", "read": "fakeGradientSpread", "required": false, "scriptable": true, "stored": true, "type": "QGradient::Spread", "user": false}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "gradientCoordinate", "read": "fakeGradientCoordinate", "required": false, "scriptable": true, "stored": true, "type": "QGradient::CoordinateMode", "user": false}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "language", "read": "fakeLanguage", "required": false, "scriptable": true, "stored": true, "type": "QLocale::Language", "user": false}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "country", "read": "fakeCountry", "required": false, "scriptable": true, "stored": true, "type": "QLocale::Country", "user": false}], "qualifiedClassName": "QAbstractFormBuilderGadget", "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "properties_p.h", "outputRevision": 69}, {"classes": [{"className": "QtBoolEdit", "lineNumber": 68, "object": true, "qualifiedClassName": "QtBoolEdit", "signals": [{"access": "public", "arguments": [{"type": "bool"}], "index": 0, "name": "toggled", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "qtpropertybrowserutils_p.h", "outputRevision": 69}, {"classes": [{"className": "AbstractFindWidget", "lineNumber": 32, "object": true, "qualifiedClassName": "AbstractFindWidget", "slots": [{"access": "public", "index": 0, "name": "activate", "returnType": "void"}, {"access": "public", "index": 1, "name": "deactivate", "returnType": "void"}, {"access": "public", "index": 2, "name": "findNext", "returnType": "void"}, {"access": "public", "index": 3, "name": "find<PERSON>revious", "returnType": "void"}, {"access": "public", "index": 4, "name": "findCurrentText", "returnType": "void"}, {"access": "private", "index": 5, "name": "updateButtons", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "abstractfindwidget_p.h", "outputRevision": 69}, {"classes": [{"className": "ItemViewFindWidget", "lineNumber": 26, "object": true, "qualifiedClassName": "ItemViewFindWidget", "superClasses": [{"access": "public", "name": "AbstractFindWidget"}]}], "inputFile": "itemviewfindwidget_p.h", "outputRevision": 69}, {"classes": [{"className": "TextEditFindWidget", "lineNumber": 24, "object": true, "qualifiedClassName": "TextEditFindWidget", "superClasses": [{"access": "public", "name": "AbstractFindWidget"}]}], "inputFile": "texteditfindwidget_p.h", "outputRevision": 69}, {"classes": [{"className": "FormLayoutRowDialog", "lineNumber": 61, "object": true, "qualifiedClassName": "qdesigner_internal::FormLayoutRowDialog", "slots": [{"access": "private", "arguments": [{"name": "text", "type": "QString"}], "index": 0, "name": "labelTextEdited", "returnType": "void"}, {"access": "private", "arguments": [{"name": "text", "type": "QString"}], "index": 1, "name": "labelNameEdited", "returnType": "void"}, {"access": "private", "arguments": [{"name": "text", "type": "QString"}], "index": 2, "name": "fieldNameEdited", "returnType": "void"}, {"access": "private", "index": 3, "name": "buddyClicked", "returnType": "void"}, {"access": "private", "arguments": [{"type": "int"}], "index": 4, "name": "fieldClassChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "formlayoutmenu.cpp", "outputRevision": 69}, {"classes": [{"className": "LanguageResourceDialog", "lineNumber": 43, "object": true, "qualifiedClassName": "qdesigner_internal::LanguageResourceDialog", "superClasses": [{"access": "public", "name": "QDialog"}]}, {"className": "IconSelector", "lineNumber": 64, "object": true, "qualifiedClassName": "qdesigner_internal::IconSelector", "signals": [{"access": "public", "arguments": [{"name": "icon", "type": "PropertySheetIconValue"}], "index": 0, "name": "iconChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}, {"className": "IconThemeEditor", "lineNumber": 95, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "theme", "read": "theme", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTheme"}], "qualifiedClassName": "qdesigner_internal::IconThemeEditor", "signals": [{"access": "public", "arguments": [{"type": "QString"}], "index": 0, "name": "edited", "returnType": "void"}], "slots": [{"access": "public", "index": 1, "name": "reset", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}, {"className": "IconThemeEnumEditor", "lineNumber": 118, "object": true, "qualifiedClassName": "qdesigner_internal::IconThemeEnumEditor", "signals": [{"access": "public", "arguments": [{"type": "int"}], "index": 0, "name": "edited", "returnType": "void"}], "slots": [{"access": "public", "index": 1, "name": "reset", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "iconselector_p.h", "outputRevision": 69}, {"classes": [{"className": "PluginDialog", "lineNumber": 28, "object": true, "qualifiedClassName": "qdesigner_internal::PluginDialog", "slots": [{"access": "private", "index": 0, "name": "updateCustomWidgetPlugins", "returnType": "void"}, {"access": "private", "arguments": [{"name": "pos", "type": "QPoint"}], "index": 1, "name": "treeWidgetContextMenu", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "plugindialog_p.h", "outputRevision": 69}, {"classes": [{"className": "QtResourceEditorDialog", "lineNumber": 27, "object": true, "qualifiedClassName": "QtResourceEditorDialog", "slots": [{"access": "public", "index": 0, "name": "accept", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "qtresourceeditordialog_p.h", "outputRevision": 69}, {"classes": [{"className": "QtResourceModel", "lineNumber": 58, "object": true, "qualifiedClassName": "QtResourceModel", "signals": [{"access": "public", "arguments": [{"name": "resourceSet", "type": "QtResourceSet*"}, {"name": "resourceSetChanged", "type": "bool"}], "index": 0, "name": "resourceSetActivated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "path", "type": "QString"}], "index": 1, "name": "qrcFileModifiedExternally", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtresourcemodel_p.h", "outputRevision": 69}, {"classes": [{"className": "QtResourceView", "lineNumber": 29, "object": true, "qualifiedClassName": "QtResourceView", "signals": [{"access": "public", "arguments": [{"name": "resource", "type": "QString"}], "index": 0, "name": "resourceSelected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resource", "type": "QString"}], "index": 1, "name": "resourceActivated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}, {"className": "QtResourceViewDialog", "lineNumber": 71, "object": true, "qualifiedClassName": "QtResourceViewDialog", "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "qtresourceview_p.h", "outputRevision": 69}, {"classes": [{"className": "SelectSignalDialog", "lineNumber": 30, "object": true, "qualifiedClassName": "qdesigner_internal::SelectSignalDialog", "slots": [{"access": "private", "arguments": [{"type": "QModelIndex"}, {"type": "QModelIndex"}], "index": 0, "name": "currentChanged", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QModelIndex"}], "index": 1, "name": "activated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "selectsignaldialog_p.h", "outputRevision": 69}, {"classes": [{"className": "SignatureModel", "lineNumber": 48, "object": true, "qualifiedClassName": "qdesigner_internal::SignatureModel", "signals": [{"access": "public", "arguments": [{"name": "signature", "type": "QString"}, {"name": "ok", "type": "bool*"}], "index": 0, "name": "checkSignature", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QStandardItemModel"}]}, {"className": "SignaturePanel", "lineNumber": 61, "object": true, "qualifiedClassName": "qdesigner_internal::SignaturePanel", "signals": [{"access": "public", "arguments": [{"name": "signature", "type": "QString"}, {"name": "ok", "type": "bool*"}], "index": 0, "name": "checkSignature", "returnType": "void"}], "slots": [{"access": "private", "index": 1, "name": "slotAdd", "returnType": "void"}, {"access": "private", "index": 2, "name": "slotRemove", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QItemSelection"}, {"type": "QItemSelection"}], "index": 3, "name": "slotSelectionChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "SignalSlotDialog", "lineNumber": 99, "object": true, "qualifiedClassName": "qdesigner_internal::SignalSlotDialog", "slots": [{"access": "private", "arguments": [{"name": "signature", "type": "QString"}, {"name": "ok", "type": "bool*"}], "index": 0, "name": "slotCheckSignature", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "signalslotdialog_p.h", "outputRevision": 69}, {"classes": [{"className": "PreviewDeviceSkin", "lineNumber": 120, "object": true, "qualifiedClassName": "qdesigner_internal::PreviewDeviceSkin", "slots": [{"access": "private", "arguments": [{"name": "code", "type": "int"}, {"name": "text", "type": "QString"}, {"name": "autorep", "type": "bool"}], "index": 0, "name": "slotSkinKeyPressEvent", "returnType": "void"}, {"access": "private", "arguments": [{"name": "code", "type": "int"}, {"name": "text", "type": "QString"}, {"name": "autorep", "type": "bool"}], "index": 1, "name": "slotSkinKeyReleaseEvent", "returnType": "void"}, {"access": "private", "index": 2, "name": "slotPopupMenu", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QAction*"}], "index": 3, "name": "slotDirection", "returnType": "void"}], "superClasses": [{"access": "public", "name": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"className": "ZoomablePreviewDeviceSkin", "lineNumber": 302, "object": true, "qualifiedClassName": "qdesigner_internal::ZoomablePreviewDeviceSkin", "signals": [{"access": "public", "arguments": [{"type": "int"}], "index": 0, "name": "zoomPercentChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"type": "int"}], "index": 1, "name": "setZoomPercent", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::PreviewDeviceSkin", "name": "PreviewDeviceSkin"}]}], "inputFile": "previewmanager.cpp", "outputRevision": 69}, {"classes": [{"className": "MyMimeData", "lineNumber": 31, "object": true, "qualifiedClassName": "qdesigner_internal::MyMimeData", "superClasses": [{"access": "public", "name": "QMimeData"}]}], "inputFile": "qdesigner_tabwidget.cpp", "outputRevision": 69}, {"classes": [{"className": "LayoutAlignmentMenu", "lineNumber": 124, "object": true, "qualifiedClassName": "qdesigner_internal::LayoutAlignmentMenu", "signals": [{"access": "public", "index": 0, "name": "changed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdesigner_taskmenu.cpp", "outputRevision": 69}, {"classes": [{"className": "QtColorButtonPrivate", "lineNumber": 14, "object": true, "qualifiedClassName": "QtColorButtonPrivate", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtcolorbutton.cpp", "outputRevision": 69}, {"classes": [{"className": "QtGradientDialogPrivate", "lineNumber": 10, "object": true, "qualifiedClassName": "QtGradientDialogPrivate", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtgradientdialog.cpp", "outputRevision": 69}, {"classes": [{"className": "QtGradientEditorPrivate", "lineNumber": 14, "object": true, "qualifiedClassName": "QtGradientEditorPrivate", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtgradienteditor.cpp", "outputRevision": 69}, {"classes": [{"className": "QtGradientStopsControllerPrivate", "lineNumber": 14, "object": true, "qualifiedClassName": "QtGradientStopsControllerPrivate", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtgradientstopscontroller.cpp", "outputRevision": 69}, {"classes": [{"className": "QtGradientStopsWidgetPrivate", "lineNumber": 19, "object": true, "qualifiedClassName": "QtGradientStopsWidgetPrivate", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtgradientstopswidget.cpp", "outputRevision": 69}, {"classes": [{"className": "QtQrcManager", "lineNumber": 272, "object": true, "qualifiedClassName": "qdesigner_internal::QtQrcManager", "signals": [{"access": "public", "arguments": [{"name": "qrcFile", "type": "qdesigner_internal::QtQrcFile*"}], "index": 0, "name": "qrcFileInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "qrcFile", "type": "qdesigner_internal::QtQrcFile*"}, {"name": "oldBeforeQrcFile", "type": "qdesigner_internal::QtQrcFile*"}], "index": 1, "name": "qrcFileMoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "qrcFile", "type": "qdesigner_internal::QtQrcFile*"}], "index": 2, "name": "qrcFileRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resourcePrefix", "type": "qdesigner_internal::QtResourcePrefix*"}], "index": 3, "name": "resourcePrefixInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resourcePrefix", "type": "qdesigner_internal::QtResourcePrefix*"}, {"name": "oldBeforeResourcePrefix", "type": "qdesigner_internal::QtResourcePrefix*"}], "index": 4, "name": "resourcePrefixMoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resourcePrefix", "type": "qdesigner_internal::QtResourcePrefix*"}, {"name": "oldPrefix", "type": "QString"}], "index": 5, "name": "resourcePrefixChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resourcePrefix", "type": "qdesigner_internal::QtResourcePrefix*"}, {"name": "oldLanguage", "type": "QString"}], "index": 6, "name": "resourceLanguageChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resourcePrefix", "type": "qdesigner_internal::QtResourcePrefix*"}], "index": 7, "name": "resourcePrefixRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resourceFile", "type": "qdesigner_internal::QtResourceFile*"}], "index": 8, "name": "resourceFileInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resourceFile", "type": "qdesigner_internal::QtResourceFile*"}, {"name": "oldBeforeResourceFile", "type": "qdesigner_internal::QtResourceFile*"}], "index": 9, "name": "resourceFileMoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resourceFile", "type": "qdesigner_internal::QtResourceFile*"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "QString"}], "index": 10, "name": "resourceAliasChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resourceFile", "type": "qdesigner_internal::QtResourceFile*"}], "index": 11, "name": "resourceFileRemoved", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "path", "type": "QString"}, {"name": "beforeQrcFile", "type": "qdesigner_internal::QtQrcFile*"}, {"name": "newFile", "type": "bool"}], "index": 12, "name": "insertQrcFile", "returnType": "QtQrcFile*"}, {"access": "public", "arguments": [{"name": "path", "type": "QString"}, {"name": "beforeQrcFile", "type": "qdesigner_internal::QtQrcFile*"}], "index": 13, "isCloned": true, "name": "insertQrcFile", "returnType": "QtQrcFile*"}, {"access": "public", "arguments": [{"name": "path", "type": "QString"}], "index": 14, "isCloned": true, "name": "insertQrcFile", "returnType": "QtQrcFile*"}, {"access": "public", "arguments": [{"name": "qrcFile", "type": "QtQrcFile*"}, {"name": "beforeQrcFile", "type": "qdesigner_internal::QtQrcFile*"}], "index": 15, "name": "moveQrcFile", "returnType": "void"}, {"access": "public", "arguments": [{"name": "qrcFile", "type": "qdesigner_internal::QtQrcFile*"}, {"name": "initialState", "type": "QtQrcFileData"}], "index": 16, "name": "setInitialState", "returnType": "void"}, {"access": "public", "arguments": [{"name": "qrcFile", "type": "qdesigner_internal::QtQrcFile*"}], "index": 17, "name": "removeQrcFile", "returnType": "void"}, {"access": "public", "arguments": [{"name": "qrcFile", "type": "qdesigner_internal::QtQrcFile*"}, {"name": "prefix", "type": "QString"}, {"name": "language", "type": "QString"}, {"name": "beforeResourcePrefix", "type": "QtResourcePrefix*"}], "index": 18, "name": "insertResourcePrefix", "returnType": "QtResourcePrefix*"}, {"access": "public", "arguments": [{"name": "qrcFile", "type": "qdesigner_internal::QtQrcFile*"}, {"name": "prefix", "type": "QString"}, {"name": "language", "type": "QString"}], "index": 19, "isCloned": true, "name": "insertResourcePrefix", "returnType": "QtResourcePrefix*"}, {"access": "public", "arguments": [{"name": "resourcePrefix", "type": "qdesigner_internal::QtResourcePrefix*"}, {"name": "beforeResourcePrefix", "type": "QtResourcePrefix*"}], "index": 20, "name": "moveResourcePrefix", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resourcePrefix", "type": "qdesigner_internal::QtResourcePrefix*"}, {"name": "newPrefix", "type": "QString"}], "index": 21, "name": "changeResourcePrefix", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resourcePrefix", "type": "qdesigner_internal::QtResourcePrefix*"}, {"name": "newLanguage", "type": "QString"}], "index": 22, "name": "changeResourceLanguage", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resourcePrefix", "type": "qdesigner_internal::QtResourcePrefix*"}], "index": 23, "name": "removeResourcePrefix", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resourcePrefix", "type": "qdesigner_internal::QtResourcePrefix*"}, {"name": "path", "type": "QString"}, {"name": "alias", "type": "QString"}, {"name": "beforeResourceFile", "type": "qdesigner_internal::QtResourceFile*"}], "index": 24, "name": "insertResourceFile", "returnType": "QtResourceFile*"}, {"access": "public", "arguments": [{"name": "resourcePrefix", "type": "qdesigner_internal::QtResourcePrefix*"}, {"name": "path", "type": "QString"}, {"name": "alias", "type": "QString"}], "index": 25, "isCloned": true, "name": "insertResourceFile", "returnType": "QtResourceFile*"}, {"access": "public", "arguments": [{"name": "resourceFile", "type": "qdesigner_internal::QtResourceFile*"}, {"name": "beforeResourceFile", "type": "qdesigner_internal::QtResourceFile*"}], "index": 26, "name": "moveResourceFile", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resourceFile", "type": "qdesigner_internal::QtResourceFile*"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "QString"}], "index": 27, "name": "changeResourceAlias", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resourceFile", "type": "qdesigner_internal::QtResourceFile*"}], "index": 28, "name": "removeResourceFile", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtresourceeditordialog.cpp", "outputRevision": 69}, {"classes": [{"className": "RichTextEditor", "lineNumber": 139, "object": true, "qualifiedClassName": "qdesigner_internal::RichTextEditor", "signals": [{"access": "public", "index": 0, "name": "stateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 1, "name": "simplifyRichTextChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "b", "type": "bool"}], "index": 2, "name": "setFontBold", "returnType": "void"}, {"access": "public", "arguments": [{"type": "double"}], "index": 3, "name": "setFontPointSize", "returnType": "void"}, {"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 4, "name": "setText", "returnType": "void"}, {"access": "public", "arguments": [{"name": "v", "type": "bool"}], "index": 5, "name": "setSimplifyRichText", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QTextEdit"}]}, {"className": "AddLinkDialog", "lineNumber": 166, "object": true, "qualifiedClassName": "qdesigner_internal::AddLinkDialog", "slots": [{"access": "public", "index": 0, "name": "accept", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}]}, {"className": "HtmlTextEdit", "lineNumber": 228, "object": true, "qualifiedClassName": "qdesigner_internal::HtmlTextEdit", "slots": [{"access": "private", "arguments": [{"name": "action", "type": "QAction*"}], "index": 0, "name": "actionTriggered", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QTextEdit"}]}, {"className": "ColorAction", "lineNumber": 280, "object": true, "qualifiedClassName": "qdesigner_internal::ColorAction", "signals": [{"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 0, "name": "colorChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 1, "name": "chooseColor", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAction"}]}, {"className": "RichTextEditorToolBar", "lineNumber": 331, "object": true, "qualifiedClassName": "qdesigner_internal::RichTextEditorToolBar", "slots": [{"access": "public", "index": 0, "name": "updateActions", "returnType": "void"}, {"access": "private", "arguments": [{"name": "action", "type": "QAction*"}], "index": 1, "name": "alignmentActionTriggered", "returnType": "void"}, {"access": "private", "arguments": [{"name": "size", "type": "QString"}], "index": 2, "name": "sizeInputActivated", "returnType": "void"}, {"access": "private", "arguments": [{"name": "color", "type": "QColor"}], "index": 3, "name": "colorChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "super", "type": "bool"}], "index": 4, "name": "setVAlignSuper", "returnType": "void"}, {"access": "private", "arguments": [{"name": "sub", "type": "bool"}], "index": 5, "name": "setVAlignSub", "returnType": "void"}, {"access": "private", "index": 6, "name": "insertLink", "returnType": "void"}, {"access": "private", "index": 7, "name": "insertImage", "returnType": "void"}, {"access": "private", "index": 8, "name": "layoutDirectionChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QToolBar"}]}], "inputFile": "richtexteditor.cpp", "outputRevision": 69}, {"classes": [{"className": "WizardPageChangeWatcher", "lineNumber": 169, "object": true, "qualifiedClassName": "qdesigner_internal::WizardPageChangeWatcher", "slots": [{"access": "public", "index": 0, "name": "pageChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "widgetfactory.cpp", "outputRevision": 69}]