#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试bootloader解析功能
Copyright (c) 2025 By.举个🌰
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 标准晶晨线刷包制作工具 import AmlogicBootloaderParser

def test_bootloader_parsing():
    """测试bootloader解析"""
    print("🧪 测试bootloader解析功能")
    print("=" * 50)
    
    bootloader_file = "bootloader.PARTITION"
    
    if not os.path.exists(bootloader_file):
        print(f"❌ 找不到bootloader文件: {bootloader_file}")
        return
    
    print(f"📁 bootloader文件: {bootloader_file}")
    print(f"📊 文件大小: {os.path.getsize(bootloader_file)} bytes")
    
    # 创建解析器
    parser = AmlogicBootloaderParser(bootloader_file)
    
    # 解析bootloader
    print("\n🔍 开始解析bootloader...")
    success, message = parser.parse_bootloader()
    
    if success:
        print(f"✅ 解析成功: {message}")
        
        # 测试保存DDR.USB
        print("\n💾 测试保存DDR.USB...")
        ddr_path = "test_DDR.USB"
        success, msg = parser.save_ddr_usb(ddr_path)
        
        if success:
            print(f"✅ DDR.USB保存成功: {msg}")
            
            # 验证文件
            if os.path.exists(ddr_path):
                with open(ddr_path, 'rb') as f:
                    header = f.read(16)
                print(f"📋 DDR.USB头部: {header.hex()}")
                
                # 检查AML标识
                if b'AML' in header:
                    print("✅ DDR.USB包含正确的AML标识")
                else:
                    print("⚠️  DDR.USB未找到AML标识")
        else:
            print(f"❌ DDR.USB保存失败: {msg}")
        
        # 测试保存UBOOT.USB
        print("\n💾 测试保存UBOOT.USB...")
        uboot_path = "test_UBOOT.USB"
        success, msg = parser.save_uboot_usb(uboot_path)
        
        if success:
            print(f"✅ UBOOT.USB保存成功: {msg}")
            
            # 验证文件
            if os.path.exists(uboot_path):
                with open(uboot_path, 'rb') as f:
                    header = f.read(32)
                print(f"📋 UBOOT.USB头部: {header.hex()}")
                
                # 检查AMLC标识
                if b'AMLC' in header:
                    print("✅ UBOOT.USB包含正确的AMLC标识")
                else:
                    print("⚠️  UBOOT.USB未找到AMLC标识")
        else:
            print(f"❌ UBOOT.USB保存失败: {msg}")
        
        # 与原始文件对比
        print("\n🔍 与原始文件对比...")
        
        if os.path.exists("DDR.USB") and os.path.exists(ddr_path):
            original_size = os.path.getsize("DDR.USB")
            extracted_size = os.path.getsize(ddr_path)
            print(f"📊 DDR.USB - 原始: {original_size} bytes, 提取: {extracted_size} bytes")
            
            if original_size == extracted_size:
                print("✅ DDR.USB大小匹配")
            else:
                print("⚠️  DDR.USB大小不匹配")
        
        if os.path.exists("UBOOT.USB") and os.path.exists(uboot_path):
            original_size = os.path.getsize("UBOOT.USB")
            extracted_size = os.path.getsize(uboot_path)
            print(f"📊 UBOOT.USB - 原始: {original_size} bytes, 提取: {extracted_size} bytes")
            
            if original_size == extracted_size:
                print("✅ UBOOT.USB大小匹配")
            else:
                print("⚠️  UBOOT.USB大小不匹配")
        
    else:
        print(f"❌ 解析失败: {message}")

def cleanup_test_files():
    """清理测试文件"""
    test_files = ["test_DDR.USB", "test_UBOOT.USB"]
    
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🗑️  清理测试文件: {file}")

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 bootloader解析功能测试")
    print("Copyright (c) 2025 By.举个🌰")
    print("=" * 60)
    
    try:
        test_bootloader_parsing()
    except Exception as e:
        print(f"❌ 测试过程异常: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🧹 清理测试文件...")
    cleanup_test_files()
    
    print("\n✅ 测试完成！")
    input("按回车键退出...")

if __name__ == "__main__":
    main()
