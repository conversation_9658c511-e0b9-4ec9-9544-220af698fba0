#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实际备份功能
Copyright (c) 2025 By.举个🌰
"""

import subprocess
import tempfile
import os

def test_actual_backup():
    """测试实际的分区备份"""
    print("🧪 测试实际分区备份")
    print("=" * 50)
    
    device_ip = "***************"
    device_port = "5555"
    adb_prefix = f"adb -s {device_ip}:{device_port}"
    
    # 测试实际的分区路径
    test_partition = "/dev/block/mmcblk0p5"  # boot分区
    print(f"测试分区: {test_partition}")
    
    # 检查分区是否存在
    cmd = f'{adb_prefix} shell "test -e {test_partition} && echo exists || echo not_exists"'
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
    print(f"分区存在检查: {result.stdout.strip()}")
    
    if "exists" in result.stdout:
        print("✅ 分区存在，开始测试备份方法")
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"临时目录: {temp_dir}")
            
            # 方法1: adb exec-out + cat
            print("\n🔍 方法1: adb exec-out + cat")
            output_file1 = os.path.join(temp_dir, "boot_exec_cat.img")
            cmd = f'{adb_prefix} exec-out "cat {test_partition}" > "{output_file1}"'
            print(f"命令: {cmd}")
            
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
            print(f"返回码: {result.returncode}")
            
            if os.path.exists(output_file1):
                size = os.path.getsize(output_file1)
                print(f"✅ 方法1成功！文件大小: {size} bytes")
                
                if size > 0:
                    # 检查文件头部
                    with open(output_file1, "rb") as f:
                        header = f.read(16)
                    print(f"文件头部: {header.hex()}")
            else:
                print("❌ 方法1失败，文件不存在")
            
            # 方法2: adb exec-out + dd
            print("\n🔍 方法2: adb exec-out + dd")
            output_file2 = os.path.join(temp_dir, "boot_exec_dd.img")
            cmd = f'{adb_prefix} exec-out "dd if={test_partition} bs=1024 2>/dev/null" > "{output_file2}"'
            print(f"命令: {cmd}")
            
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
            print(f"返回码: {result.returncode}")
            
            if os.path.exists(output_file2):
                size = os.path.getsize(output_file2)
                print(f"✅ 方法2成功！文件大小: {size} bytes")
                
                if size > 0:
                    # 检查文件头部
                    with open(output_file2, "rb") as f:
                        header = f.read(16)
                    print(f"文件头部: {header.hex()}")
            else:
                print("❌ 方法2失败，文件不存在")
            
            # 方法3: adb pull (直接拉取设备文件)
            print("\n🔍 方法3: adb pull")
            output_file3 = os.path.join(temp_dir, "boot_pull.img")
            cmd = f'{adb_prefix} pull {test_partition} "{output_file3}"'
            print(f"命令: {cmd}")
            
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
            print(f"返回码: {result.returncode}")
            print(f"输出: {result.stdout}")
            if result.stderr:
                print(f"错误: {result.stderr}")
            
            if os.path.exists(output_file3):
                size = os.path.getsize(output_file3)
                print(f"✅ 方法3成功！文件大小: {size} bytes")
                
                if size > 0:
                    # 检查文件头部
                    with open(output_file3, "rb") as f:
                        header = f.read(16)
                    print(f"文件头部: {header.hex()}")
            else:
                print("❌ 方法3失败，文件不存在")
            
            # 总结最佳方法
            print("\n📊 备份方法测试总结:")
            methods = [
                ("adb exec-out + cat", output_file1),
                ("adb exec-out + dd", output_file2),
                ("adb pull", output_file3)
            ]
            
            best_method = None
            best_size = 0
            
            for method_name, file_path in methods:
                if os.path.exists(file_path):
                    size = os.path.getsize(file_path)
                    if size > 0:
                        print(f"✅ {method_name}: {size} bytes")
                        if size > best_size:
                            best_method = method_name
                            best_size = size
                    else:
                        print(f"⚠️  {method_name}: 0 bytes")
                else:
                    print(f"❌ {method_name}: 文件不存在")
            
            if best_method:
                print(f"\n🎯 推荐使用: {best_method} (文件大小: {best_size} bytes)")
                return best_method
            else:
                print("\n⚠️  所有方法都失败了")
                return None
    else:
        print("❌ 分区不存在，无法进行备份测试")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 实际备份功能测试")
    print("Copyright (c) 2025 By.举个🌰")
    print("=" * 60)
    
    best_method = test_actual_backup()
    
    if best_method:
        print(f"\n🎉 找到可用的备份方法: {best_method}")
        print("现在可以更新ADB备份工具使用这个方法！")
    else:
        print("\n⚠️  没有找到可用的备份方法")
        print("可能需要检查设备权限或分区路径")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
