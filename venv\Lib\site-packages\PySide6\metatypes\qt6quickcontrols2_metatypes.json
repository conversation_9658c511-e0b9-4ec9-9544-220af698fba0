[{"classes": [{"className": "QQuickAttachedPropertyPropagator", "lineNumber": 14, "object": true, "qualifiedClassName": "QQuickAttachedPropertyPropagator", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickattachedpropertypropagator.h", "outputRevision": 69}, {"classes": [{"className": "QQuickStylePlugin", "lineNumber": 25, "object": true, "qualifiedClassName": "QQuickStylePlugin", "superClasses": [{"access": "public", "name": "QQmlExtensionPlugin"}]}], "inputFile": "qquickstyleplugin_p.h", "outputRevision": 69}]