#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试本地提取功能
Copyright (c) 2025 By.举个🌰
"""

import sys
import os
import tempfile

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 晶晨本地刷机包制作 import BootloaderExtractor, LocalFirmwareBuilder

def test_bootloader_extraction():
    """测试bootloader提取功能"""
    print("🧪 测试bootloader提取功能")
    print("=" * 50)
    
    bootloader_file = "bootloader.PARTITION"
    
    if not os.path.exists(bootloader_file):
        print(f"❌ 找不到bootloader文件: {bootloader_file}")
        return False
    
    print(f"📁 bootloader文件: {bootloader_file}")
    print(f"📊 文件大小: {os.path.getsize(bootloader_file)} bytes")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 临时目录: {temp_dir}")
        
        # 测试提取
        success, message = BootloaderExtractor.extract_ddr_uboot(bootloader_file, temp_dir)
        
        if success:
            print(f"✅ 提取成功: {message}")
            
            # 检查生成的文件
            ddr_path = os.path.join(temp_dir, "DDR.USB")
            uboot_path = os.path.join(temp_dir, "UBOOT.USB")
            
            if os.path.exists(ddr_path):
                ddr_size = os.path.getsize(ddr_path)
                print(f"📦 DDR.USB: {ddr_size} bytes")
                
                # 检查DDR文件头部
                with open(ddr_path, 'rb') as f:
                    header = f.read(32)
                print(f"📋 DDR.USB头部: {header[:16].hex()}")
                
                # 检查是否包含AML标识
                if b'AML' in header:
                    print("✅ DDR.USB包含AML标识")
                else:
                    print("⚠️  DDR.USB未包含AML标识")
            
            if os.path.exists(uboot_path):
                uboot_size = os.path.getsize(uboot_path)
                print(f"📦 UBOOT.USB: {uboot_size} bytes")
                
                # 检查U-Boot文件头部
                with open(uboot_path, 'rb') as f:
                    header = f.read(32)
                print(f"📋 UBOOT.USB头部: {header[:16].hex()}")
                
                # 检查是否包含AMLC标识
                if b'AMLC' in header:
                    print("✅ UBOOT.USB包含AMLC标识")
                else:
                    print("⚠️  UBOOT.USB未包含AMLC标识")
            
            # 与现有文件对比
            print("\n🔍 与现有文件对比...")
            if os.path.exists("DDR.USB"):
                original_size = os.path.getsize("DDR.USB")
                extracted_size = os.path.getsize(ddr_path)
                print(f"📊 DDR.USB - 现有: {original_size} bytes, 提取: {extracted_size} bytes")
                
                size_diff = abs(original_size - extracted_size)
                if size_diff == 0:
                    print("✅ DDR.USB大小完全匹配")
                elif size_diff < 1024:
                    print(f"✅ DDR.USB大小基本匹配 (差异: {size_diff} bytes)")
                else:
                    print(f"⚠️  DDR.USB大小差异较大 (差异: {size_diff} bytes)")
            
            if os.path.exists("UBOOT.USB"):
                original_size = os.path.getsize("UBOOT.USB")
                extracted_size = os.path.getsize(uboot_path)
                print(f"📊 UBOOT.USB - 现有: {original_size} bytes, 提取: {extracted_size} bytes")
                
                size_diff = abs(original_size - extracted_size)
                if size_diff == 0:
                    print("✅ UBOOT.USB大小完全匹配")
                elif size_diff < 1024:
                    print(f"✅ UBOOT.USB大小基本匹配 (差异: {size_diff} bytes)")
                else:
                    print(f"⚠️  UBOOT.USB大小差异较大 (差异: {size_diff} bytes)")
            
            return True
        else:
            print(f"❌ 提取失败: {message}")
            return False

def test_firmware_builder():
    """测试固件构建器"""
    print("\n🧪 测试固件构建器")
    print("=" * 50)
    
    source_dir = "."  # 当前目录
    device_info = {
        'model': 'CM311-1e',
        'brand': 'CM311-1e',
        'chip': 'amlogic',
        'platform': 'amlogic'
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 临时输出目录: {temp_dir}")
        
        builder = LocalFirmwareBuilder(source_dir, temp_dir, device_info)
        
        # 测试目录结构创建
        print("\n📂 测试目录结构创建...")
        success, msg = builder.create_package_structure()
        if success:
            print(f"✅ {msg}")
        else:
            print(f"❌ {msg}")
            return False
        
        # 测试文件复制
        print("\n📋 测试文件复制...")
        success, msg, copied_files = builder.copy_partition_files()
        if success:
            print(f"✅ {msg}")
            print("📋 复制的文件:")
            for file_info in copied_files:
                print(f"  - {file_info['name']}: {file_info['size']}")
        else:
            print(f"❌ {msg}")
        
        # 测试bootloader提取
        print("\n🔧 测试bootloader提取...")
        success, msg = builder.extract_bootloader_files()
        if success:
            print(f"✅ {msg}")
        else:
            print(f"⚠️  {msg}")
        
        # 测试配置文件创建
        print("\n📝 测试配置文件创建...")
        
        # 创建image.cfg
        success, msg = builder.create_image_cfg(copied_files)
        if success:
            print(f"✅ image.cfg: {msg}")
        else:
            print(f"❌ image.cfg: {msg}")
        
        # 创建platform.conf
        success, msg = builder.create_platform_conf()
        if success:
            print(f"✅ platform.conf: {msg}")
        else:
            print(f"❌ platform.conf: {msg}")
        
        # 创建README
        success, msg = builder.create_readme(copied_files)
        if success:
            print(f"✅ README.md: {msg}")
        else:
            print(f"❌ README.md: {msg}")
        
        # 检查生成的文件
        print("\n📋 检查生成的文件...")
        package_dir = builder.package_dir
        
        expected_files = [
            "DDR.USB",
            "UBOOT.USB", 
            "image.cfg",
            "platform/platform.conf",
            "README.md"
        ]
        
        for file_path in expected_files:
            full_path = os.path.join(package_dir, file_path)
            if os.path.exists(full_path):
                size = os.path.getsize(full_path)
                print(f"✅ {file_path}: {size} bytes")
            else:
                print(f"❌ {file_path}: 文件不存在")
        
        # 显示目录结构
        print(f"\n📁 完整目录结构:")
        for root, dirs, files in os.walk(package_dir):
            level = root.replace(package_dir, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                print(f"{subindent}{file} ({file_size} bytes)")
        
        return True

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 晶晨本地刷机包制作功能测试")
    print("Copyright (c) 2025 By.举个🌰")
    print("=" * 60)
    
    # 测试1: bootloader提取
    success1 = test_bootloader_extraction()
    
    # 测试2: 固件构建器
    success2 = test_firmware_builder()
    
    print("\n" + "=" * 60)
    print("📊 测试结果:")
    print(f"  bootloader提取: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"  固件构建器: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！工具功能正常")
    else:
        print("\n⚠️  部分测试失败，请检查相关功能")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
