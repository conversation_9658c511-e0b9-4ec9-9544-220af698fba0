[{"classes": [{"className": "QTyped<PERSON>son", "enums": [{"isClass": true, "isFlag": false, "name": "ObjectOption", "values": ["None", "KeepExtraFields", "WarnExtra"]}, {"isClass": true, "isFlag": false, "name": "ParseMode", "values": ["StopOnError"]}, {"isClass": true, "isFlag": false, "name": "ParseStatus", "values": ["Normal", "Failed"]}], "lineNumber": 38, "namespace": true, "qualifiedClassName": "QTyped<PERSON>son"}], "inputFile": "qtypedjson_p.h", "outputRevision": 69}]