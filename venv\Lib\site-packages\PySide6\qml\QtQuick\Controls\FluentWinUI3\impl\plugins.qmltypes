import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquickfluentwinui3focusstroke_p.h"
        name: "QQuickFluentWinUI3FocusStroke"
        accessSemantics: "reference"
        prototype: "QQuickPaintedItem"
        exports: ["QtQuick.Controls.FluentWinUI3.impl/FocusStroke 6.8"]
        exportMetaObjectRevisions: [1544]
        Property { name: "color"; type: "QColor"; read: "color"; write: "setColor"; index: 0; isFinal: true }
        Property { name: "radius"; type: "int"; read: "radius"; write: "setRadius"; index: 1; isFinal: true }
    }
}
