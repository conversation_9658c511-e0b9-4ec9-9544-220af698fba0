#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
晶晨ADB备份工具
Copyright (c) 2025 By.举个🌰
"""

import sys
import os
import subprocess
import threading
import time
import json
import py7zr
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

class ADBManager:
    """ADB管理器"""
    def __init__(self):
        self.device_ip = "***************"
        self.device_port = "5555"
        self.connected = False
        
    def connect_device(self):
        """连接ADB设备"""
        try:
            # 连接设备
            cmd = f"adb connect {self.device_ip}:{self.device_port}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
            
            if "connected" in result.stdout.lower() or "already connected" in result.stdout.lower():
                self.connected = True
                return True, "设备连接成功"
            else:
                return False, f"连接失败: {result.stdout + result.stderr}"
        except Exception as e:
            return False, f"连接异常: {str(e)}"
    
    def get_device_info(self):
        """获取设备信息"""
        if not self.connected:
            return {}
        
        try:
            info = {}
            # 获取设备型号
            cmd = "adb shell getprop ro.product.model"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
            info['model'] = result.stdout.strip() if result.returncode == 0 else "Unknown"
            
            # 获取设备品牌
            cmd = "adb shell getprop ro.product.brand"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
            info['brand'] = result.stdout.strip() if result.returncode == 0 else "Unknown"
            
            # 获取Android版本
            cmd = "adb shell getprop ro.build.version.release"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
            info['android_version'] = result.stdout.strip() if result.returncode == 0 else "Unknown"
            
            return info
        except Exception as e:
            return {"error": str(e)}
    
    def get_partitions(self):
        """获取分区列表"""
        if not self.connected:
            return []

        try:
            partitions = []

            # 尝试多种方式获取分区信息
            partition_paths = [
                "/dev/block/by-name/",
                "/dev/block/platform/*/by-name/",
                "/dev/block/bootdevice/by-name/"
            ]

            for path_pattern in partition_paths:
                if '*' in path_pattern:
                    # 使用通配符查找
                    cmd = f"adb shell find /dev/block -name by-name -type d 2>/dev/null"
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0 and result.stdout.strip():
                        actual_paths = result.stdout.strip().split('\n')
                        for actual_path in actual_paths:
                            cmd = f"adb shell ls -la {actual_path}/"
                            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                            if result.returncode == 0:
                                partitions.extend(self._parse_partition_list(result.stdout, actual_path))
                                break
                else:
                    cmd = f"adb shell ls -la {path_pattern}"
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        partitions.extend(self._parse_partition_list(result.stdout, path_pattern))
                        break

            # 如果上述方法都失败，尝试直接查找常见分区
            if not partitions:
                common_partitions = [
                    'boot', 'bootloader', 'recovery', 'system', 'vendor',
                    'product', 'logo', 'param', 'vbmeta', 'metadata', 'cri_data'
                ]

                for partition in common_partitions:
                    # 尝试多种可能的路径
                    possible_paths = [
                        f"/dev/block/{partition}",
                        f"/dev/{partition}",
                        f"/dev/block/mmcblk0p*"  # 这个需要特殊处理
                    ]

                    for path in possible_paths:
                        if '*' not in path:
                            cmd = f"adb shell test -e {path} && echo exists"
                            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                            if result.returncode == 0 and 'exists' in result.stdout:
                                # 获取分区大小
                                partition_size = self._get_partition_size(path)

                                partitions.append({
                                    'name': partition,
                                    'path': path,
                                    'size': partition_size
                                })
                                break

            return partitions
        except Exception as e:
            return []

    def _parse_partition_list(self, output, base_path):
        """解析分区列表输出"""
        partitions = []
        lines = output.strip().split('\n')

        for line in lines:
            if '->' in line and not line.startswith('total'):
                parts = line.split()
                if len(parts) >= 9:
                    partition_name = parts[-3]
                    partition_path = parts[-1]
                    # 如果是相对路径，转换为绝对路径
                    if not partition_path.startswith('/'):
                        partition_path = f"{base_path.rstrip('/')}/{partition_path}"

                    # 获取分区大小
                    partition_size = self._get_partition_size(partition_path)

                    partitions.append({
                        'name': partition_name,
                        'path': partition_path,
                        'size': partition_size
                    })

        return partitions

    def _get_partition_size(self, partition_path):
        """获取分区大小"""
        try:
            # 方法1: 使用blockdev --getsize64 (如果支持)
            cmd = f'adb shell "blockdev --getsize64 {partition_path} 2>/dev/null"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
            if result.returncode == 0 and result.stdout.strip().isdigit():
                size_bytes = int(result.stdout.strip())
                return self._format_size(size_bytes)

            # 方法2: 从/proc/partitions获取大小
            device_name = partition_path.split('/')[-1]
            cmd = f'adb shell "cat /proc/partitions | grep {device_name}"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    parts = line.split()
                    if len(parts) >= 4 and parts[3] == device_name:
                        # /proc/partitions中的大小是以KB为单位
                        size_kb = int(parts[2])
                        size_bytes = size_kb * 1024
                        return self._format_size(size_bytes)

            # 方法3: 尝试使用stat命令
            cmd = f'adb shell "stat -c %s {partition_path} 2>/dev/null"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
            if result.returncode == 0 and result.stdout.strip().isdigit():
                size_bytes = int(result.stdout.strip())
                return self._format_size(size_bytes)

            return "Unknown"

        except Exception:
            return "Unknown"

    def _format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        size = float(size_bytes)

        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1

        if unit_index == 0:
            return f"{int(size)} {units[unit_index]}"
        else:
            return f"{size:.1f} {units[unit_index]}"

class ConnectWorker(QThread):
    """连接工作线程"""
    finished = Signal(bool, str)

    def __init__(self, adb_manager):
        super().__init__()
        self.adb_manager = adb_manager

    def run(self):
        """执行连接"""
        try:
            success, message = self.adb_manager.connect_device()
            self.finished.emit(success, message)
        except Exception as e:
            self.finished.emit(False, f"连接异常: {str(e)}")

class DeviceInfoWorker(QThread):
    """设备信息获取线程"""
    finished = Signal(dict)

    def __init__(self, adb_manager):
        super().__init__()
        self.adb_manager = adb_manager

    def run(self):
        """获取设备信息"""
        try:
            info = self.adb_manager.get_device_info()
            self.finished.emit(info)
        except Exception as e:
            self.finished.emit({"error": str(e)})

class PartitionWorker(QThread):
    """分区获取线程"""
    finished = Signal(list)

    def __init__(self, adb_manager):
        super().__init__()
        self.adb_manager = adb_manager

    def run(self):
        """获取分区列表"""
        try:
            partitions = self.adb_manager.get_partitions()
            self.finished.emit(partitions)
        except Exception as e:
            self.finished.emit([])

class BackupWorker(QThread):
    """备份工作线程"""
    progress_updated = Signal(int, str)
    backup_finished = Signal(bool, str)
    
    def __init__(self, adb_manager, partitions, output_dir):
        super().__init__()
        self.adb_manager = adb_manager
        self.partitions = partitions
        self.output_dir = output_dir
        self.running = True
        
    def run(self):
        """执行备份"""
        try:
            total_partitions = len(self.partitions)
            
            for i, partition in enumerate(self.partitions):
                if not self.running:
                    break
                    
                partition_name = partition['name']
                partition_path = partition['path']
                
                self.progress_updated.emit(
                    int((i / total_partitions) * 100),
                    f"正在备份分区: {partition_name}"
                )
                
                # 备份分区
                output_file = os.path.join(self.output_dir, f"{partition_name}.img")
                backup_success = False
                backup_method = ""

                # 优先级排序的备份方法
                backup_methods = [
                    ("adb pull", f'adb pull {partition_path} "{output_file}"'),
                    ("adb exec-out", f'adb exec-out "cat {partition_path}" > "{output_file}"'),
                    ("dd命令", f'adb shell "dd if={partition_path} bs=1024" > "{output_file}"'),
                    ("cat命令", f'adb shell "cat {partition_path}" > "{output_file}"')
                ]

                for method_name, cmd in backup_methods:
                    if backup_success:
                        break

                    try:
                        self.progress_updated.emit(
                            int((i / total_partitions) * 100),
                            f"尝试{method_name}: {partition_name}"
                        )

                        # 删除可能存在的空文件
                        if os.path.exists(output_file):
                            os.remove(output_file)

                        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)

                        # 检查备份是否成功
                        if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                            backup_success = True
                            backup_method = method_name
                            file_size = os.path.getsize(output_file)
                            self.progress_updated.emit(
                                int((i / total_partitions) * 100),
                                f"备份成功({method_name}): {partition_name} ({file_size} bytes)"
                            )
                        else:
                            self.progress_updated.emit(
                                int((i / total_partitions) * 100),
                                f"{method_name}失败: {partition_name}"
                            )
                    except subprocess.TimeoutExpired:
                        self.progress_updated.emit(
                            int((i / total_partitions) * 100),
                            f"{method_name}超时: {partition_name}"
                        )
                    except Exception as e:
                        self.progress_updated.emit(
                            int((i / total_partitions) * 100),
                            f"{method_name}异常: {partition_name} - {str(e)}"
                        )

                if not backup_success:
                    self.progress_updated.emit(
                        int((i / total_partitions) * 100),
                        f"备份失败: {partition_name} - 所有方法都失败"
                    )
                    # 删除空文件
                    if os.path.exists(output_file):
                        os.remove(output_file)
                    continue

                # 压缩文件
                if backup_success:
                    try:
                        self.progress_updated.emit(
                            int((i / total_partitions) * 100),
                            f"正在压缩: {partition_name}.img"
                        )

                        compressed_file = output_file + ".7z"
                        with py7zr.SevenZipFile(compressed_file, 'w', password=None) as archive:
                            archive.write(output_file, f"{partition_name}.img")

                        # 删除原始文件
                        if os.path.exists(output_file):
                            os.remove(output_file)

                        self.progress_updated.emit(
                            int((i / total_partitions) * 100),
                            f"压缩完成: {partition_name}.img.7z"
                        )
                    except Exception as e:
                        self.progress_updated.emit(
                            int((i / total_partitions) * 100),
                            f"压缩失败: {partition_name} - {str(e)}"
                        )
            
            self.progress_updated.emit(100, "备份完成")
            self.backup_finished.emit(True, "所有分区备份完成")
            
        except Exception as e:
            self.backup_finished.emit(False, f"备份过程出错: {str(e)}")
    
    def stop(self):
        """停止备份"""
        self.running = False

class MainWindow(QMainWindow):
    """主窗口"""
    def __init__(self):
        super().__init__()
        self.adb_manager = ADBManager()
        self.backup_worker = None
        self.init_ui()
        self.apply_dracula_style()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("晶晨ADB备份工具 - By.举个🌰")
        self.setGeometry(100, 100, 800, 600)
        self.setWindowIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 连接区域
        connection_group = QGroupBox("设备连接")
        connection_layout = QHBoxLayout(connection_group)
        
        self.ip_label = QLabel(f"设备IP: {self.adb_manager.device_ip}")
        self.connect_btn = QPushButton("连接设备")
        self.connect_btn.clicked.connect(self.connect_device)
        self.status_label = QLabel("未连接")
        
        connection_layout.addWidget(self.ip_label)
        connection_layout.addWidget(self.connect_btn)
        connection_layout.addWidget(self.status_label)
        connection_layout.addStretch()
        
        # 设备信息区域
        info_group = QGroupBox("设备信息")
        info_layout = QVBoxLayout(info_group)
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(100)
        self.info_text.setReadOnly(True)
        info_layout.addWidget(self.info_text)
        
        # 分区列表区域
        partition_group = QGroupBox("分区列表")
        partition_layout = QVBoxLayout(partition_group)
        
        self.partition_table = QTableWidget()
        self.partition_table.setColumnCount(3)
        self.partition_table.setHorizontalHeaderLabels(["分区名称", "设备路径", "大小"])

        # 设置列宽自适应
        header = self.partition_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 分区名称列
        header.setSectionResizeMode(1, QHeaderView.Stretch)          # 设备路径列拉伸
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 大小列

        # 设置表格属性
        self.partition_table.setAlternatingRowColors(True)
        self.partition_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.partition_table.setShowGrid(True)
        partition_layout.addWidget(self.partition_table)
        
        # 操作区域
        operation_group = QGroupBox("备份操作")
        operation_layout = QVBoxLayout(operation_group)
        
        path_layout = QHBoxLayout()
        self.path_label = QLabel("保存路径: 未选择")
        self.select_path_btn = QPushButton("选择路径")
        self.select_path_btn.clicked.connect(self.select_output_path)
        path_layout.addWidget(self.path_label)
        path_layout.addWidget(self.select_path_btn)
        
        button_layout = QHBoxLayout()
        self.refresh_btn = QPushButton("刷新分区")
        self.refresh_btn.clicked.connect(self.refresh_partitions)
        self.backup_btn = QPushButton("开始备份")
        self.backup_btn.clicked.connect(self.start_backup)
        self.backup_btn.setEnabled(False)
        
        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.backup_btn)
        button_layout.addStretch()
        
        operation_layout.addLayout(path_layout)
        operation_layout.addLayout(button_layout)
        
        # 进度区域
        progress_group = QGroupBox("备份进度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_label = QLabel("等待开始...")
        
        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.progress_label)
        
        # 日志区域
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        # 添加到主布局
        main_layout.addWidget(connection_group)
        main_layout.addWidget(info_group)
        main_layout.addWidget(partition_group)
        main_layout.addWidget(operation_group)
        main_layout.addWidget(progress_group)
        main_layout.addWidget(log_group)
        
        self.output_dir = None
        
    def apply_dracula_style(self):
        """应用Dracula风格"""
        style = """
        QMainWindow {
            background-color: #282a36;
            color: #f8f8f2;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #6272a4;
            border-radius: 5px;
            margin-top: 1ex;
            color: #f8f8f2;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #bd93f9;
        }
        QPushButton {
            background-color: #44475a;
            border: 1px solid #6272a4;
            border-radius: 4px;
            padding: 8px;
            color: #f8f8f2;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #6272a4;
        }
        QPushButton:pressed {
            background-color: #bd93f9;
        }
        QPushButton:disabled {
            background-color: #44475a;
            color: #6272a4;
        }
        QTextEdit, QTableWidget {
            background-color: #44475a;
            border: 1px solid #6272a4;
            border-radius: 4px;
            color: #f8f8f2;
            selection-background-color: #bd93f9;
        }
        QTableWidget::item {
            padding: 5px;
        }
        QTableWidget::item:selected {
            background-color: #bd93f9;
        }
        QHeaderView::section {
            background-color: #6272a4;
            color: #f8f8f2;
            padding: 5px;
            border: none;
        }
        QProgressBar {
            border: 1px solid #6272a4;
            border-radius: 4px;
            text-align: center;
            color: #f8f8f2;
        }
        QProgressBar::chunk {
            background-color: #50fa7b;
            border-radius: 3px;
        }
        QLabel {
            color: #f8f8f2;
        }
        """
        self.setStyleSheet(style)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

    def connect_device(self):
        """连接设备"""
        self.connect_btn.setEnabled(False)
        self.connect_btn.setText("连接中...")
        self.log_message("正在连接设备...")

        # 使用QThread替代普通线程
        self.connect_worker = ConnectWorker(self.adb_manager)
        self.connect_worker.finished.connect(self.on_connect_finished)
        self.connect_worker.start()

    def on_connect_finished(self, success, message):
        """连接完成回调"""
        self.connect_btn.setEnabled(True)
        self.connect_btn.setText("连接设备")

        if success:
            self.status_label.setText("✅ 已连接")
            self.status_label.setStyleSheet("color: #50fa7b;")
            self.log_message("设备连接成功")
            self.refresh_btn.setEnabled(True)

            # 获取设备信息
            self.update_device_info()
            self.refresh_partitions()
        else:
            self.status_label.setText("❌ 连接失败")
            self.status_label.setStyleSheet("color: #ff5555;")
            self.log_message(f"连接失败: {message}")

    def update_device_info(self):
        """更新设备信息"""
        self.info_worker = DeviceInfoWorker(self.adb_manager)
        self.info_worker.finished.connect(self.display_device_info)
        self.info_worker.start()

    def display_device_info(self, info):
        """显示设备信息"""
        if 'error' in info:
            self.info_text.setText(f"获取设备信息失败: {info['error']}")
        else:
            info_text = f"品牌: {info.get('brand', 'Unknown')}\n"
            info_text += f"型号: {info.get('model', 'Unknown')}\n"
            info_text += f"Android版本: {info.get('android_version', 'Unknown')}"
            self.info_text.setText(info_text)

    def refresh_partitions(self):
        """刷新分区列表"""
        if not self.adb_manager.connected:
            self.log_message("请先连接设备")
            return

        self.refresh_btn.setEnabled(False)
        self.refresh_btn.setText("刷新中...")
        self.log_message("正在获取分区列表...")

        self.partition_worker = PartitionWorker(self.adb_manager)
        self.partition_worker.finished.connect(self.update_partition_table)
        self.partition_worker.start()

    def update_partition_table(self, partitions):
        """更新分区表格"""
        self.refresh_btn.setEnabled(True)
        self.refresh_btn.setText("刷新分区")

        self.partition_table.setRowCount(len(partitions))

        for i, partition in enumerate(partitions):
            self.partition_table.setItem(i, 0, QTableWidgetItem(partition['name']))
            self.partition_table.setItem(i, 1, QTableWidgetItem(partition['path']))
            self.partition_table.setItem(i, 2, QTableWidgetItem(partition['size']))

        self.log_message(f"发现 {len(partitions)} 个分区")

        if len(partitions) > 0 and self.output_dir:
            self.backup_btn.setEnabled(True)

    def select_output_path(self):
        """选择输出路径"""
        dialog = QFileDialog()
        path = dialog.getExistingDirectory(self, "选择备份保存路径")

        if path:
            # 获取设备型号作为文件夹名
            device_info = self.adb_manager.get_device_info()
            model = device_info.get('model', 'Unknown_Device').replace(' ', '_')

            # 创建以设备型号命名的文件夹
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            folder_name = f"{model}_{timestamp}"
            self.output_dir = os.path.join(path, folder_name)

            try:
                os.makedirs(self.output_dir, exist_ok=True)
                self.path_label.setText(f"保存路径: {self.output_dir}")
                self.log_message(f"输出路径设置为: {self.output_dir}")

                if self.partition_table.rowCount() > 0:
                    self.backup_btn.setEnabled(True)
            except Exception as e:
                self.log_message(f"创建输出目录失败: {str(e)}")

    def start_backup(self):
        """开始备份"""
        if not self.adb_manager.connected:
            self.log_message("请先连接设备")
            return

        if not self.output_dir:
            self.log_message("请先选择保存路径")
            return

        if self.partition_table.rowCount() == 0:
            self.log_message("没有发现分区")
            return

        # 获取分区列表
        partitions = []
        for i in range(self.partition_table.rowCount()):
            name = self.partition_table.item(i, 0).text()
            path = self.partition_table.item(i, 1).text()
            partitions.append({'name': name, 'path': path})

        # 禁用按钮
        self.backup_btn.setEnabled(False)
        self.backup_btn.setText("备份中...")
        self.refresh_btn.setEnabled(False)

        # 启动备份线程
        self.backup_worker = BackupWorker(self.adb_manager, partitions, self.output_dir)
        self.backup_worker.progress_updated.connect(self.update_progress)
        self.backup_worker.backup_finished.connect(self.on_backup_finished)
        self.backup_worker.start()

        self.log_message("开始备份分区...")

    def update_progress(self, value, message):
        """更新进度"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(message)
        self.log_message(message)

    def on_backup_finished(self, success, message):
        """备份完成回调"""
        self.backup_btn.setEnabled(True)
        self.backup_btn.setText("开始备份")
        self.refresh_btn.setEnabled(True)

        if success:
            self.log_message("✅ 备份完成！")
            QMessageBox.information(self, "备份完成", f"所有分区已成功备份到:\n{self.output_dir}")
        else:
            self.log_message(f"❌ 备份失败: {message}")
            QMessageBox.warning(self, "备份失败", message)

    def closeEvent(self, event):
        """关闭事件"""
        if self.backup_worker and self.backup_worker.isRunning():
            reply = QMessageBox.question(self, "确认退出", "备份正在进行中，确定要退出吗？")
            if reply == QMessageBox.Yes:
                self.backup_worker.stop()
                self.backup_worker.wait(3000)  # 等待3秒
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("晶晨ADB备份工具")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("By.举个🌰")

    # 检查ADB是否可用
    try:
        result = subprocess.run("adb version", shell=True, capture_output=True, text=True, timeout=5)
        if result.returncode != 0:
            QMessageBox.critical(None, "错误", "未找到ADB工具，请确保ADB已安装并添加到系统PATH中。")
            sys.exit(1)
    except Exception as e:
        QMessageBox.critical(None, "错误", f"ADB检查失败: {str(e)}")
        sys.exit(1)

    window = MainWindow()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
