#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
晶晨ADB备份工具
Copyright (c) 2025 By.举个🌰
"""

import sys
import os
import subprocess
import threading
import time
import json
import py7zr
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

class ADBManager:
    """ADB管理器"""
    def __init__(self, device_ip="***************", device_port="5555"):
        self.device_ip = device_ip
        self.device_port = device_port
        self.connected = False
        
    def connect_device(self):
        """连接ADB设备"""
        try:
            # 先检查是否已经连接
            check_cmd = "adb devices"
            check_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=5)

            if check_result.returncode == 0 and f"{self.device_ip}:{self.device_port}" in check_result.stdout:
                if "device" in check_result.stdout:
                    # 设备已连接，尝试获取root权限
                    root_success, root_msg = self._enable_root()
                    if root_success:
                        self.connected = True
                        return True, f"设备已连接并获取root权限: {root_msg}"
                    else:
                        self.connected = True
                        return True, f"设备已连接但未获取root权限: {root_msg}"

            # 连接设备
            cmd = f"adb connect {self.device_ip}:{self.device_port}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=15)

            if result.returncode == 0:
                output_lower = result.stdout.lower()
                if "connected" in output_lower or "already connected" in output_lower:
                    # 连接成功，尝试获取root权限
                    root_success, root_msg = self._enable_root()
                    self.connected = True
                    if root_success:
                        return True, f"设备连接成功并获取root权限: {root_msg}"
                    else:
                        return True, f"设备连接成功但未获取root权限: {root_msg}"
                elif "cannot connect" in output_lower:
                    return False, "无法连接到设备，请检查IP地址和网络"
                else:
                    return False, f"连接状态不明确: {result.stdout}"
            else:
                return False, f"连接命令失败: {result.stderr}"
        except subprocess.TimeoutExpired:
            return False, "连接超时，请检查网络连接"
        except Exception as e:
            return False, f"连接异常: {str(e)}"

    def _enable_root(self):
        """启用ADB root权限"""
        try:
            adb_prefix = self._get_adb_command_prefix()

            # 检查当前是否已经是root
            check_cmd = f'{adb_prefix} shell "id"'
            result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=5)
            if result.returncode == 0 and "uid=0(root)" in result.stdout:
                return True, "已经具有root权限"

            # 尝试启用root
            root_cmd = f'{adb_prefix} root'
            result = subprocess.run(root_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                output_lower = result.stdout.lower()
                if "restarting" in output_lower or "already running as root" in output_lower:
                    # 等待ADB重启
                    import time
                    time.sleep(2)

                    # 重新检查root权限
                    check_cmd = f'{adb_prefix} shell "id"'
                    result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=5)
                    if result.returncode == 0 and "uid=0(root)" in result.stdout:
                        return True, "成功获取root权限"
                    else:
                        return False, "root权限获取失败，可能设备不支持"
                elif "cannot run as root" in output_lower:
                    return False, "设备不支持root权限"
                else:
                    return True, "root权限状态不明确，尝试继续"
            else:
                return False, f"root命令执行失败: {result.stderr}"

        except Exception as e:
            return False, f"root权限获取异常: {str(e)}"

    def _get_adb_command_prefix(self):
        """获取ADB命令前缀，指定设备"""
        return f"adb -s {self.device_ip}:{self.device_port}"
    
    def get_device_info(self):
        """获取设备信息"""
        if not self.connected:
            return {}

        try:
            info = {}
            adb_prefix = self._get_adb_command_prefix()

            # 获取设备型号
            cmd = f"{adb_prefix} shell getprop ro.product.model"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
            info['model'] = result.stdout.strip() if result.returncode == 0 else "Unknown"

            # 获取设备品牌
            cmd = f"{adb_prefix} shell getprop ro.product.brand"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
            info['brand'] = result.stdout.strip() if result.returncode == 0 else "Unknown"

            # 获取Android版本
            cmd = f"{adb_prefix} shell getprop ro.build.version.release"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
            info['android_version'] = result.stdout.strip() if result.returncode == 0 else "Unknown"

            return info
        except Exception as e:
            return {"error": str(e)}
    
    def get_partitions(self):
        """获取分区列表"""
        if not self.connected:
            return []

        try:
            partitions = []
            adb_prefix = self._get_adb_command_prefix()

            # 方法1: 尝试从/proc/partitions获取分区信息
            print("🔍 尝试方法1: /proc/partitions")
            cmd = f"{adb_prefix} shell cat /proc/partitions"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
            if result.returncode == 0 and result.stdout.strip():
                print("✅ 成功获取/proc/partitions")
                partitions = self._parse_proc_partitions(result.stdout)
                if partitions:
                    print(f"✅ 方法1成功，找到 {len(partitions)} 个分区")
                    return partitions

            # 方法2: 尝试从/dev/block/by-name/获取分区
            print("🔍 尝试方法2: /dev/block/by-name/")
            cmd = f"{adb_prefix} shell ls -la /dev/block/by-name/"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
            if result.returncode == 0 and result.stdout.strip():
                partitions = self._parse_partition_list(result.stdout, "/dev/block/by-name")
                if partitions:
                    print(f"✅ 方法2成功，找到 {len(partitions)} 个分区")
                    return partitions

            # 方法3: 查找所有by-name目录
            print("🔍 尝试方法3: 查找by-name目录")
            cmd = f"{adb_prefix} shell find /dev/block -name by-name -type d"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
            if result.returncode == 0 and result.stdout.strip():
                by_name_paths = result.stdout.strip().split('\n')
                for by_name_path in by_name_paths:
                    cmd = f"{adb_prefix} shell ls -la {by_name_path}/"
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0 and result.stdout.strip():
                        partitions = self._parse_partition_list(result.stdout, by_name_path)
                        if partitions:
                            print(f"✅ 方法3成功，在 {by_name_path} 找到 {len(partitions)} 个分区")
                            return partitions

            # 方法4: 直接查找常见分区
            print("🔍 尝试方法4: 直接查找常见分区")
            common_partitions = [
                'boot', 'bootloader', 'recovery', 'system', 'vendor',
                'product', 'logo', 'param', 'vbmeta', 'metadata',
                'cri_data', 'dtb', 'misc', 'cache', 'userdata'
            ]

            for partition in common_partitions:
                # 尝试多种可能的路径
                possible_paths = [
                    f"/dev/block/{partition}",
                    f"/dev/{partition}"
                ]

                for path in possible_paths:
                    cmd = f'{adb_prefix} shell "test -e {path} && echo exists"'
                    try:
                        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=3)
                        if result.returncode == 0 and 'exists' in result.stdout:
                            # 获取分区大小
                            partition_size = self._get_partition_size(path)
                            partitions.append({
                                'name': partition,
                                'path': path,
                                'size': partition_size
                            })
                            print(f"✅ 找到分区: {partition} -> {path} ({partition_size})")
                            break
                    except subprocess.TimeoutExpired:
                        continue
                    except Exception:
                        continue

            if partitions:
                print(f"✅ 方法4成功，找到 {len(partitions)} 个分区")
            else:
                print("❌ 所有方法都失败，未找到分区")

            return partitions
        except Exception as e:
            print(f"❌ 分区获取异常: {str(e)}")
            return []

    def _parse_proc_partitions(self, output):
        """解析/proc/partitions输出"""
        partitions = []
        lines = output.strip().split('\n')

        # 跳过标题行
        for line in lines[2:]:  # 前两行通常是标题
            parts = line.split()
            if len(parts) >= 4:
                try:
                    major = int(parts[0])
                    minor = int(parts[1])
                    blocks = int(parts[2])  # 以KB为单位
                    name = parts[3]

                    # 过滤掉主设备和一些不需要的分区
                    if minor == 0 or name.startswith('loop') or name.startswith('ram'):
                        continue

                    # 尝试映射到常见的分区名称
                    partition_name = self._map_partition_name(name)
                    if partition_name:
                        size_bytes = blocks * 1024
                        partitions.append({
                            'name': partition_name,
                            'path': f"/dev/block/{name}",
                            'size': self._format_size(size_bytes)
                        })
                        print(f"✅ 从/proc/partitions找到: {partition_name} -> /dev/block/{name} ({self._format_size(size_bytes)})")

                except ValueError:
                    continue

        return partitions

    def _map_partition_name(self, device_name):
        """映射设备名称到分区名称"""
        # 常见的分区映射规则
        partition_mappings = {
            # mmcblk设备映射
            'mmcblk0p1': 'bootloader',
            'mmcblk0p2': 'bootloader',
            'mmcblk0p3': 'env',
            'mmcblk0p4': 'logo',
            'mmcblk0p5': 'boot',
            'mmcblk0p6': 'recovery',
            'mmcblk0p7': 'system',
            'mmcblk0p8': 'vendor',
            'mmcblk0p9': 'product',
            'mmcblk0p10': 'param',
            'mmcblk0p11': 'vbmeta',
            'mmcblk0p12': 'metadata',
            'mmcblk0p13': 'cri_data',
            'mmcblk0p14': 'misc',
            'mmcblk0p15': 'cache',
            'mmcblk0p16': 'userdata',

            # 直接名称映射
            'boot': 'boot',
            'bootloader': 'bootloader',
            'recovery': 'recovery',
            'system': 'system',
            'vendor': 'vendor',
            'product': 'product',
            'logo': 'logo',
            'param': 'param',
            'vbmeta': 'vbmeta',
            'metadata': 'metadata',
            'cri_data': 'cri_data',
            'misc': 'misc',
            'cache': 'cache',
            'userdata': 'userdata'
        }

        # 直接匹配
        if device_name in partition_mappings:
            return partition_mappings[device_name]

        # 模糊匹配
        for pattern, name in partition_mappings.items():
            if pattern in device_name or device_name in pattern:
                return name

        # 如果是mmcblk设备但没有映射，返回原名称
        if 'mmcblk' in device_name and 'p' in device_name:
            return device_name

        return None

    def _parse_partition_list(self, output, base_path):
        """解析分区列表输出"""
        partitions = []
        lines = output.strip().split('\n')

        for line in lines:
            if '->' in line and not line.startswith('total'):
                parts = line.split()
                if len(parts) >= 9:
                    partition_name = parts[-3]
                    partition_path = parts[-1]
                    # 如果是相对路径，转换为绝对路径
                    if not partition_path.startswith('/'):
                        partition_path = f"{base_path.rstrip('/')}/{partition_path}"

                    # 获取分区大小
                    partition_size = self._get_partition_size(partition_path)

                    partitions.append({
                        'name': partition_name,
                        'path': partition_path,
                        'size': partition_size
                    })

        return partitions

    def _get_partition_size(self, partition_path):
        """获取分区大小"""
        try:
            adb_prefix = self._get_adb_command_prefix()

            # 方法1: 使用blockdev --getsize64
            cmd = f'{adb_prefix} shell "blockdev --getsize64 {partition_path} 2>/dev/null || echo 0"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                size_output = result.stdout.strip()
                if size_output.isdigit() and int(size_output) > 0:
                    size_bytes = int(size_output)
                    return self._format_size(size_bytes)

            # 方法2: 从/proc/partitions获取大小
            device_name = partition_path.split('/')[-1]
            cmd = f'{adb_prefix} shell "cat /proc/partitions 2>/dev/null | grep {device_name}"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    parts = line.split()
                    if len(parts) >= 4 and parts[3] == device_name:
                        try:
                            # /proc/partitions中的大小是以KB为单位
                            size_kb = int(parts[2])
                            if size_kb > 0:
                                size_bytes = size_kb * 1024
                                return self._format_size(size_bytes)
                        except ValueError:
                            continue

            # 方法3: 尝试使用stat命令
            cmd = f'adb shell "stat -c %s {partition_path} 2>/dev/null"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
            if result.returncode == 0 and result.stdout.strip().isdigit():
                size_bytes = int(result.stdout.strip())
                return self._format_size(size_bytes)

            return "Unknown"

        except Exception:
            return "Unknown"

    def _format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        size = float(size_bytes)

        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1

        if unit_index == 0:
            return f"{int(size)} {units[unit_index]}"
        else:
            return f"{size:.1f} {units[unit_index]}"

class ConnectWorker(QThread):
    """连接工作线程"""
    finished = Signal(bool, str)

    def __init__(self, adb_manager):
        super().__init__()
        self.adb_manager = adb_manager

    def run(self):
        """执行连接"""
        try:
            success, message = self.adb_manager.connect_device()
            self.finished.emit(success, message)
        except Exception as e:
            self.finished.emit(False, f"连接异常: {str(e)}")

class DeviceInfoWorker(QThread):
    """设备信息获取线程"""
    finished = Signal(dict)

    def __init__(self, adb_manager):
        super().__init__()
        self.adb_manager = adb_manager

    def run(self):
        """获取设备信息"""
        try:
            info = self.adb_manager.get_device_info()
            self.finished.emit(info)
        except Exception as e:
            self.finished.emit({"error": str(e)})

class PartitionWorker(QThread):
    """分区获取线程"""
    finished = Signal(list)
    progress = Signal(str)

    def __init__(self, adb_manager):
        super().__init__()
        self.adb_manager = adb_manager

    def run(self):
        """获取分区列表"""
        try:
            self.progress.emit("开始获取分区列表...")
            partitions = self.adb_manager.get_partitions()
            self.progress.emit(f"分区获取完成，找到 {len(partitions)} 个分区")
            self.finished.emit(partitions)
        except Exception as e:
            self.progress.emit(f"分区获取异常: {str(e)}")
            self.finished.emit([])

class BackupWorker(QThread):
    """备份工作线程"""
    progress_updated = Signal(int, str)
    backup_finished = Signal(bool, str)
    
    def __init__(self, adb_manager, partitions, output_dir):
        super().__init__()
        self.adb_manager = adb_manager
        self.partitions = partitions
        self.output_dir = output_dir
        self.running = True
        
    def run(self):
        """执行备份"""
        try:
            total_partitions = len(self.partitions)
            
            for i, partition in enumerate(self.partitions):
                if not self.running:
                    break
                    
                partition_name = partition['name']
                partition_path = partition['path']
                
                self.progress_updated.emit(
                    int((i / total_partitions) * 100),
                    f"正在备份分区: {partition_name}"
                )
                
                # 备份分区
                output_file = os.path.join(self.output_dir, f"{partition_name}.img")
                backup_success = False
                backup_method = ""

                # 优先级排序的备份方法
                backup_methods = [
                    ("adb pull", f'adb pull {partition_path} "{output_file}"'),
                    ("adb exec-out", f'adb exec-out "cat {partition_path}" > "{output_file}"'),
                    ("dd命令", f'adb shell "dd if={partition_path} bs=1024" > "{output_file}"'),
                    ("cat命令", f'adb shell "cat {partition_path}" > "{output_file}"')
                ]

                for method_name, cmd in backup_methods:
                    if backup_success:
                        break

                    try:
                        self.progress_updated.emit(
                            int((i / total_partitions) * 100),
                            f"尝试{method_name}: {partition_name}"
                        )

                        # 删除可能存在的空文件
                        if os.path.exists(output_file):
                            os.remove(output_file)

                        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)

                        # 检查备份是否成功
                        if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                            backup_success = True
                            backup_method = method_name
                            file_size = os.path.getsize(output_file)
                            self.progress_updated.emit(
                                int((i / total_partitions) * 100),
                                f"备份成功({method_name}): {partition_name} ({file_size} bytes)"
                            )
                        else:
                            self.progress_updated.emit(
                                int((i / total_partitions) * 100),
                                f"{method_name}失败: {partition_name}"
                            )
                    except subprocess.TimeoutExpired:
                        self.progress_updated.emit(
                            int((i / total_partitions) * 100),
                            f"{method_name}超时: {partition_name}"
                        )
                    except Exception as e:
                        self.progress_updated.emit(
                            int((i / total_partitions) * 100),
                            f"{method_name}异常: {partition_name} - {str(e)}"
                        )

                if not backup_success:
                    self.progress_updated.emit(
                        int((i / total_partitions) * 100),
                        f"备份失败: {partition_name} - 所有方法都失败"
                    )
                    # 删除空文件
                    if os.path.exists(output_file):
                        os.remove(output_file)
                    continue

                # 压缩文件
                if backup_success:
                    try:
                        self.progress_updated.emit(
                            int((i / total_partitions) * 100),
                            f"正在压缩: {partition_name}.img"
                        )

                        compressed_file = output_file + ".7z"
                        with py7zr.SevenZipFile(compressed_file, 'w', password=None) as archive:
                            archive.write(output_file, f"{partition_name}.img")

                        # 删除原始文件
                        if os.path.exists(output_file):
                            os.remove(output_file)

                        self.progress_updated.emit(
                            int((i / total_partitions) * 100),
                            f"压缩完成: {partition_name}.img.7z"
                        )
                    except Exception as e:
                        self.progress_updated.emit(
                            int((i / total_partitions) * 100),
                            f"压缩失败: {partition_name} - {str(e)}"
                        )
            
            self.progress_updated.emit(100, "备份完成")
            self.backup_finished.emit(True, "所有分区备份完成")
            
        except Exception as e:
            self.backup_finished.emit(False, f"备份过程出错: {str(e)}")
    
    def stop(self):
        """停止备份"""
        self.running = False

class MainWindow(QMainWindow):
    """主窗口"""
    def __init__(self):
        super().__init__()
        self.adb_manager = None  # 初始化为None，稍后创建
        self.backup_worker = None
        self.init_ui()
        self.apply_dracula_style()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("晶晨ADB备份工具 - By.举个🌰")
        self.setGeometry(100, 100, 800, 600)
        self.setWindowIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 连接区域
        connection_group = QGroupBox("设备连接")
        connection_layout = QGridLayout(connection_group)

        # IP地址输入
        connection_layout.addWidget(QLabel("设备IP:"), 0, 0)
        self.ip_input = QLineEdit("***************")
        self.ip_input.setPlaceholderText("请输入设备IP地址")
        connection_layout.addWidget(self.ip_input, 0, 1)

        # 端口输入
        connection_layout.addWidget(QLabel("端口:"), 0, 2)
        self.port_input = QLineEdit("5555")
        self.port_input.setPlaceholderText("端口")
        self.port_input.setMaximumWidth(80)
        connection_layout.addWidget(self.port_input, 0, 3)

        # 连接按钮和状态
        self.connect_btn = QPushButton("连接设备")
        self.connect_btn.clicked.connect(self.connect_device)
        connection_layout.addWidget(self.connect_btn, 0, 4)

        self.status_label = QLabel("未连接")
        connection_layout.addWidget(self.status_label, 0, 5)

        # 设置列拉伸
        connection_layout.setColumnStretch(1, 1)  # IP输入框拉伸
        connection_layout.setColumnStretch(5, 1)  # 状态标签拉伸
        
        # 设备信息区域
        info_group = QGroupBox("设备信息")
        info_layout = QVBoxLayout(info_group)
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(100)
        self.info_text.setReadOnly(True)
        info_layout.addWidget(self.info_text)
        
        # 分区列表区域
        partition_group = QGroupBox("分区列表")
        partition_layout = QVBoxLayout(partition_group)
        
        self.partition_table = QTableWidget()
        self.partition_table.setColumnCount(3)
        self.partition_table.setHorizontalHeaderLabels(["分区名称", "设备路径", "大小"])

        # 设置列宽自适应
        header = self.partition_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 分区名称列
        header.setSectionResizeMode(1, QHeaderView.Stretch)          # 设备路径列拉伸
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 大小列

        # 设置表格属性
        self.partition_table.setAlternatingRowColors(True)
        self.partition_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.partition_table.setShowGrid(True)
        partition_layout.addWidget(self.partition_table)
        
        # 操作区域
        operation_group = QGroupBox("备份操作")
        operation_layout = QVBoxLayout(operation_group)
        
        path_layout = QHBoxLayout()
        self.path_label = QLabel("保存路径: 未选择")
        self.select_path_btn = QPushButton("选择路径")
        self.select_path_btn.clicked.connect(self.select_output_path)
        path_layout.addWidget(self.path_label)
        path_layout.addWidget(self.select_path_btn)
        
        button_layout = QHBoxLayout()
        self.refresh_btn = QPushButton("刷新分区")
        self.refresh_btn.clicked.connect(self.refresh_partitions)
        self.backup_btn = QPushButton("开始备份")
        self.backup_btn.clicked.connect(self.start_backup)
        self.backup_btn.setEnabled(False)
        
        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.backup_btn)
        button_layout.addStretch()
        
        operation_layout.addLayout(path_layout)
        operation_layout.addLayout(button_layout)
        
        # 进度区域
        progress_group = QGroupBox("备份进度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_label = QLabel("等待开始...")
        
        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.progress_label)
        
        # 日志区域
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        # 添加到主布局
        main_layout.addWidget(connection_group)
        main_layout.addWidget(info_group)
        main_layout.addWidget(partition_group)
        main_layout.addWidget(operation_group)
        main_layout.addWidget(progress_group)
        main_layout.addWidget(log_group)
        
        self.output_dir = None
        
    def apply_dracula_style(self):
        """应用Dracula风格"""
        style = """
        QMainWindow {
            background-color: #282a36;
            color: #f8f8f2;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #6272a4;
            border-radius: 5px;
            margin-top: 1ex;
            color: #f8f8f2;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #bd93f9;
        }
        QPushButton {
            background-color: #44475a;
            border: 1px solid #6272a4;
            border-radius: 4px;
            padding: 8px;
            color: #f8f8f2;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #6272a4;
        }
        QPushButton:pressed {
            background-color: #bd93f9;
        }
        QPushButton:disabled {
            background-color: #44475a;
            color: #6272a4;
        }
        QTextEdit, QTableWidget {
            background-color: #44475a;
            border: 1px solid #6272a4;
            border-radius: 4px;
            color: #f8f8f2;
            selection-background-color: #bd93f9;
        }
        QTableWidget::item {
            padding: 5px;
        }
        QTableWidget::item:selected {
            background-color: #bd93f9;
        }
        QHeaderView::section {
            background-color: #6272a4;
            color: #f8f8f2;
            padding: 5px;
            border: none;
        }
        QProgressBar {
            border: 1px solid #6272a4;
            border-radius: 4px;
            text-align: center;
            color: #f8f8f2;
        }
        QProgressBar::chunk {
            background-color: #50fa7b;
            border-radius: 3px;
        }
        QLabel {
            color: #f8f8f2;
        }
        """
        self.setStyleSheet(style)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

    def connect_device(self):
        """连接设备"""
        # 获取用户输入的IP和端口
        device_ip = self.ip_input.text().strip()
        device_port = self.port_input.text().strip()

        # 验证输入
        if not device_ip:
            self.log_message("请输入设备IP地址")
            return

        if not device_port:
            device_port = "5555"  # 默认端口

        # 创建新的ADB管理器
        self.adb_manager = ADBManager(device_ip, device_port)

        self.connect_btn.setEnabled(False)
        self.connect_btn.setText("连接中...")
        self.log_message(f"正在连接设备 {device_ip}:{device_port}...")

        # 使用QThread替代普通线程
        self.connect_worker = ConnectWorker(self.adb_manager)
        self.connect_worker.finished.connect(self.on_connect_finished)
        self.connect_worker.start()

    def on_connect_finished(self, success, message):
        """连接完成回调"""
        self.connect_btn.setEnabled(True)
        self.connect_btn.setText("连接设备")

        if success:
            self.status_label.setText("✅ 已连接")
            self.status_label.setStyleSheet("color: #50fa7b;")
            self.log_message("设备连接成功")
            self.refresh_btn.setEnabled(True)

            # 获取设备信息
            self.update_device_info()
            self.refresh_partitions()
        else:
            self.status_label.setText("❌ 连接失败")
            self.status_label.setStyleSheet("color: #ff5555;")
            self.log_message(f"连接失败: {message}")

    def update_device_info(self):
        """更新设备信息"""
        self.info_worker = DeviceInfoWorker(self.adb_manager)
        self.info_worker.finished.connect(self.display_device_info)
        self.info_worker.start()

    def display_device_info(self, info):
        """显示设备信息"""
        if 'error' in info:
            self.info_text.setText(f"获取设备信息失败: {info['error']}")
        else:
            info_text = f"品牌: {info.get('brand', 'Unknown')}\n"
            info_text += f"型号: {info.get('model', 'Unknown')}\n"
            info_text += f"Android版本: {info.get('android_version', 'Unknown')}"
            self.info_text.setText(info_text)

    def refresh_partitions(self):
        """刷新分区列表"""
        if not self.adb_manager.connected:
            self.log_message("请先连接设备")
            return

        self.refresh_btn.setEnabled(False)
        self.refresh_btn.setText("刷新中...")
        self.log_message("正在获取分区列表...")

        self.partition_worker = PartitionWorker(self.adb_manager)
        self.partition_worker.finished.connect(self.update_partition_table)
        self.partition_worker.progress.connect(self.log_message)
        self.partition_worker.start()

    def update_partition_table(self, partitions):
        """更新分区表格"""
        self.refresh_btn.setEnabled(True)
        self.refresh_btn.setText("刷新分区")

        self.partition_table.setRowCount(len(partitions))

        for i, partition in enumerate(partitions):
            self.partition_table.setItem(i, 0, QTableWidgetItem(partition['name']))
            self.partition_table.setItem(i, 1, QTableWidgetItem(partition['path']))
            self.partition_table.setItem(i, 2, QTableWidgetItem(partition['size']))

        self.log_message(f"发现 {len(partitions)} 个分区")

        if len(partitions) > 0 and self.output_dir:
            self.backup_btn.setEnabled(True)

    def select_output_path(self):
        """选择输出路径"""
        dialog = QFileDialog()
        path = dialog.getExistingDirectory(self, "选择备份保存路径")

        if path:
            # 获取设备型号作为文件夹名
            device_info = self.adb_manager.get_device_info()
            model = device_info.get('model', 'Unknown_Device').replace(' ', '_')

            # 创建以设备型号命名的文件夹
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            folder_name = f"{model}_{timestamp}"
            self.output_dir = os.path.join(path, folder_name)

            try:
                os.makedirs(self.output_dir, exist_ok=True)
                self.path_label.setText(f"保存路径: {self.output_dir}")
                self.log_message(f"输出路径设置为: {self.output_dir}")

                if self.partition_table.rowCount() > 0:
                    self.backup_btn.setEnabled(True)
            except Exception as e:
                self.log_message(f"创建输出目录失败: {str(e)}")

    def start_backup(self):
        """开始备份"""
        if not self.adb_manager.connected:
            self.log_message("请先连接设备")
            return

        if not self.output_dir:
            self.log_message("请先选择保存路径")
            return

        if self.partition_table.rowCount() == 0:
            self.log_message("没有发现分区")
            return

        # 获取分区列表
        partitions = []
        for i in range(self.partition_table.rowCount()):
            name = self.partition_table.item(i, 0).text()
            path = self.partition_table.item(i, 1).text()
            partitions.append({'name': name, 'path': path})

        # 禁用按钮
        self.backup_btn.setEnabled(False)
        self.backup_btn.setText("备份中...")
        self.refresh_btn.setEnabled(False)

        # 启动备份线程
        self.backup_worker = BackupWorker(self.adb_manager, partitions, self.output_dir)
        self.backup_worker.progress_updated.connect(self.update_progress)
        self.backup_worker.backup_finished.connect(self.on_backup_finished)
        self.backup_worker.start()

        self.log_message("开始备份分区...")

    def update_progress(self, value, message):
        """更新进度"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(message)
        self.log_message(message)

    def on_backup_finished(self, success, message):
        """备份完成回调"""
        self.backup_btn.setEnabled(True)
        self.backup_btn.setText("开始备份")
        self.refresh_btn.setEnabled(True)

        if success:
            self.log_message("✅ 备份完成！")
            QMessageBox.information(self, "备份完成", f"所有分区已成功备份到:\n{self.output_dir}")
        else:
            self.log_message(f"❌ 备份失败: {message}")
            QMessageBox.warning(self, "备份失败", message)

    def closeEvent(self, event):
        """关闭事件"""
        if self.backup_worker and self.backup_worker.isRunning():
            reply = QMessageBox.question(self, "确认退出", "备份正在进行中，确定要退出吗？")
            if reply == QMessageBox.Yes:
                self.backup_worker.stop()
                self.backup_worker.wait(3000)  # 等待3秒
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("晶晨ADB备份工具")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("By.举个🌰")

    # 检查ADB是否可用
    try:
        result = subprocess.run("adb version", shell=True, capture_output=True, text=True, timeout=5)
        if result.returncode != 0:
            QMessageBox.critical(None, "错误", "未找到ADB工具，请确保ADB已安装并添加到系统PATH中。")
            sys.exit(1)
    except Exception as e:
        QMessageBox.critical(None, "错误", f"ADB检查失败: {str(e)}")
        sys.exit(1)

    window = MainWindow()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
