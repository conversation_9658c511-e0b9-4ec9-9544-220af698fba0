#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
晶晨线刷包自动制作工具
从ADB设备自动提取固件并制作线刷包
Copyright (c) 2025 By.举个🌰
"""

import sys
import os
import subprocess
import threading
import time
import json
import py7zr
import struct
import shutil
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

class FirmwareExtractor:
    """固件提取器"""
    def __init__(self):
        self.device_ip = "***************"
        self.device_port = "5555"
        self.connected = False
        self.device_info = {}
        
    def connect_device(self):
        """连接ADB设备"""
        try:
            # 先检查是否已经连接
            check_cmd = "adb devices"
            check_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=5)

            if check_result.returncode == 0 and f"{self.device_ip}:{self.device_port}" in check_result.stdout:
                if "device" in check_result.stdout:
                    self.connected = True
                    return True, "设备已连接"

            # 尝试连接
            cmd = f"adb connect {self.device_ip}:{self.device_port}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=15)

            if result.returncode == 0:
                output_lower = result.stdout.lower()
                if "connected" in output_lower or "already connected" in output_lower:
                    self.connected = True
                    return True, "设备连接成功"
                elif "cannot connect" in output_lower:
                    return False, "无法连接到设备，请检查IP地址和网络"
                else:
                    return False, f"连接状态不明确: {result.stdout}"
            else:
                return False, f"连接命令失败: {result.stderr}"

        except subprocess.TimeoutExpired:
            return False, "连接超时，请检查网络连接"
        except Exception as e:
            return False, f"连接异常: {str(e)}"
    
    def get_device_info(self):
        """获取设备信息"""
        if not self.connected:
            return {}
        
        try:
            info_commands = {
                'model': "adb shell getprop ro.product.model",
                'brand': "adb shell getprop ro.product.brand",
                'device': "adb shell getprop ro.product.device",
                'platform': "adb shell getprop ro.board.platform",
                'hardware': "adb shell getprop ro.hardware",
                'android_version': "adb shell getprop ro.build.version.release",
                'build_id': "adb shell getprop ro.build.id",
                'build_date': "adb shell getprop ro.build.date"
            }
            
            info = {}
            for key, cmd in info_commands.items():
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                info[key] = result.stdout.strip() if result.returncode == 0 else "Unknown"
            
            self.device_info = info
            return info
        except Exception as e:
            return {"error": str(e)}
    
    def get_partitions(self):
        """获取分区列表"""
        if not self.connected:
            return []

        try:
            partitions = []
            common_partitions = [
                'boot', 'bootloader', 'recovery', 'system', 'vendor',
                'product', 'logo', 'param', 'vbmeta', 'metadata',
                'cri_data', 'dtb', 'dtbo', 'misc', 'cache', 'userdata'
            ]

            # 优化：批量检查分区存在性
            for partition in common_partitions:
                found = False
                partition_path = ""
                size_bytes = 0

                # 优先检查 /dev/block/ 路径
                for path_prefix in ['/dev/block/', '/dev/']:
                    test_path = f"{path_prefix}{partition}"

                    # 使用更短的超时时间
                    cmd = f'adb shell "test -e {test_path} && echo exists || echo notfound"'
                    try:
                        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=3)

                        if result.returncode == 0 and 'exists' in result.stdout:
                            partition_path = test_path

                            # 获取分区大小
                            size_cmd = f'adb shell "blockdev --getsize64 {test_path} 2>/dev/null || echo 0"'
                            size_result = subprocess.run(size_cmd, shell=True, capture_output=True, text=True, timeout=3)

                            if size_result.returncode == 0:
                                size_output = size_result.stdout.strip()
                                if size_output.isdigit():
                                    size_bytes = int(size_output)
                                else:
                                    size_bytes = 0

                            found = True
                            break
                    except subprocess.TimeoutExpired:
                        continue
                    except Exception:
                        continue

                if found:
                    size_str = self._format_size(size_bytes) if size_bytes > 0 else "Unknown"
                    partitions.append({
                        'name': partition,
                        'path': partition_path,
                        'size': size_str,
                        'size_bytes': size_bytes
                    })

            return partitions
        except Exception as e:
            return []
    
    def _format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        size = float(size_bytes)
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        if unit_index == 0:
            return f"{int(size)} {units[unit_index]}"
        else:
            return f"{size:.1f} {units[unit_index]}"

class FirmwarePackager:
    """固件打包器"""
    def __init__(self, output_dir):
        self.output_dir = output_dir
        self.usb_dir = os.path.join(output_dir, "USB_Burning_Tool")
        
    def create_package_structure(self):
        """创建线刷包目录结构"""
        try:
            # 创建主目录
            os.makedirs(self.usb_dir, exist_ok=True)
            
            # 创建必要的子目录
            subdirs = ['platform', 'image']
            for subdir in subdirs:
                os.makedirs(os.path.join(self.usb_dir, subdir), exist_ok=True)
            
            return True
        except Exception as e:
            return False
    
    def process_dtb_file(self, dtb_source):
        """处理DTB文件"""
        try:
            dtb_target = os.path.join(self.usb_dir, "meson1.dtb")
            
            if os.path.exists(dtb_source):
                # 检查是否是有效的DTB文件
                with open(dtb_source, 'rb') as f:
                    data = f.read()
                
                # 查找DTB magic number
                dtb_magic = b'\xd0\x0d\xfe\xed'
                pos = data.find(dtb_magic)
                
                if pos != -1:
                    # 提取DTB数据
                    dtb_data = data[pos:]
                    if len(dtb_data) >= 8:
                        totalsize = struct.unpack('>I', dtb_data[4:8])[0]
                        if totalsize <= len(dtb_data) and totalsize > 0:
                            with open(dtb_target, 'wb') as f:
                                f.write(dtb_data[:totalsize])
                            return True, f"DTB文件处理成功: {totalsize} bytes"
                
                # 如果找不到DTB magic，直接复制
                shutil.copy2(dtb_source, dtb_target)
                return True, "DTB文件复制成功"
            else:
                return False, "DTB源文件不存在"
                
        except Exception as e:
            return False, f"DTB处理失败: {str(e)}"
    
    def create_image_cfg(self, partitions, device_info):
        """创建image.cfg配置文件"""
        try:
            cfg_content = []
            cfg_content.append("[LIST_NORMAL]")
            cfg_content.append(f"file=\"meson1.dtb\",main_type=\"dtb\",sub_type=\"meson1\"")
            
            # 添加分区配置
            for partition in partitions:
                name = partition['name']
                if name in ['boot', 'bootloader', 'recovery', 'system', 'vendor', 'product']:
                    cfg_content.append(f"file=\"{name}.PARTITION\",main_type=\"PARTITION\",sub_type=\"{name}\"")
            
            cfg_content.append("")
            cfg_content.append("[LIST_VERIFY]")
            cfg_content.append("dtb")
            
            for partition in partitions:
                name = partition['name']
                if name in ['boot', 'bootloader', 'recovery', 'system', 'vendor', 'product']:
                    cfg_content.append(name)
            
            # 写入配置文件
            cfg_file = os.path.join(self.usb_dir, "image.cfg")
            with open(cfg_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(cfg_content))
            
            return True, "image.cfg创建成功"
            
        except Exception as e:
            return False, f"image.cfg创建失败: {str(e)}"
    
    def create_platform_cfg(self, device_info):
        """创建platform.cfg配置文件"""
        try:
            cfg_content = []
            cfg_content.append("[PLATFORM]")
            cfg_content.append(f"name={device_info.get('model', 'Unknown')}")
            cfg_content.append(f"brand={device_info.get('brand', 'Unknown')}")
            cfg_content.append(f"chip={device_info.get('hardware', 'amlogic')}")
            cfg_content.append(f"platform={device_info.get('platform', 'amlogic')}")
            cfg_content.append(f"android_version={device_info.get('android_version', '9')}")
            cfg_content.append(f"build_id={device_info.get('build_id', 'Unknown')}")
            cfg_content.append("")
            cfg_content.append("[BURNING]")
            cfg_content.append("erase_bootloader=true")
            cfg_content.append("erase_flash=false")
            cfg_content.append("reboot=true")
            
            # 写入配置文件
            cfg_file = os.path.join(self.usb_dir, "platform", "platform.cfg")
            with open(cfg_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(cfg_content))
            
            return True, "platform.cfg创建成功"
            
        except Exception as e:
            return False, f"platform.cfg创建失败: {str(e)}"

class ConnectWorker(QThread):
    """连接工作线程"""
    finished = Signal(bool, str)
    progress = Signal(str)

    def __init__(self, extractor):
        super().__init__()
        self.extractor = extractor

    def run(self):
        """执行连接"""
        try:
            self.progress.emit("开始连接设备...")
            success, message = self.extractor.connect_device()
            self.progress.emit(f"连接结果: {success}, {message}")
            self.finished.emit(success, message)
        except Exception as e:
            error_msg = f"连接异常: {str(e)}"
            self.progress.emit(error_msg)
            self.finished.emit(False, error_msg)

class PartitionWorker(QThread):
    """分区获取工作线程"""
    finished = Signal(list)
    progress = Signal(str)

    def __init__(self, extractor):
        super().__init__()
        self.extractor = extractor

    def run(self):
        """执行分区获取"""
        try:
            self.progress.emit("开始扫描分区...")
            partitions = self.extractor.get_partitions()
            self.progress.emit(f"分区扫描完成，发现 {len(partitions)} 个分区")
            self.finished.emit(partitions)
        except Exception as e:
            self.progress.emit(f"分区扫描异常: {str(e)}")
            self.finished.emit([])

class ExtractWorker(QThread):
    """提取工作线程"""
    progress_updated = Signal(int, str)
    extract_finished = Signal(bool, str)
    
    def __init__(self, extractor, packager, partitions, output_dir):
        super().__init__()
        self.extractor = extractor
        self.packager = packager
        self.partitions = partitions
        self.output_dir = output_dir
        self.running = True
        
    def run(self):
        """执行提取和打包"""
        try:
            total_steps = len(self.partitions) + 5  # 分区数量 + 额外步骤
            current_step = 0
            
            # 步骤1: 创建目录结构
            self.progress_updated.emit(
                int((current_step / total_steps) * 100),
                "创建线刷包目录结构..."
            )
            
            if not self.packager.create_package_structure():
                self.extract_finished.emit(False, "创建目录结构失败")
                return
            
            current_step += 1
            
            # 步骤2: 提取DTB文件
            self.progress_updated.emit(
                int((current_step / total_steps) * 100),
                "提取DTB文件..."
            )
            
            dtb_file = os.path.join(self.output_dir, "device_dtb.dtb")
            cmd = f'adb pull /dev/dtb "{dtb_file}"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0 and os.path.exists(dtb_file):
                success, msg = self.packager.process_dtb_file(dtb_file)
                if not success:
                    self.progress_updated.emit(
                        int((current_step / total_steps) * 100),
                        f"DTB处理警告: {msg}"
                    )
            
            current_step += 1
            
            # 步骤3: 提取分区文件
            for i, partition in enumerate(self.partitions):
                if not self.running:
                    break
                
                partition_name = partition['name']
                partition_path = partition['path']
                
                self.progress_updated.emit(
                    int((current_step / total_steps) * 100),
                    f"提取分区: {partition_name}"
                )
                
                # 提取分区
                output_file = os.path.join(self.packager.usb_dir, f"{partition_name}.PARTITION")
                
                # 尝试多种提取方法
                extract_success = False
                methods = [
                    ("adb pull", f'adb pull {partition_path} "{output_file}"'),
                    ("adb exec-out", f'adb exec-out "cat {partition_path}" > "{output_file}"')
                ]
                
                for method_name, cmd in methods:
                    try:
                        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
                        
                        if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                            file_size = os.path.getsize(output_file)
                            self.progress_updated.emit(
                                int((current_step / total_steps) * 100),
                                f"提取成功({method_name}): {partition_name} ({self.extractor._format_size(file_size)})"
                            )
                            extract_success = True
                            break
                    except:
                        continue
                
                if not extract_success:
                    self.progress_updated.emit(
                        int((current_step / total_steps) * 100),
                        f"提取失败: {partition_name}"
                    )
                
                current_step += 1
            
            # 步骤4: 创建配置文件
            self.progress_updated.emit(
                int((current_step / total_steps) * 100),
                "创建配置文件..."
            )
            
            # 创建image.cfg
            success, msg = self.packager.create_image_cfg(self.partitions, self.extractor.device_info)
            if not success:
                self.progress_updated.emit(
                    int((current_step / total_steps) * 100),
                    f"配置文件警告: {msg}"
                )
            
            # 创建platform.cfg
            success, msg = self.packager.create_platform_cfg(self.extractor.device_info)
            if not success:
                self.progress_updated.emit(
                    int((current_step / total_steps) * 100),
                    f"平台配置警告: {msg}"
                )
            
            current_step += 1
            
            # 步骤5: 创建说明文件
            self.progress_updated.emit(
                int((current_step / total_steps) * 100),
                "创建说明文件..."
            )
            
            readme_content = f"""# {self.extractor.device_info.get('model', 'Unknown')} 线刷包

## 设备信息
- 型号: {self.extractor.device_info.get('model', 'Unknown')}
- 品牌: {self.extractor.device_info.get('brand', 'Unknown')}
- 平台: {self.extractor.device_info.get('platform', 'Unknown')}
- Android版本: {self.extractor.device_info.get('android_version', 'Unknown')}
- 构建ID: {self.extractor.device_info.get('build_id', 'Unknown')}

## 使用方法
1. 使用晶晨USB Burning Tool
2. 选择此线刷包目录
3. 按照工具提示进行刷机

## 注意事项
- 刷机有风险，请确保设备型号匹配
- 刷机前请备份重要数据
- 刷机过程中请勿断电或拔线

## 制作信息
- 制作时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
- 制作工具: 晶晨线刷包自动制作工具 v1.0
- Copyright (c) 2025 By.举个🌰
"""
            
            readme_file = os.path.join(self.packager.usb_dir, "README.md")
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            self.progress_updated.emit(100, "线刷包制作完成")
            self.extract_finished.emit(True, f"线刷包已创建: {self.packager.usb_dir}")
            
        except Exception as e:
            self.extract_finished.emit(False, f"制作过程出错: {str(e)}")
    
    def stop(self):
        """停止提取"""
        self.running = False

class MainWindow(QMainWindow):
    """主窗口"""
    def __init__(self):
        super().__init__()
        self.extractor = FirmwareExtractor()
        self.packager = None
        self.extract_worker = None
        self.init_ui()
        self.apply_dracula_style()

    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("晶晨线刷包自动制作工具 - By.举个🌰")
        self.setGeometry(100, 100, 900, 700)
        self.setWindowIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))

        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 标题区域
        title_label = QLabel("🔧 晶晨线刷包自动制作工具")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #bd93f9; padding: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # 连接区域
        connection_group = QGroupBox("设备连接")
        connection_layout = QHBoxLayout(connection_group)

        self.ip_label = QLabel(f"设备IP: {self.extractor.device_ip}")
        self.connect_btn = QPushButton("连接设备")
        self.connect_btn.clicked.connect(self.connect_device)
        self.status_label = QLabel("未连接")

        connection_layout.addWidget(self.ip_label)
        connection_layout.addWidget(self.connect_btn)
        connection_layout.addWidget(self.status_label)
        connection_layout.addStretch()

        # 设备信息区域
        info_group = QGroupBox("设备信息")
        info_layout = QVBoxLayout(info_group)
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(120)
        self.info_text.setReadOnly(True)
        info_layout.addWidget(self.info_text)

        # 分区列表区域
        partition_group = QGroupBox("分区列表")
        partition_layout = QVBoxLayout(partition_group)

        self.partition_table = QTableWidget()
        self.partition_table.setColumnCount(3)
        self.partition_table.setHorizontalHeaderLabels(["分区名称", "设备路径", "大小"])

        # 设置列宽自适应
        header = self.partition_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)

        self.partition_table.setAlternatingRowColors(True)
        self.partition_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        partition_layout.addWidget(self.partition_table)

        # 操作区域
        operation_group = QGroupBox("线刷包制作")
        operation_layout = QVBoxLayout(operation_group)

        # 输出路径选择
        path_layout = QHBoxLayout()
        self.path_label = QLabel("输出路径: 未选择")
        self.select_path_btn = QPushButton("选择输出路径")
        self.select_path_btn.clicked.connect(self.select_output_path)
        path_layout.addWidget(self.path_label)
        path_layout.addWidget(self.select_path_btn)

        # 操作按钮
        button_layout = QHBoxLayout()
        self.refresh_btn = QPushButton("刷新分区")
        self.refresh_btn.clicked.connect(self.refresh_partitions)
        self.refresh_btn.setEnabled(False)

        self.create_package_btn = QPushButton("🚀 一键制作线刷包")
        self.create_package_btn.clicked.connect(self.create_firmware_package)
        self.create_package_btn.setEnabled(False)
        self.create_package_btn.setStyleSheet("font-weight: bold; font-size: 14px;")

        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.create_package_btn)
        button_layout.addStretch()

        operation_layout.addLayout(path_layout)
        operation_layout.addLayout(button_layout)

        # 进度区域
        progress_group = QGroupBox("制作进度")
        progress_layout = QVBoxLayout(progress_group)

        self.progress_bar = QProgressBar()
        self.progress_label = QLabel("等待开始...")

        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.progress_label)

        # 日志区域
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        # 添加到主布局
        main_layout.addWidget(connection_group)
        main_layout.addWidget(info_group)
        main_layout.addWidget(partition_group)
        main_layout.addWidget(operation_group)
        main_layout.addWidget(progress_group)
        main_layout.addWidget(log_group)

        self.output_dir = None
        self.partitions = []

    def apply_dracula_style(self):
        """应用Dracula风格"""
        style = """
        QMainWindow {
            background-color: #282a36;
            color: #f8f8f2;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #6272a4;
            border-radius: 5px;
            margin-top: 1ex;
            color: #f8f8f2;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #bd93f9;
        }
        QPushButton {
            background-color: #44475a;
            border: 1px solid #6272a4;
            border-radius: 4px;
            padding: 8px;
            color: #f8f8f2;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #6272a4;
        }
        QPushButton:pressed {
            background-color: #bd93f9;
        }
        QPushButton:disabled {
            background-color: #44475a;
            color: #6272a4;
        }
        QTextEdit, QTableWidget {
            background-color: #44475a;
            border: 1px solid #6272a4;
            border-radius: 4px;
            color: #f8f8f2;
            selection-background-color: #bd93f9;
        }
        QTableWidget::item {
            padding: 5px;
        }
        QTableWidget::item:selected {
            background-color: #bd93f9;
        }
        QHeaderView::section {
            background-color: #6272a4;
            color: #f8f8f2;
            padding: 5px;
            border: none;
        }
        QProgressBar {
            border: 1px solid #6272a4;
            border-radius: 4px;
            text-align: center;
            color: #f8f8f2;
        }
        QProgressBar::chunk {
            background-color: #50fa7b;
            border-radius: 3px;
        }
        QLabel {
            color: #f8f8f2;
        }
        """
        self.setStyleSheet(style)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

    def connect_device(self):
        """连接设备"""
        self.connect_btn.setEnabled(False)
        self.connect_btn.setText("连接中...")
        self.log_message("正在连接设备...")

        # 使用QThread替代普通线程
        self.connect_worker = ConnectWorker(self.extractor)
        self.connect_worker.finished.connect(self.on_connect_finished)
        self.connect_worker.progress.connect(self.log_message)
        self.connect_worker.start()

    def on_connect_finished(self, success, message):
        """连接完成回调"""
        try:
            self.connect_btn.setEnabled(True)
            self.connect_btn.setText("连接设备")

            if success:
                self.status_label.setText("✅ 已连接")
                self.status_label.setStyleSheet("color: #50fa7b;")
                self.log_message("设备连接成功")
                self.refresh_btn.setEnabled(True)

                # 获取设备信息
                self.update_device_info()
                self.refresh_partitions()
            else:
                self.status_label.setText("❌ 连接失败")
                self.status_label.setStyleSheet("color: #ff5555;")
                self.log_message(f"连接失败: {message}")
        except Exception as e:
            self.log_message(f"连接回调异常: {str(e)}")
            self.connect_btn.setEnabled(True)
            self.connect_btn.setText("连接设备")

    def update_device_info(self):
        """更新设备信息"""
        def get_info_thread():
            info = self.extractor.get_device_info()
            QTimer.singleShot(0, lambda: self.display_device_info(info))

        threading.Thread(target=get_info_thread, daemon=True).start()

    def display_device_info(self, info):
        """显示设备信息"""
        if 'error' in info:
            self.info_text.setText(f"获取设备信息失败: {info['error']}")
        else:
            info_text = f"型号: {info.get('model', 'Unknown')}\n"
            info_text += f"品牌: {info.get('brand', 'Unknown')}\n"
            info_text += f"平台: {info.get('platform', 'Unknown')}\n"
            info_text += f"硬件: {info.get('hardware', 'Unknown')}\n"
            info_text += f"Android版本: {info.get('android_version', 'Unknown')}\n"
            info_text += f"构建ID: {info.get('build_id', 'Unknown')}"
            self.info_text.setText(info_text)

    def refresh_partitions(self):
        """刷新分区列表"""
        if not self.extractor.connected:
            self.log_message("请先连接设备")
            return

        self.refresh_btn.setEnabled(False)
        self.refresh_btn.setText("刷新中...")
        self.log_message("正在获取分区列表...")

        # 使用QThread替代普通线程
        self.partition_worker = PartitionWorker(self.extractor)
        self.partition_worker.finished.connect(self.update_partition_table)
        self.partition_worker.progress.connect(self.log_message)
        self.partition_worker.start()

    def update_partition_table(self, partitions):
        """更新分区表格"""
        try:
            self.refresh_btn.setEnabled(True)
            self.refresh_btn.setText("刷新分区")

            self.partitions = partitions
            self.partition_table.setRowCount(len(partitions))

            for i, partition in enumerate(partitions):
                self.partition_table.setItem(i, 0, QTableWidgetItem(partition['name']))
                self.partition_table.setItem(i, 1, QTableWidgetItem(partition['path']))
                self.partition_table.setItem(i, 2, QTableWidgetItem(partition['size']))

            self.log_message(f"发现 {len(partitions)} 个分区")

            if len(partitions) > 0 and self.output_dir:
                self.create_package_btn.setEnabled(True)
        except Exception as e:
            self.log_message(f"更新分区表格异常: {str(e)}")
            self.refresh_btn.setEnabled(True)
            self.refresh_btn.setText("刷新分区")

    def select_output_path(self):
        """选择输出路径"""
        dialog = QFileDialog()
        path = dialog.getExistingDirectory(self, "选择线刷包输出路径")

        if path:
            # 创建以设备型号和时间戳命名的文件夹
            device_model = self.extractor.device_info.get('model', 'Unknown_Device').replace(' ', '_')
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            folder_name = f"{device_model}_Firmware_{timestamp}"
            self.output_dir = os.path.join(path, folder_name)

            try:
                os.makedirs(self.output_dir, exist_ok=True)
                self.path_label.setText(f"输出路径: {self.output_dir}")
                self.log_message(f"输出路径设置为: {self.output_dir}")

                if len(self.partitions) > 0:
                    self.create_package_btn.setEnabled(True)
            except Exception as e:
                self.log_message(f"创建输出目录失败: {str(e)}")

    def create_firmware_package(self):
        """创建固件包"""
        if not self.extractor.connected:
            self.log_message("请先连接设备")
            return

        if not self.output_dir:
            self.log_message("请先选择输出路径")
            return

        if len(self.partitions) == 0:
            self.log_message("没有发现分区")
            return

        # 创建打包器
        self.packager = FirmwarePackager(self.output_dir)

        # 禁用按钮
        self.create_package_btn.setEnabled(False)
        self.create_package_btn.setText("制作中...")
        self.refresh_btn.setEnabled(False)

        # 启动提取线程
        self.extract_worker = ExtractWorker(
            self.extractor,
            self.packager,
            self.partitions,
            self.output_dir
        )
        self.extract_worker.progress_updated.connect(self.update_progress)
        self.extract_worker.extract_finished.connect(self.on_extract_finished)
        self.extract_worker.start()

        self.log_message("开始制作线刷包...")

    def update_progress(self, value, message):
        """更新进度"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(message)
        self.log_message(message)

    def on_extract_finished(self, success, message):
        """提取完成回调"""
        self.create_package_btn.setEnabled(True)
        self.create_package_btn.setText("🚀 一键制作线刷包")
        self.refresh_btn.setEnabled(True)

        if success:
            self.log_message("✅ 线刷包制作完成！")

            # 显示完成对话框
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("制作完成")
            msg_box.setText("线刷包制作完成！")
            msg_box.setDetailedText(f"输出路径: {message}")
            msg_box.setStandardButtons(QMessageBox.Ok | QMessageBox.Open)
            msg_box.setDefaultButton(QMessageBox.Open)

            result = msg_box.exec()
            if result == QMessageBox.Open:
                # 打开输出目录
                if os.name == 'nt':  # Windows
                    os.startfile(self.packager.usb_dir)
                else:  # Linux/Mac
                    subprocess.run(['xdg-open', self.packager.usb_dir])
        else:
            self.log_message(f"❌ 线刷包制作失败: {message}")
            QMessageBox.warning(self, "制作失败", message)

    def closeEvent(self, event):
        """关闭事件"""
        if self.extract_worker and self.extract_worker.isRunning():
            reply = QMessageBox.question(self, "确认退出", "线刷包制作正在进行中，确定要退出吗？")
            if reply == QMessageBox.Yes:
                self.extract_worker.stop()
                self.extract_worker.wait(3000)
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("晶晨线刷包自动制作工具")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("By.举个🌰")

    # 检查ADB是否可用
    try:
        result = subprocess.run("adb version", shell=True, capture_output=True, text=True, timeout=5)
        if result.returncode != 0:
            QMessageBox.critical(None, "错误", "未找到ADB工具，请确保ADB已安装并添加到系统PATH中。")
            sys.exit(1)
    except Exception as e:
        QMessageBox.critical(None, "错误", f"ADB检查失败: {str(e)}")
        sys.exit(1)

    window = MainWindow()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
