[{"classes": [{"className": "QQuickAnimatedNode", "lineNumber": 28, "object": true, "qualifiedClassName": "QQuickAnimatedNode", "signals": [{"access": "public", "index": 0, "name": "started", "returnType": "void"}, {"access": "public", "index": 1, "name": "stopped", "returnType": "void"}], "slots": [{"access": "private", "index": 2, "name": "advance", "returnType": "void"}, {"access": "private", "index": 3, "name": "update", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QSGTransformNode"}]}], "inputFile": "qquickanimatednode_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Check<PERSON>abel"}, {"name": "QML.AddedInVersion", "value": "515"}], "className": "QQuickCheckLabel", "lineNumber": 23, "object": true, "qualifiedClassName": "QQuickCheckLabel", "superClasses": [{"access": "public", "name": "QQuickText"}]}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QQuickText"}, {"name": "QML.AddedInVersion", "value": "515"}], "className": "QQuickTextForeign", "gadget": true, "lineNumber": 33, "qualifiedClassName": "QQuickTextForeign"}], "inputFile": "qquickchecklabel_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ClippedText"}, {"name": "QML.AddedInVersion", "value": "514"}], "className": "QQuickClippedText", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "clipX", "read": "clipX", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setClipX"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "clipY", "read": "clipY", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setClipY"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "clipWidth", "read": "clipWidth", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "clipHeight", "read": "clipHeight", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setClipHeight"}], "qualifiedClassName": "QQuickClippedText", "superClasses": [{"access": "public", "name": "QQuickText"}]}], "inputFile": "qquickclippedtext_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Color"}, {"name": "QML.Singleton", "value": "true"}, {"name": "QML.AddedInVersion", "value": "515"}], "className": "QQuickColor", "lineNumber": 25, "methods": [{"access": "public", "arguments": [{"name": "color", "type": "QColor"}, {"name": "opacity", "type": "qreal"}], "index": 0, "isConst": true, "name": "transparent", "returnType": "QColor"}, {"access": "public", "arguments": [{"name": "a", "type": "QColor"}, {"name": "b", "type": "QColor"}, {"name": "factor", "type": "qreal"}], "index": 1, "isConst": true, "name": "blend", "returnType": "QColor"}], "object": true, "qualifiedClassName": "QQuickColor", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickcolor_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ColorImage"}, {"name": "QML.AddedInVersion", "value": "515"}], "className": "QQuickColorImage", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "reset": "resetColor", "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "defaultColor", "notify": "defaultColorChanged", "read": "defaultColor", "required": false, "reset": "resetDefaultColor", "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setDefaultColor"}], "qualifiedClassName": "QQuickColorImage", "signals": [{"access": "public", "index": 0, "name": "colorChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "defaultColorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickImage"}]}], "inputFile": "qquickcolorimage_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "IconImage"}, {"name": "QML.AddedInVersion", "value": "515"}], "className": "QQuickIconImage", "lineNumber": 25, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}], "qualifiedClassName": "QQuickIconImage", "signals": [{"access": "public", "index": 0, "name": "nameChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "colorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickImage"}]}], "inputFile": "qquickiconimage_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "IconLabel"}, {"name": "QML.AddedInVersion", "value": "515"}], "className": "QQuickIconLabel", "enums": [{"isClass": false, "isFlag": false, "name": "Display", "values": ["IconOnly", "TextOnly", "TextBesideIcon", "TextUnderIcon"]}], "lineNumber": 26, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "icon", "read": "icon", "required": false, "scriptable": true, "stored": true, "type": "QQuickIcon", "user": false, "write": "setIcon"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "text", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "font", "read": "font", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setFont"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "color", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "display", "read": "display", "required": false, "scriptable": true, "stored": true, "type": "Display", "user": false, "write": "setDisplay"}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "spacing", "read": "spacing", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setSpacing"}, {"constant": false, "designable": true, "final": true, "index": 6, "name": "mirrored", "read": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "set<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": true, "index": 7, "name": "alignment", "read": "alignment", "required": false, "scriptable": true, "stored": true, "type": "Qt::Alignment", "user": false, "write": "setAlignment"}, {"constant": false, "designable": true, "final": true, "index": 8, "name": "topPadding", "read": "topPadding", "required": false, "reset": "resetTopPadding", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setTopPadding"}, {"constant": false, "designable": true, "final": true, "index": 9, "name": "leftPadding", "read": "leftPadding", "required": false, "reset": "resetLeftPadding", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setLeftPadding"}, {"constant": false, "designable": true, "final": true, "index": 10, "name": "rightPadding", "read": "rightPadding", "required": false, "reset": "resetRightPadding", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setRightPadding"}, {"constant": false, "designable": true, "final": true, "index": 11, "name": "bottomPadding", "read": "bottomPadding", "required": false, "reset": "resetBottomPadding", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setBottomPadding"}], "qualifiedClassName": "QQuickIconLabel", "superClasses": [{"access": "public", "name": "QQuickItem"}]}], "inputFile": "qquickiconlabel_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ImageSelector"}, {"name": "QML.AddedInVersion", "value": "515"}], "className": "QQuickImageSelector", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}], [{"className": "QQmlPropertyValueInterceptor", "id": "\"org.qt-project.Qt.QQmlPropertyValueInterceptor\""}]], "lineNumber": 29, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "path", "read": "path", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "set<PERSON>ath"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "states", "read": "states", "required": false, "scriptable": true, "stored": true, "type": "QVariantList", "user": false, "write": "setStates"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "separator", "read": "separator", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setSeparator"}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "cache", "read": "cache", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setCache"}], "qualifiedClassName": "QQuickImageSelector", "signals": [{"access": "public", "index": 0, "name": "sourceChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}, {"access": "public", "name": "QQmlPropertyValueInterceptor"}]}, {"classInfos": [{"name": "QML.Element", "value": "NinePatchImageSelector"}, {"name": "QML.AddedInVersion", "value": "515"}], "className": "QQuickNinePatchImageSelector", "lineNumber": 93, "object": true, "qualifiedClassName": "QQuickNinePatchImageSelector", "superClasses": [{"access": "public", "name": "QQuickImageSelector"}]}, {"classInfos": [{"name": "QML.Element", "value": "AnimatedImageSelector"}, {"name": "QML.AddedInVersion", "value": "515"}], "className": "QQuickAnimatedImageSelector", "lineNumber": 106, "object": true, "qualifiedClassName": "QQuickAnimatedImageSelector", "superClasses": [{"access": "public", "name": "QQuickImageSelector"}]}], "inputFile": "qquickimageselector_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ItemGroup"}, {"name": "QML.AddedInVersion", "value": "514"}], "className": "QQuickItemGroup", "lineNumber": 24, "object": true, "qualifiedClassName": "QQuickItemGroup", "superClasses": [{"access": "public", "name": "QQuickImplicitSizeItem"}, {"access": "protected", "name": "QQuickItemChangeListener"}]}], "inputFile": "qquickitemgroup_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MnemonicLabel"}, {"name": "QML.AddedInVersion", "value": "515"}], "className": "QQuickMnemonicLabel", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "text", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "mnemonicVisible", "read": "isMnemonicVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMnemonicVisible"}], "qualifiedClassName": "QQuickMnemonicLabel", "superClasses": [{"access": "public", "name": "QQuickText"}]}], "inputFile": "qquickmnemoniclabel_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "NinePatchImage"}, {"name": "QML.AddedInVersion", "value": "515"}], "className": "QQuickNinePatchImage", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "topPadding", "notify": "topPaddingChanged", "read": "topPadding", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "leftPadding", "notify": "leftPaddingChanged", "read": "leftPadding", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "rightPadding", "notify": "rightPaddingChanged", "read": "rightPadding", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "bottomPadding", "notify": "bottomPaddingChanged", "read": "bottomPadding", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "topInset", "notify": "topInsetChanged", "read": "topInset", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "leftInset", "notify": "leftInsetChanged", "read": "leftInset", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": true, "index": 6, "name": "rightInset", "notify": "rightInsetChanged", "read": "rightInset", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": true, "index": 7, "name": "bottomInset", "notify": "bottomInsetChanged", "read": "bottomInset", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QQuickNinePatchImage", "signals": [{"access": "public", "index": 0, "name": "topPaddingChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "leftPaddingChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "rightPaddingChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "bottomPaddingChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "topInsetChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "leftInsetChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "rightInsetChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "bottomInsetChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickImage"}]}], "inputFile": "qquickninepatchimage_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "PaddedRectangle"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickPaddedRectangle", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "padding", "notify": "paddingChanged", "read": "padding", "required": false, "reset": "resetPadding", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setPadding"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "topPadding", "notify": "topPaddingChanged", "read": "topPadding", "required": false, "reset": "resetTopPadding", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setTopPadding"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "leftPadding", "notify": "leftPaddingChanged", "read": "leftPadding", "required": false, "reset": "resetLeftPadding", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setLeftPadding"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "rightPadding", "notify": "rightPaddingChanged", "read": "rightPadding", "required": false, "reset": "resetRightPadding", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setRightPadding"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "bottomPadding", "notify": "bottomPaddingChanged", "read": "bottomPadding", "required": false, "reset": "resetBottomPadding", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setBottomPadding"}], "qualifiedClassName": "QQuickPaddedRectangle", "signals": [{"access": "public", "index": 0, "name": "paddingChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "topPaddingChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "leftPaddingChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "rightPaddingChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "bottomPaddingChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickRectangle"}]}], "inputFile": "qquickpaddedrectangle_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "PlaceholderText"}, {"name": "QML.AddedInVersion", "value": "514"}], "className": "QQuickPlaceholderText", "lineNumber": 23, "object": true, "qualifiedClassName": "QQuickPlaceholderText", "slots": [{"access": "private", "index": 0, "name": "updateAlignment", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickText"}]}], "inputFile": "qquickplaceholdertext_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Extended", "value": "QPlatformTheme"}, {"name": "QML.ExtensionIsNamespace", "value": "true"}, {"name": "QML.Element", "value": "PlatformTheme"}, {"name": "QML.Singleton", "value": "true"}, {"name": "QML.AddedInVersion", "value": "1539"}], "className": "QQuickPlatformTheme", "lineNumber": 25, "methods": [{"access": "public", "arguments": [{"name": "themeHint", "type": "QPlatformTheme::ThemeHint"}], "index": 0, "isConst": true, "name": "themeHint", "returnType": "Q<PERSON><PERSON><PERSON>"}], "object": true, "qualifiedClassName": "QQuickPlatformTheme", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickplatformtheme_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "TumblerView"}, {"name": "QML.AddedInVersion", "value": "513"}], "className": "QQuickTumblerView", "lineNumber": 31, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "model", "notify": "modelChanged", "read": "model", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setModel"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "delegate", "notify": "delegate<PERSON><PERSON><PERSON>", "read": "delegate", "required": false, "scriptable": true, "stored": true, "type": "QQmlComponent*", "user": false, "write": "setDelegate"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "path", "notify": "pathChanged", "read": "path", "required": false, "scriptable": true, "stored": true, "type": "QQuickPath*", "user": false, "write": "set<PERSON>ath"}], "qualifiedClassName": "QQuickTumblerView", "signals": [{"access": "public", "index": 0, "name": "modelChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "delegate<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 2, "name": "pathChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}]}], "inputFile": "qquicktumblerview_p.h", "outputRevision": 69}]