import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "tcinputmethod_p.h"
        name: "QtVirtualKeyboard::TCInputMethod"
        accessSemantics: "reference"
        prototype: "QVirtualKeyboardAbstractInputMethod"
        exports: [
            "QtQuick.VirtualKeyboard.Plugins.TCIme/TCInputMethod 2.0",
            "QtQuick.VirtualKeyboard.Plugins.TCIme/TCInputMethod 6.0",
            "QtQuick.VirtualKeyboard.Plugins.TCIme/TCInputMethod 6.1"
        ]
        exportMetaObjectRevisions: [512, 1536, 1537]
        Property {
            name: "simplified"
            type: "bool"
            read: "simplified"
            write: "setSimplified"
            notify: "simplifiedChanged"
            index: 0
        }
        Signal { name: "simplifiedChanged" }
    }
}
