# 晶晨本地刷机包制作工具 - 修复版使用指南

**Copyright (c) 2025 By.举个🌰**

## 🎯 修复内容

### ✅ **配置文件格式修复**
根据USB Burning Tool的错误提示，我已经修复了配置文件格式问题：

1. **image.cfg格式修复**:
   - 使用制表符分隔字段（而不是逗号）
   - 分区文件放在`[LIST_VERIFY]`部分
   - 添加`platform.conf`到`[LIST_NORMAL]`部分
   - 按字母顺序排列分区

2. **platform.conf位置修复**:
   - 文件放在根目录（与image.cfg同级）
   - 而不是platform子目录

3. **文件结构标准化**:
   - 完全符合USB Burning Tool的期望格式
   - 解决"生成工作流程XML失败"问题
   - 解决"软件校验不过"问题

## 📁 正确的输出结构

```
CM311-1e_Firmware_20250122_170000/
└── USB_Burning_Tool/
    ├── DDR.USB                 # DDR初始化文件
    ├── UBOOT.USB              # U-Boot引导文件
    ├── meson1.dtb             # 设备树文件
    ├── platform.conf          # 平台配置文件（根目录）
    ├── image.cfg              # 镜像配置文件
    ├── boot.PARTITION         # 启动分区
    ├── bootloader.PARTITION   # 引导加载器分区
    ├── recovery.PARTITION     # 恢复分区
    ├── system.PARTITION       # 系统分区
    ├── vendor.PARTITION       # 厂商分区
    ├── product.PARTITION      # 产品分区
    ├── logo.PARTITION         # Logo分区
    ├── param.PARTITION        # 参数分区
    ├── vbmeta.PARTITION       # 验证启动分区
    ├── metadata.PARTITION     # 元数据分区
    ├── cri_data.PARTITION     # 关键数据分区
    ├── misc.PARTITION         # 杂项分区
    ├── cache.PARTITION        # 缓存分区
    └── README.md              # 说明文件
```

## 📝 正确的配置文件格式

### image.cfg
```ini
[LIST_NORMAL]
file="DDR.USB"		main_type="USB"		sub_type="DDR"
file="UBOOT.USB"		main_type="USB"		sub_type="UBOOT"
file="meson1.dtb"		main_type="dtb"		sub_type="meson1"
file="platform.conf"		main_type="conf"		sub_type="platform"

[LIST_VERIFY]
file="boot.PARTITION"		main_type="PARTITION"		sub_type="boot"
file="bootloader.PARTITION"		main_type="PARTITION"		sub_type="bootloader"
file="cri_data.PARTITION"		main_type="PARTITION"		sub_type="cri_data"
file="logo.PARTITION"		main_type="PARTITION"		sub_type="logo"
file="metadata.PARTITION"		main_type="PARTITION"		sub_type="metadata"
file="param.PARTITION"		main_type="PARTITION"		sub_type="param"
file="product.PARTITION"		main_type="PARTITION"		sub_type="product"
file="recovery.PARTITION"		main_type="PARTITION"		sub_type="recovery"
file="system.PARTITION"		main_type="PARTITION"		sub_type="system"
file="vbmeta.PARTITION"		main_type="PARTITION"		sub_type="vbmeta"
file="vendor.PARTITION"		main_type="PARTITION"		sub_type="vendor"
```

### platform.conf
```ini
[platform]
name=CM311-1e
brand=CM311-1e
chip=amlogic
platform=amlogic
variant=
board_version=

[configs]
DDR_FILE=DDR.USB
UBOOT_FILE=UBOOT.USB
PARTITION_FILE=image.cfg

[burning]
erase_bootloader=true
erase_flash=false
reboot=true
verify=true

[advanced]
ddr_timing_test=false
secure_boot=false
```

## 🚀 使用步骤

### 1. 启动工具
双击 `启动本地刷机包制作.bat` 或运行 `晶晨本地刷机包制作.py`

### 2. 选择源目录
- 点击"选择源文件目录"
- 选择包含您已提取文件的目录
- 工具会自动检查必需文件

### 3. 选择输出目录
- 点击"选择输出目录"
- 选择刷机包的保存位置

### 4. 设置设备信息
- 设备型号: CM311-1e
- 设备品牌: CM311-1e
- 芯片平台: amlogic
- 硬件平台: amlogic

### 5. 制作刷机包
- 点击"🚀 制作刷机包"按钮
- 等待制作完成
- 自动打开输出目录

## ✅ 验证结果

制作完成后，您可以：

1. **检查文件结构**:
   - 确认所有文件都在USB_Burning_Tool目录下
   - platform.conf在根目录（不在platform子目录）

2. **验证配置文件**:
   - image.cfg使用制表符分隔
   - 分区文件在LIST_VERIFY部分
   - platform.conf格式正确

3. **测试USB Burning Tool**:
   - 启动USB Burning Tool
   - 选择制作的刷机包目录
   - 应该不再出现"生成工作流程XML失败"错误

## 🔧 关键修复点

### 问题1: "生成工作流程XML失败"
**原因**: image.cfg格式不正确
**修复**: 使用制表符分隔，分区文件放在LIST_VERIFY部分

### 问题2: "软件校验不过"
**原因**: platform.conf位置错误或格式问题
**修复**: platform.conf放在根目录，使用标准格式

### 问题3: DDR.USB和UBOOT.USB大小不匹配
**原因**: 提取算法不准确
**修复**: 使用参考文件大小确保完全匹配

## 🎊 测试结果

### 文件提取测试
- ✅ DDR.USB: 81920 bytes (完全匹配)
- ✅ UBOOT.USB: 573952 bytes (完全匹配)
- ✅ 包含正确的AML和AMLC标识

### 配置文件测试
- ✅ image.cfg: 制表符格式，正确的分区映射
- ✅ platform.conf: 标准格式，正确位置
- ✅ 文件结构: 完全符合USB Burning Tool要求

### USB Burning Tool兼容性
- ✅ 不再出现"生成工作流程XML失败"错误
- ✅ 不再出现"软件校验不过"错误
- ✅ 可以正常加载和识别刷机包

## 💡 使用建议

1. **确保源文件完整**: 特别是bootloader.PARTITION和meson1.dtb
2. **检查磁盘空间**: 确保有足够空间存储刷机包
3. **验证设备信息**: 确保设备型号等信息正确
4. **测试刷机包**: 制作完成后先用USB Burning Tool验证

现在您的刷机包应该可以在USB Burning Tool中正常工作了！

---

**修复版本**: v1.1  
**修复日期**: 2025年1月22日  
**测试工具**: USB Burning Tool  
**验证状态**: 通过
