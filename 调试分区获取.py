#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试分区获取问题
Copyright (c) 2025 By.举个🌰
"""

import subprocess
import sys
import os

def debug_adb_commands():
    """调试ADB命令执行"""
    print("🔍 调试ADB分区获取命令")
    print("=" * 50)
    
    # 检查ADB连接状态
    print("1. 检查ADB连接状态...")
    try:
        result = subprocess.run("adb devices", shell=True, capture_output=True, text=True, timeout=5)
        print(f"   返回码: {result.returncode}")
        print(f"   输出: {result.stdout}")
        if result.stderr:
            print(f"   错误: {result.stderr}")
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    # 测试方法1: /dev/block/by-name/
    print("\n2. 测试方法1: /dev/block/by-name/")
    try:
        cmd = "adb shell ls -la /dev/block/by-name/ 2>/dev/null"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        print(f"   命令: {cmd}")
        print(f"   返回码: {result.returncode}")
        print(f"   输出长度: {len(result.stdout)} 字符")
        if result.stdout.strip():
            lines = result.stdout.strip().split('\n')
            print(f"   输出行数: {len(lines)}")
            print("   前5行:")
            for i, line in enumerate(lines[:5]):
                print(f"     {i+1}: {line}")
        else:
            print("   输出为空")
        if result.stderr:
            print(f"   错误: {result.stderr}")
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    # 测试方法2: 查找by-name目录
    print("\n3. 测试方法2: 查找by-name目录")
    try:
        cmd = "adb shell find /dev/block -name by-name -type d 2>/dev/null"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        print(f"   命令: {cmd}")
        print(f"   返回码: {result.returncode}")
        print(f"   输出: {result.stdout}")
        if result.stderr:
            print(f"   错误: {result.stderr}")
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    # 测试方法3: 直接查找常见分区
    print("\n4. 测试方法3: 直接查找常见分区")
    common_partitions = ['boot', 'system', 'recovery']
    
    for partition in common_partitions:
        print(f"\n   测试分区: {partition}")
        
        # 测试路径1: /dev/block/partition
        path1 = f"/dev/block/{partition}"
        try:
            cmd = f'adb shell "test -e {path1} && echo exists || echo notfound"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=3)
            print(f"     {path1}: {result.stdout.strip()}")
        except Exception as e:
            print(f"     {path1}: 异常 {str(e)}")
        
        # 测试路径2: /dev/partition
        path2 = f"/dev/{partition}"
        try:
            cmd = f'adb shell "test -e {path2} && echo exists || echo notfound"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=3)
            print(f"     {path2}: {result.stdout.strip()}")
        except Exception as e:
            print(f"     {path2}: 异常 {str(e)}")
    
    # 测试分区大小获取
    print("\n5. 测试分区大小获取")
    test_path = "/dev/block/boot"
    
    # 方法1: blockdev
    try:
        cmd = f'adb shell "blockdev --getsize64 {test_path} 2>/dev/null || echo 0"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
        print(f"   blockdev结果: {result.stdout.strip()}")
    except Exception as e:
        print(f"   blockdev异常: {str(e)}")
    
    # 方法2: /proc/partitions
    try:
        cmd = 'adb shell "cat /proc/partitions 2>/dev/null | grep boot"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
        print(f"   /proc/partitions结果: {result.stdout.strip()}")
    except Exception as e:
        print(f"   /proc/partitions异常: {str(e)}")

def debug_partition_parsing():
    """调试分区解析逻辑"""
    print("\n🔍 调试分区解析逻辑")
    print("=" * 50)
    
    # 模拟ls -la输出
    sample_output = """total 0
drwxr-xr-x 2 <USER> <GROUP> 460 Jan 22 00:52 .
drwxr-xr-x 4 <USER> <GROUP> 280 Jan 22 00:52 ..
lrwxrwxrwx 1 root root  21 Jan 22 00:52 boot -> /dev/block/mmcblk0p5
lrwxrwxrwx 1 root root  21 Jan 22 00:52 bootloader -> /dev/block/mmcblk0p2
lrwxrwxrwx 1 root root  21 Jan 22 00:52 recovery -> /dev/block/mmcblk0p6
lrwxrwxrwx 1 root root  21 Jan 22 00:52 system -> /dev/block/mmcblk0p7
lrwxrwxrwx 1 root root  21 Jan 22 00:52 vendor -> /dev/block/mmcblk0p8"""
    
    print("模拟解析ls -la输出:")
    print(sample_output)
    
    print("\n解析结果:")
    lines = sample_output.strip().split('\n')
    partitions = []
    
    for line in lines:
        if '->' in line and not line.startswith('total') and not line.startswith('d'):
            parts = line.split()
            if len(parts) >= 11:  # ls -la的标准输出格式
                partition_name = parts[8]  # 分区名称
                target_path = parts[10]    # 目标路径
                
                print(f"  发现分区: {partition_name} -> {target_path}")
                partitions.append({
                    'name': partition_name,
                    'path': target_path,
                    'size': 'Unknown'
                })
    
    print(f"\n总共解析出 {len(partitions)} 个分区")

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 ADB分区获取调试工具")
    print("Copyright (c) 2025 By.举个🌰")
    print("=" * 60)
    
    debug_adb_commands()
    debug_partition_parsing()
    
    print("\n" + "=" * 60)
    print("💡 调试建议:")
    print("1. 如果方法1失败，检查设备是否支持by-name目录")
    print("2. 如果方法2失败，可能需要root权限")
    print("3. 如果方法3失败，检查分区命名规则")
    print("4. 检查ADB连接是否稳定")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
