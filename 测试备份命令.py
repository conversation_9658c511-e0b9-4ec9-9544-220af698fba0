#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试备份命令
Copyright (c) 2025 By.举个🌰
"""

import sys
import os
import subprocess
import tempfile

def test_backup_commands():
    """测试各种备份命令"""
    print("🧪 测试备份命令")
    print("=" * 50)
    
    device_ip = "***************"
    device_port = "5555"
    adb_prefix = f"adb -s {device_ip}:{device_port}"
    
    # 测试分区
    test_partition = "/dev/block/mmcblk0p5"  # boot分区
    
    print(f"测试分区: {test_partition}")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"临时目录: {temp_dir}")
        
        # 方法1: adb pull
        print("\n🔍 测试方法1: adb pull")
        output_file1 = os.path.join(temp_dir, "boot_pull.img")
        cmd = f'{adb_prefix} pull {test_partition} "{output_file1}"'
        print(f"命令: {cmd}")
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
        print(f"返回码: {result.returncode}")
        print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
        
        if os.path.exists(output_file1):
            size = os.path.getsize(output_file1)
            print(f"✅ adb pull成功，文件大小: {size} bytes")
        else:
            print("❌ adb pull失败，文件不存在")
        
        # 方法2: dd命令
        print("\n🔍 测试方法2: dd命令")
        output_file2 = os.path.join(temp_dir, "boot_dd.img")
        cmd = f'{adb_prefix} shell "dd if={test_partition} bs=1M" > "{output_file2}"'
        print(f"命令: {cmd}")
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
        print(f"返回码: {result.returncode}")
        print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
        
        if os.path.exists(output_file2):
            size = os.path.getsize(output_file2)
            print(f"✅ dd命令成功，文件大小: {size} bytes")
        else:
            print("❌ dd命令失败，文件不存在")
        
        # 方法3: cat命令
        print("\n🔍 测试方法3: cat命令")
        output_file3 = os.path.join(temp_dir, "boot_cat.img")
        cmd = f'{adb_prefix} shell "cat {test_partition}" > "{output_file3}"'
        print(f"命令: {cmd}")
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
        print(f"返回码: {result.returncode}")
        print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
        
        if os.path.exists(output_file3):
            size = os.path.getsize(output_file3)
            print(f"✅ cat命令成功，文件大小: {size} bytes")
        else:
            print("❌ cat命令失败，文件不存在")
        
        # 方法4: adb exec-out
        print("\n🔍 测试方法4: adb exec-out")
        output_file4 = os.path.join(temp_dir, "boot_exec.img")
        cmd = f'{adb_prefix} exec-out "cat {test_partition}" > "{output_file4}"'
        print(f"命令: {cmd}")
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
        print(f"返回码: {result.returncode}")
        print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
        
        if os.path.exists(output_file4):
            size = os.path.getsize(output_file4)
            print(f"✅ adb exec-out成功，文件大小: {size} bytes")
        else:
            print("❌ adb exec-out失败，文件不存在")
        
        # 方法5: 改进的dd命令（使用管道）
        print("\n🔍 测试方法5: 改进的dd命令")
        output_file5 = os.path.join(temp_dir, "boot_dd_pipe.img")
        cmd = f'{adb_prefix} exec-out "dd if={test_partition} bs=1M 2>/dev/null" > "{output_file5}"'
        print(f"命令: {cmd}")
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
        print(f"返回码: {result.returncode}")
        print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
        
        if os.path.exists(output_file5):
            size = os.path.getsize(output_file5)
            print(f"✅ 改进dd命令成功，文件大小: {size} bytes")
        else:
            print("❌ 改进dd命令失败，文件不存在")
        
        # 总结
        print("\n📊 备份方法测试总结:")
        methods = [
            ("adb pull", output_file1),
            ("dd命令", output_file2),
            ("cat命令", output_file3),
            ("adb exec-out", output_file4),
            ("改进dd命令", output_file5)
        ]
        
        successful_methods = []
        for method_name, file_path in methods:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                if size > 0:
                    print(f"✅ {method_name}: {size} bytes")
                    successful_methods.append(method_name)
                else:
                    print(f"⚠️  {method_name}: 0 bytes")
            else:
                print(f"❌ {method_name}: 文件不存在")
        
        print(f"\n🎯 成功的方法: {len(successful_methods)}/5")
        if successful_methods:
            print(f"推荐使用: {successful_methods[0]}")
        else:
            print("⚠️  所有方法都失败了")

def test_partition_info():
    """测试分区信息获取"""
    print("\n🔍 测试分区信息获取")
    print("=" * 50)
    
    device_ip = "***************"
    device_port = "5555"
    adb_prefix = f"adb -s {device_ip}:{device_port}"
    
    # 获取分区大小
    test_partition = "/dev/block/mmcblk0p5"
    
    print(f"测试分区: {test_partition}")
    
    # 方法1: blockdev
    print("\n使用blockdev获取大小:")
    cmd = f'{adb_prefix} shell "blockdev --getsize64 {test_partition}"'
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
    
    if result.returncode == 0 and result.stdout.strip().isdigit():
        size_bytes = int(result.stdout.strip())
        size_mb = size_bytes / (1024 * 1024)
        print(f"分区大小: {size_bytes} bytes ({size_mb:.1f} MB)")
    else:
        print(f"获取失败: {result.stderr}")
    
    # 方法2: ls -l
    print("\n使用ls -l查看分区:")
    cmd = f'{adb_prefix} shell "ls -l {test_partition}"'
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
    
    if result.returncode == 0:
        print(f"ls -l输出: {result.stdout.strip()}")
    else:
        print(f"ls -l失败: {result.stderr}")

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 备份命令测试工具")
    print("Copyright (c) 2025 By.举个🌰")
    print("=" * 60)
    
    # 测试分区信息
    test_partition_info()
    
    # 测试备份命令
    test_backup_commands()
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
